<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统修复中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            text-align: center;
            color: white;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1600px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .panel h2 {
            color: #764ba2;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #764ba2;
            padding-bottom: 10px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .action-btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .action-btn.success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        }
        
        .action-btn.warning {
            background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
            color: #333;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #764ba2;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .console-panel {
            grid-column: 1 / -1;
            margin-top: 20px;
        }
        
        .console {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .results-panel {
            grid-column: 1 / -1;
        }
        
        .result-item {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .result-healthy {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        
        .result-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        
        .result-critical {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        
        .result-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-healthy { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-critical { background: #dc3545; }
        .status-unknown { background: #6c757d; }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
            margin: 10px 0;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #764ba2;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 OTA系统修复中心</h1>
        <p>统一的系统诊断、修复和维护平台</p>
    </div>
    
    <div class="container">
        <div class="dashboard">
            <!-- 快速操作面板 -->
            <div class="panel">
                <h2>🚀 快速操作</h2>
                <div class="quick-actions">
                    <button class="action-btn" onclick="initializeSystem()">
                        🔧 初始化系统
                    </button>
                    <button class="action-btn" onclick="runQuickDiagnostic()">
                        🔍 快速诊断
                    </button>
                    <button class="action-btn success" onclick="performAutoRepair()">
                        🛠️ 自动修复
                    </button>
                    <button class="action-btn warning" onclick="runFullDiagnostic()">
                        📊 完整诊断
                    </button>
                    <button class="action-btn" onclick="getSystemStatus()">
                        📈 系统状态
                    </button>
                    <button class="action-btn danger" onclick="emergencyReset()">
                        🚨 紧急重置
                    </button>
                </div>
                
                <div style="margin-top: 20px;">
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <span>自动修复:</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="autoRepairToggle" onchange="toggleAutoRepair()">
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>
            </div>
            
            <!-- 系统状态面板 -->
            <div class="panel">
                <h2>📊 系统状态</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="systemHealth">未知</div>
                        <div class="stat-label">系统健康度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="repairTools">0</div>
                        <div class="stat-label">修复工具</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="diagnosticTools">0</div>
                        <div class="stat-label">诊断工具</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="healthChecks">0</div>
                        <div class="stat-label">健康检查</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="repairHistory">0</div>
                        <div class="stat-label">修复历史</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="lastRepair">从未</div>
                        <div class="stat-label">最后修复</div>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h3 style="color: #764ba2; margin-bottom: 10px;">核心组件状态</h3>
                    <div id="componentStatus"></div>
                </div>
            </div>
        </div>
        
        <!-- 控制台面板 -->
        <div class="panel console-panel">
            <h2>📋 系统控制台</h2>
            <div class="console" id="console"></div>
            <div style="margin-top: 15px; text-align: right;">
                <button class="action-btn" onclick="clearConsole()">🧹 清空控制台</button>
                <button class="action-btn" onclick="exportLogs()">📤 导出日志</button>
            </div>
        </div>
        
        <!-- 结果面板 -->
        <div class="panel results-panel">
            <h2>📋 诊断与修复结果</h2>
            <div id="results"></div>
        </div>
    </div>

    <!-- 加载核心架构模块 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/central-state-manager.js"></script>
    <script src="js/core/state-manager-adapter.js"></script>
    <script src="js/core/system-repair-center.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    
    <!-- 加载基础服务 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    
    <!-- 加载现有修复工具（如果存在） -->
    <script src="js/comprehensive-button-fix.js" onerror="console.log('按钮修复工具未找到')"></script>
    <script src="js/responsible-person-fix.js" onerror="console.log('负责人修复工具未找到')"></script>
    <script src="js/button-diagnostics.js" onerror="console.log('按钮诊断工具未找到')"></script>
    
    <script>
        // 系统修复中心主控制器
        class RepairCenterController {
            constructor() {
                this.repairCenter = null;
                this.initialized = false;
                this.autoUpdateInterval = null;
            }
            
            async init() {
                try {
                    this.logToConsole('🚀 初始化系统修复中心控制器...', 'info');
                    
                    // 获取修复中心实例
                    this.repairCenter = window.getSystemRepairCenter();
                    if (!this.repairCenter) {
                        throw new Error('系统修复中心不可用');
                    }
                    
                    // 初始化修复中心
                    if (!this.repairCenter.initialized) {
                        this.repairCenter.init();
                    }
                    
                    this.initialized = true;
                    this.logToConsole('✅ 系统修复中心控制器初始化完成', 'success');
                    
                    // 更新界面
                    this.updateUI();
                    
                    // 启动自动更新
                    this.startAutoUpdate();
                    
                } catch (error) {
                    this.logToConsole(`❌ 初始化失败: ${error.message}`, 'error');
                }
            }
            
            updateUI() {
                if (!this.repairCenter || !this.repairCenter.initialized) return;
                
                try {
                    const status = this.repairCenter.getStatus();
                    
                    // 更新统计数据
                    document.getElementById('repairTools').textContent = status.repairTools;
                    document.getElementById('diagnosticTools').textContent = status.diagnosticTools;
                    document.getElementById('healthChecks').textContent = status.healthChecks;
                    document.getElementById('repairHistory').textContent = status.repairHistory;
                    
                    // 更新最后修复时间
                    const lastRepair = status.lastRepair ? 
                        new Date(status.lastRepair).toLocaleString() : '从未';
                    document.getElementById('lastRepair').textContent = lastRepair;
                    
                    // 更新自动修复开关
                    document.getElementById('autoRepairToggle').checked = status.autoRepairEnabled;
                    
                    // 更新组件状态
                    this.updateComponentStatus();
                    
                } catch (error) {
                    this.logToConsole(`⚠️ UI更新失败: ${error.message}`, 'warning');
                }
            }
            
            updateComponentStatus() {
                const statusDiv = document.getElementById('componentStatus');
                statusDiv.innerHTML = '';
                
                const components = [
                    { name: 'OTA命名空间', check: () => !!window.OTA },
                    { name: '依赖容器', check: () => !!(window.OTA && window.OTA.container) },
                    { name: '服务定位器', check: () => !!(window.OTA && window.OTA.serviceLocator) },
                    { name: '中央状态管理器', check: () => !!window.getCentralStateManager() },
                    { name: '状态管理器适配器', check: () => !!window.getStateManagerAdapter() },
                    { name: 'getService函数', check: () => typeof window.getService === 'function' }
                ];
                
                components.forEach(component => {
                    const status = component.check();
                    const statusItem = document.createElement('div');
                    statusItem.style.cssText = 'display: flex; align-items: center; margin: 5px 0;';
                    statusItem.innerHTML = `
                        <span class="status-indicator status-${status ? 'healthy' : 'critical'}"></span>
                        <span>${component.name}: ${status ? '正常' : '异常'}</span>
                    `;
                    statusDiv.appendChild(statusItem);
                });
            }
            
            startAutoUpdate() {
                // 每30秒自动更新一次状态
                this.autoUpdateInterval = setInterval(() => {
                    this.updateUI();
                }, 30000);
            }
            
            stopAutoUpdate() {
                if (this.autoUpdateInterval) {
                    clearInterval(this.autoUpdateInterval);
                    this.autoUpdateInterval = null;
                }
            }
            
            logToConsole(message, type = 'info') {
                const console = document.getElementById('console');
                const timestamp = new Date().toLocaleTimeString();
                const icons = {
                    'info': 'ℹ️',
                    'success': '✅',
                    'warning': '⚠️',
                    'error': '❌'
                };
                const icon = icons[type] || 'ℹ️';
                const line = `[${timestamp}] ${icon} ${message}\n`;
                console.textContent += line;
                console.scrollTop = console.scrollHeight;
            }
            
            displayResults(results, title) {
                const resultsDiv = document.getElementById('results');
                
                // 添加标题
                const titleDiv = document.createElement('div');
                titleDiv.innerHTML = `<h3 style="color: #764ba2; margin: 20px 0 10px 0;">${title}</h3>`;
                resultsDiv.appendChild(titleDiv);
                
                // 显示结果
                if (Array.isArray(results)) {
                    results.forEach(result => {
                        const resultDiv = document.createElement('div');
                        resultDiv.className = `result-item result-${result.type || 'info'}`;
                        resultDiv.innerHTML = result.message || result;
                        resultsDiv.appendChild(resultDiv);
                    });
                } else if (typeof results === 'object') {
                    Object.entries(results).forEach(([key, value]) => {
                        const resultDiv = document.createElement('div');
                        resultDiv.className = 'result-item result-info';
                        resultDiv.innerHTML = `<strong>${key}:</strong> ${JSON.stringify(value)}`;
                        resultsDiv.appendChild(resultDiv);
                    });
                } else {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result-item result-info';
                    resultDiv.innerHTML = results;
                    resultsDiv.appendChild(resultDiv);
                }
            }
        }
        
        // 创建全局控制器实例
        const repairController = new RepairCenterController();
        
        // 页面加载完成后自动初始化
        window.addEventListener('load', () => {
            setTimeout(() => {
                repairController.init();
            }, 1000);
        });
        
        // 全局函数定义
        async function initializeSystem() {
            repairController.logToConsole('🔧 手动初始化系统...', 'info');
            await repairController.init();
        }
        
        async function runQuickDiagnostic() {
            if (!repairController.repairCenter) {
                repairController.logToConsole('❌ 修复中心未初始化', 'error');
                return;
            }
            
            repairController.logToConsole('🔍 开始快速诊断...', 'info');
            try {
                // 运行系统健康诊断
                const healthResult = await repairController.repairCenter.diagnoseSystemHealth();
                repairController.logToConsole(`📊 系统健康: ${healthResult.status}`, 
                    healthResult.status === 'healthy' ? 'success' : 'warning');
                
                document.getElementById('systemHealth').textContent = 
                    healthResult.status === 'healthy' ? '100%' : '需检查';
                
                repairController.displayResults([{
                    type: healthResult.status === 'healthy' ? 'healthy' : 'warning',
                    message: `快速诊断完成 - 系统健康状态: ${healthResult.status}`
                }], '🔍 快速诊断结果');
                
            } catch (error) {
                repairController.logToConsole(`❌ 快速诊断失败: ${error.message}`, 'error');
            }
        }
        
        async function runFullDiagnostic() {
            if (!repairController.repairCenter) {
                repairController.logToConsole('❌ 修复中心未初始化', 'error');
                return;
            }
            
            repairController.logToConsole('📊 开始完整系统诊断...', 'info');
            try {
                const result = await repairController.repairCenter.runFullDiagnostic();
                repairController.logToConsole(`📊 诊断完成，健康度: ${result.healthScore}%`, 
                    result.overall === 'healthy' ? 'success' : 'warning');
                
                document.getElementById('systemHealth').textContent = result.healthScore + '%';
                
                // 显示详细结果
                const results = [];
                results.push({
                    type: result.overall === 'healthy' ? 'healthy' : 'warning',
                    message: `整体健康度: ${result.healthScore}% (${result.overall})`
                });
                
                Object.values(result.categories).forEach(category => {
                    results.push({
                        type: category.status === 'healthy' ? 'healthy' : 'warning',
                        message: `${category.name}: ${category.status} (${category.checks.length}项检查)`
                    });
                });
                
                if (result.issues.length > 0) {
                    results.push({
                        type: 'info',
                        message: `发现 ${result.issues.length} 个问题需要修复`
                    });
                }
                
                repairController.displayResults(results, '📊 完整诊断结果');
                
            } catch (error) {
                repairController.logToConsole(`❌ 完整诊断失败: ${error.message}`, 'error');
            }
        }
        
        async function performAutoRepair() {
            if (!repairController.repairCenter) {
                repairController.logToConsole('❌ 修复中心未初始化', 'error');
                return;
            }
            
            repairController.logToConsole('🛠️ 开始自动修复...', 'info');
            try {
                // 启用自动修复
                repairController.repairCenter.enableAutoRepair();
                
                const result = await repairController.repairCenter.performAutoRepair();
                const successRate = Math.round((result.fixedIssues / result.totalIssues) * 100);
                
                repairController.logToConsole(`🛠️ 自动修复完成，成功率: ${successRate}%`, 
                    successRate >= 80 ? 'success' : 'warning');
                
                // 显示修复结果
                const results = [];
                results.push({
                    type: successRate >= 80 ? 'healthy' : 'warning',
                    message: `修复统计: 总问题${result.totalIssues}个，已修复${result.fixedIssues}个，失败${result.failedFixes}个`
                });
                
                result.results.forEach(repair => {
                    results.push({
                        type: repair.success ? 'healthy' : 'critical',
                        message: `${repair.tool}: ${repair.message}`
                    });
                });
                
                repairController.displayResults(results, '🛠️ 自动修复结果');
                repairController.updateUI();
                
            } catch (error) {
                repairController.logToConsole(`❌ 自动修复失败: ${error.message}`, 'error');
            }
        }
        
        function getSystemStatus() {
            if (!repairController.repairCenter) {
                repairController.logToConsole('❌ 修复中心未初始化', 'error');
                return;
            }
            
            repairController.logToConsole('📈 获取系统状态...', 'info');
            try {
                const status = repairController.repairCenter.getStatus();
                repairController.logToConsole(`📈 系统状态: ${JSON.stringify(status, null, 2)}`, 'info');
                
                repairController.displayResults(status, '📈 系统状态详情');
                repairController.updateUI();
                
            } catch (error) {
                repairController.logToConsole(`❌ 获取状态失败: ${error.message}`, 'error');
            }
        }
        
        function toggleAutoRepair() {
            if (!repairController.repairCenter) {
                repairController.logToConsole('❌ 修复中心未初始化', 'error');
                return;
            }
            
            try {
                const checkbox = document.getElementById('autoRepairToggle');
                if (checkbox.checked) {
                    repairController.repairCenter.enableAutoRepair();
                    repairController.logToConsole('✅ 自动修复已启用', 'success');
                } else {
                    repairController.repairCenter.disableAutoRepair();
                    repairController.logToConsole('⚠️ 自动修复已禁用', 'warning');
                }
                repairController.updateUI();
            } catch (error) {
                repairController.logToConsole(`❌ 切换自动修复失败: ${error.message}`, 'error');
            }
        }
        
        function emergencyReset() {
            if (confirm('确定要执行紧急重置吗？这将重新加载页面并重新初始化所有系统。')) {
                repairController.logToConsole('🚨 执行紧急重置...', 'warning');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }
        
        function clearConsole() {
            document.getElementById('console').textContent = '';
            repairController.logToConsole('🧹 控制台已清空', 'info');
        }
        
        function exportLogs() {
            const logs = document.getElementById('console').textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ota-repair-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            repairController.logToConsole('📤 日志已导出', 'success');
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (repairController) {
                repairController.stopAutoUpdate();
            }
        });
    </script>
</body>
</html>
