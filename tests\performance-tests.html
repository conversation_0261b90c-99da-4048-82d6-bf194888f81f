<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能学习型格式预处理引擎 - 性能测试</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .test-header h1 {
            color: #4a5568;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-warning {
            background: #ed8936;
            color: white;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .test-card {
            background: rgba(247, 250, 252, 0.8);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #a0aec0;
        }

        .status-running {
            background: #4299e1;
            animation: pulse 1.5s ease-in-out infinite;
        }

        .status-success {
            background: #48bb78;
        }

        .status-warning {
            background: #ed8936;
        }

        .status-error {
            background: #f56565;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2b6cb0;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.85rem;
            color: #718096;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            transition: width 0.3s ease;
            width: 0%;
        }

        .test-log {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .log-entry {
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .log-info { color: #63b3ed; }
        .log-success { color: #68d391; }
        .log-warning { color: #fbb6ce; }
        .log-error { color: #fc8181; }

        .chart-container {
            position: relative;
            height: 200px;
            margin-top: 15px;
            background: white;
            border-radius: 8px;
            padding: 10px;
        }

        .summary-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .summary-item {
            text-align: center;
            padding: 20px;
            background: rgba(247, 250, 252, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .summary-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .value-excellent { color: #48bb78; }
        .value-good { color: #4299e1; }
        .value-warning { color: #ed8936; }
        .value-poor { color: #f56565; }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .metric-grid {
                grid-template-columns: 1fr;
            }
            
            .test-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>性能测试套件</h1>
            <p>智能学习型格式预处理引擎 - 压力测试和性能优化</p>
        </div>

        <div class="test-controls">
            <button class="btn btn-primary" onclick="runAllPerformanceTests()" id="run-all-btn">
                运行所有测试
            </button>
            <button class="btn btn-success" onclick="runMemoryTest()" id="memory-test-btn">
                内存测试
            </button>
            <button class="btn btn-success" onclick="runSpeedTest()" id="speed-test-btn">
                速度测试
            </button>
            <button class="btn btn-warning" onclick="runStressTest()" id="stress-test-btn">
                压力测试
            </button>
            <button class="btn btn-danger" onclick="stopAllTests()" id="stop-btn">
                停止测试
            </button>
            <button class="btn btn-secondary" onclick="clearResults()" id="clear-btn">
                清空结果
            </button>
        </div>

        <div class="test-grid">
            <!-- 内存使用测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="status-indicator" id="memory-status"></span>
                    内存使用测试
                </div>
                
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="memory-current">--</div>
                        <div class="metric-label">当前使用</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="memory-peak">--</div>
                        <div class="metric-label">峰值使用</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="memory-growth">--</div>
                        <div class="metric-label">增长率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="memory-efficiency">--</div>
                        <div class="metric-label">效率评分</div>
                    </div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="memory-progress"></div>
                </div>

                <div class="test-log" id="memory-log">
                    <div class="log-entry log-info">[INFO] 内存测试准备就绪</div>
                </div>
            </div>

            <!-- 响应速度测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="status-indicator" id="speed-status"></span>
                    响应速度测试
                </div>
                
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="speed-average">--</div>
                        <div class="metric-label">平均响应时间</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="speed-min">--</div>
                        <div class="metric-label">最快响应</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="speed-max">--</div>
                        <div class="metric-label">最慢响应</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="speed-throughput">--</div>
                        <div class="metric-label">吞吐量</div>
                    </div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="speed-progress"></div>
                </div>

                <div class="test-log" id="speed-log">
                    <div class="log-entry log-info">[INFO] 速度测试准备就绪</div>
                </div>
            </div>

            <!-- 缓存性能测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="status-indicator" id="cache-status"></span>
                    缓存性能测试
                </div>
                
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="cache-hit-rate">--</div>
                        <div class="metric-label">命中率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="cache-response-time">--</div>
                        <div class="metric-label">缓存响应时间</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="cache-size">--</div>
                        <div class="metric-label">缓存大小</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="cache-efficiency">--</div>
                        <div class="metric-label">缓存效率</div>
                    </div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="cache-progress"></div>
                </div>

                <div class="test-log" id="cache-log">
                    <div class="log-entry log-info">[INFO] 缓存测试准备就绪</div>
                </div>
            </div>

            <!-- 学习算法性能测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="status-indicator" id="learning-status"></span>
                    学习算法性能测试
                </div>
                
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="learning-accuracy">--</div>
                        <div class="metric-label">学习准确率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="learning-speed">--</div>
                        <div class="metric-label">学习速度</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="learning-rules">--</div>
                        <div class="metric-label">生成规则数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="learning-prediction">--</div>
                        <div class="metric-label">预测准确率</div>
                    </div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="learning-progress"></div>
                </div>

                <div class="test-log" id="learning-log">
                    <div class="log-entry log-info">[INFO] 学习算法测试准备就绪</div>
                </div>
            </div>
        </div>

        <!-- 测试总结 -->
        <div class="summary-section" id="test-summary" style="display: none;">
            <div class="summary-title">测试总结报告</div>
            
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value value-good" id="overall-score">--</div>
                    <div class="metric-label">综合评分</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="performance-grade">--</div>
                    <div class="metric-label">性能等级</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="optimization-suggestions">--</div>
                    <div class="metric-label">优化建议数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="test-duration">--</div>
                    <div class="metric-label">测试耗时</div>
                </div>
            </div>

            <div class="test-log" id="summary-log" style="margin-top: 20px;">
                <!-- 总结日志将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 引入被测试的模块 -->
    <script src="../js/logger.js"></script>
    <script src="../js/learning-engine/learning-config.js"></script>
    <script src="../js/learning-engine/learning-storage-manager.js"></script>
    <script src="../js/learning-engine/user-operation-learner.js"></script>
    <script src="../js/learning-engine/error-classification-system.js"></script>
    <script src="../js/learning-engine/pattern-matching-engine.js"></script>
    <script src="../js/learning-engine/manual-correction-interface.js"></script>
    <script src="../js/learning-engine/rule-generation-engine.js"></script>
    <script src="../js/learning-engine/learning-integration-manager.js"></script>
    <script src="../js/learning-engine/ui-correction-manager.js"></script>
    <script src="../js/learning-engine/data-persistence-manager.js"></script>
    <script src="../js/learning-engine/predictive-corrector.js"></script>
    <script src="../js/learning-engine/adaptive-prompt-optimizer.js"></script>
    <script src="../js/learning-engine/learning-effectiveness-evaluator.js"></script>
    <script src="../js/learning-engine/intelligent-cache-manager.js"></script>
    <script src="../js/learning-engine/performance-monitor.js"></script>
    <script src="../js/learning-engine/performance-optimizer.js"></script>

    <script src="performance-test-suite.js"></script>
</body>
</html>
