/**
 * 中央状态管理器测试脚本
 * 验证中央状态管理器的各项功能
 */

(function() {
    'use strict';

    // 测试结果收集器
    const testResults = {
        total: 0,
        passed: 0,
        failed: 0,
        results: []
    };

    // 添加测试结果
    function addTestResult(testName, success, message, details = null) {
        testResults.total++;
        if (success) {
            testResults.passed++;
        } else {
            testResults.failed++;
        }

        const result = {
            name: testName,
            success,
            message,
            details,
            timestamp: new Date().toISOString()
        };

        testResults.results.push(result);
        
        // 输出到控制台
        const icon = success ? '✅' : '❌';
        console.log(`${icon} ${testName}: ${message}`);
        if (details) {
            console.log('   详细信息:', details);
        }
    }

    // 测试中央状态管理器基本功能
    function testBasicFunctionality() {
        console.log('\n🔧 测试中央状态管理器基本功能...');

        try {
            const stateManager = window.getCentralStateManager();
            
            // 测试1: 检查状态管理器是否存在
            addTestResult('状态管理器存在性检查', !!stateManager,
                stateManager ? '中央状态管理器已正确加载' : '中央状态管理器未找到');

            if (!stateManager) return false;

            // 测试2: 初始化状态管理器
            stateManager.init();
            addTestResult('状态管理器初始化', stateManager.initialized,
                stateManager.initialized ? '状态管理器初始化成功' : '状态管理器初始化失败');

            // 测试3: 测试状态域注册
            const stats = stateManager.getStats();
            const expectedDomains = ['auth', 'system', 'order', 'ui', 'config', 'cache'];
            const hasAllDomains = expectedDomains.every(domain => stats.domains.includes(domain));
            addTestResult('状态域注册检查', hasAllDomains,
                `状态域注册${hasAllDomains ? '完整' : '不完整'}`, stats.domains);

            return true;

        } catch (error) {
            addTestResult('基本功能测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 测试状态读写功能
    function testStateOperations() {
        console.log('\n📝 测试状态读写功能...');

        try {
            const stateManager = window.getCentralStateManager();
            if (!stateManager || !stateManager.initialized) {
                addTestResult('状态操作测试', false, '状态管理器未初始化');
                return false;
            }

            // 测试1: 读取初始状态
            const initialAuthState = stateManager.getState('auth');
            addTestResult('初始状态读取', !!initialAuthState,
                initialAuthState ? '初始状态读取成功' : '初始状态读取失败', initialAuthState);

            // 测试2: 设置状态
            const testValue = 'test-user-' + Date.now();
            const setResult = stateManager.setState('auth', 'user.name', testValue);
            addTestResult('状态设置', setResult,
                setResult ? '状态设置成功' : '状态设置失败');

            // 测试3: 读取设置的状态
            const retrievedValue = stateManager.getState('auth', 'user.name');
            const readSuccess = retrievedValue === testValue;
            addTestResult('状态读取验证', readSuccess,
                readSuccess ? '状态读取验证成功' : '状态读取验证失败',
                { expected: testValue, actual: retrievedValue });

            // 测试4: 嵌套状态操作
            stateManager.setState('order', 'current.status', 'processing');
            const nestedValue = stateManager.getState('order', 'current.status');
            addTestResult('嵌套状态操作', nestedValue === 'processing',
                nestedValue === 'processing' ? '嵌套状态操作成功' : '嵌套状态操作失败');

            return true;

        } catch (error) {
            addTestResult('状态操作测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 测试状态订阅功能
    function testStateSubscription() {
        console.log('\n📡 测试状态订阅功能...');

        return new Promise((resolve) => {
            try {
                const stateManager = window.getCentralStateManager();
                if (!stateManager || !stateManager.initialized) {
                    addTestResult('状态订阅测试', false, '状态管理器未初始化');
                    resolve(false);
                    return;
                }

                let callbackExecuted = false;
                let receivedValue = null;

                // 测试1: 订阅状态变更
                const unsubscribe = stateManager.subscribe('ui', 'theme', (newValue, oldValue) => {
                    callbackExecuted = true;
                    receivedValue = newValue;
                });

                addTestResult('状态订阅注册', typeof unsubscribe === 'function',
                    typeof unsubscribe === 'function' ? '状态订阅注册成功' : '状态订阅注册失败');

                // 测试2: 触发状态变更
                const testTheme = 'dark-test-' + Date.now();
                stateManager.setState('ui', 'theme', testTheme);

                // 等待回调执行
                setTimeout(() => {
                    addTestResult('状态变更通知', callbackExecuted,
                        callbackExecuted ? '状态变更通知成功' : '状态变更通知失败');

                    addTestResult('回调参数验证', receivedValue === testTheme,
                        receivedValue === testTheme ? '回调参数验证成功' : '回调参数验证失败',
                        { expected: testTheme, received: receivedValue });

                    // 测试3: 取消订阅
                    unsubscribe();
                    callbackExecuted = false;
                    stateManager.setState('ui', 'theme', 'light');

                    setTimeout(() => {
                        addTestResult('取消订阅验证', !callbackExecuted,
                            !callbackExecuted ? '取消订阅验证成功' : '取消订阅验证失败');
                        resolve(true);
                    }, 100);
                }, 100);

            } catch (error) {
                addTestResult('状态订阅测试', false, `测试过程中出错: ${error.message}`);
                resolve(false);
            }
        });
    }

    // 测试状态持久化功能
    function testStatePersistence() {
        console.log('\n💾 测试状态持久化功能...');

        try {
            const stateManager = window.getCentralStateManager();
            if (!stateManager || !stateManager.initialized) {
                addTestResult('状态持久化测试', false, '状态管理器未初始化');
                return false;
            }

            // 测试1: 设置需要持久化的状态
            const testConfig = { testKey: 'testValue-' + Date.now() };
            stateManager.setState('config', 'testConfig', testConfig, { persist: true });

            // 测试2: 检查localStorage中是否存在
            const storageKey = 'ota-central-state-config';
            const storedData = localStorage.getItem(storageKey);
            const hasStoredData = !!storedData;
            addTestResult('状态持久化存储', hasStoredData,
                hasStoredData ? '状态持久化存储成功' : '状态持久化存储失败');

            if (hasStoredData) {
                try {
                    const parsedData = JSON.parse(storedData);
                    const hasTestConfig = parsedData.state && parsedData.state.testConfig;
                    addTestResult('持久化数据验证', hasTestConfig,
                        hasTestConfig ? '持久化数据验证成功' : '持久化数据验证失败');
                } catch (parseError) {
                    addTestResult('持久化数据解析', false, '持久化数据解析失败');
                }
            }

            // 测试3: 状态恢复
            stateManager.restoreState('config');
            const restoredValue = stateManager.getState('config', 'testConfig');
            const restoreSuccess = JSON.stringify(restoredValue) === JSON.stringify(testConfig);
            addTestResult('状态恢复验证', restoreSuccess,
                restoreSuccess ? '状态恢复验证成功' : '状态恢复验证失败');

            return true;

        } catch (error) {
            addTestResult('状态持久化测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 测试兼容性API
    function testCompatibilityAPI() {
        console.log('\n🔄 测试兼容性API...');

        try {
            // 测试1: 检查兼容性API是否存在
            const hasCompatAPI = window.OTA && window.OTA.state;
            addTestResult('兼容性API存在性', hasCompatAPI,
                hasCompatAPI ? '兼容性API已正确加载' : '兼容性API未找到');

            if (!hasCompatAPI) return false;

            const compatAPI = window.OTA.state;

            // 测试2: 测试兼容性get方法
            const testValue = compatAPI.get('auth.isLoggedIn');
            addTestResult('兼容性get方法', testValue !== undefined,
                testValue !== undefined ? '兼容性get方法工作正常' : '兼容性get方法失败');

            // 测试3: 测试兼容性set方法
            const setResult = compatAPI.set('ui.testFlag', true);
            addTestResult('兼容性set方法', setResult,
                setResult ? '兼容性set方法工作正常' : '兼容性set方法失败');

            // 测试4: 验证设置的值
            const retrievedValue = compatAPI.get('ui.testFlag');
            addTestResult('兼容性API数据一致性', retrievedValue === true,
                retrievedValue === true ? '兼容性API数据一致性验证成功' : '兼容性API数据一致性验证失败');

            return true;

        } catch (error) {
            addTestResult('兼容性API测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 运行所有测试
    async function runAllTests() {
        console.log('🚀 开始执行中央状态管理器测试...');
        console.log('测试时间:', new Date().toLocaleString());

        const startTime = performance.now();

        // 执行测试
        const basicOk = testBasicFunctionality();
        const operationsOk = testStateOperations();
        const subscriptionOk = await testStateSubscription();
        const persistenceOk = testStatePersistence();
        const compatibilityOk = testCompatibilityAPI();

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // 输出测试总结
        console.log('\n📊 测试总结:');
        console.log(`总测试数: ${testResults.total}`);
        console.log(`通过测试: ${testResults.passed}`);
        console.log(`失败测试: ${testResults.failed}`);
        console.log(`成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
        console.log(`测试耗时: ${duration}ms`);

        // 输出状态管理器统计信息
        const stateManager = window.getCentralStateManager();
        if (stateManager && stateManager.initialized) {
            const stats = stateManager.getStats();
            console.log('\n📈 状态管理器统计:');
            console.log(`状态域数量: ${stats.domains.length}`);
            console.log(`订阅者数量: ${stats.totalSubscribers}`);
            console.log(`历史记录数: ${stats.historySize}`);
            console.log(`中间件数量:`, stats.middleware);
        }

        // 返回测试结果
        return {
            success: testResults.failed === 0,
            stats: {
                total: testResults.total,
                passed: testResults.passed,
                failed: testResults.failed,
                successRate: Math.round((testResults.passed / testResults.total) * 100),
                duration
            },
            results: testResults.results
        };
    }

    // 导出测试函数到全局
    window.testCentralStateManager = {
        runAllTests,
        testBasicFunctionality,
        testStateOperations,
        testStateSubscription,
        testStatePersistence,
        testCompatibilityAPI,
        getResults: () => testResults
    };

    console.log('✅ 中央状态管理器测试脚本已加载');

})();
