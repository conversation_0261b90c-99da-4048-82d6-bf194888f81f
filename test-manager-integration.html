<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统 - Manager集成测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            color: #764ba2;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #764ba2;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        #console {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 OTA系统Manager集成测试</h1>
        
        <div class="test-section">
            <h2>📊 测试控制面板</h2>
            <button onclick="testManagerIntegration()">🚀 测试Manager集成</button>
            <button onclick="testStateManagerAdapter()">🔄 测试状态管理器适配器</button>
            <button onclick="testCentralStateManager()">📦 测试中央状态管理器</button>
            <button onclick="runFullIntegrationTest()">🎯 完整集成测试</button>
            <button onclick="clearConsole()">🧹 清空控制台</button>
        </div>

        <div class="stats" id="testStats">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 测试控制台</h2>
            <div id="console"></div>
        </div>

        <div class="test-section">
            <h2>🔍 系统状态</h2>
            <div id="systemStatus"></div>
        </div>
    </div>

    <!-- 加载核心架构模块 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/central-state-manager.js"></script>
    <script src="js/core/state-manager-adapter.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    
    <!-- 加载基础服务 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    
    <!-- 加载测试脚本 -->
    <script src="test-state-manager-integration.js"></script>
    
    <script>
        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 自定义控制台输出
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '✅';
            const line = `[${timestamp}] ${prefix} ${message}\n`;
            consoleDiv.textContent += line;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            originalLog(message);
        }
        
        console.log = logToConsole;
        console.error = (msg) => logToConsole(msg, 'error');
        console.warn = (msg) => logToConsole(msg, 'warn');
        
        function clearConsole() {
            consoleDiv.textContent = '';
            testStats = { total: 0, passed: 0, failed: 0 };
            updateStats();
        }
        
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            
            const successRate = testStats.total > 0 ? 
                Math.round((testStats.passed / testStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function addTestResult(name, success, message) {
            testStats.total++;
            if (success) {
                testStats.passed++;
            } else {
                testStats.failed++;
            }
            updateStats();
            
            const icon = success ? '✅' : '❌';
            logToConsole(`${icon} ${name}: ${message}`, success ? 'info' : 'error');
        }

        // 测试Manager集成
        function testManagerIntegration() {
            logToConsole('🔗 开始测试Manager集成...');
            
            try {
                // 测试1: 检查getAppState是否返回适配器
                const appState = window.getAppState();
                const isAdapter = appState && typeof appState.getMigrationReport === 'function';
                addTestResult('getAppState适配器检查', isAdapter, 
                    isAdapter ? 'getAppState返回适配器实例' : 'getAppState未返回适配器');

                // 测试2: 测试状态读写
                if (appState) {
                    const testValue = 'test-' + Date.now();
                    const setResult = appState.set('config.theme', testValue);
                    addTestResult('状态设置测试', setResult, 
                        setResult ? '状态设置成功' : '状态设置失败');

                    const getValue = appState.get('config.theme');
                    addTestResult('状态读取测试', getValue === testValue,
                        getValue === testValue ? '状态读取成功' : '状态读取失败');
                }

                // 测试3: 检查中央状态管理器
                const centralManager = window.getCentralStateManager();
                addTestResult('中央状态管理器检查', !!centralManager,
                    centralManager ? '中央状态管理器可用' : '中央状态管理器不可用');

                logToConsole('✅ Manager集成测试完成');

            } catch (error) {
                addTestResult('Manager集成测试', false, `测试失败: ${error.message}`);
            }
        }

        // 测试状态管理器适配器
        function testStateManagerAdapter() {
            logToConsole('🔄 开始测试状态管理器适配器...');
            
            if (window.testStateManagerIntegration) {
                window.testStateManagerIntegration.runAllTests().then(results => {
                    logToConsole(`📊 适配器测试完成: 成功率 ${results.stats.successRate}%`);
                    testStats.total += results.stats.total;
                    testStats.passed += results.stats.passed;
                    testStats.failed += results.stats.failed;
                    updateStats();
                });
            } else {
                addTestResult('适配器测试', false, '测试脚本未加载');
            }
        }

        // 测试中央状态管理器
        function testCentralStateManager() {
            logToConsole('📦 开始测试中央状态管理器...');
            
            try {
                const centralManager = window.getCentralStateManager();
                if (centralManager) {
                    // 初始化（如果未初始化）
                    if (!centralManager.initialized) {
                        centralManager.init();
                    }
                    
                    addTestResult('中央状态管理器初始化', centralManager.initialized,
                        centralManager.initialized ? '初始化成功' : '初始化失败');

                    // 测试状态操作
                    const testValue = 'central-test-' + Date.now();
                    const setResult = centralManager.setState('ui', 'theme', testValue);
                    addTestResult('中央状态设置', setResult, 
                        setResult ? '状态设置成功' : '状态设置失败');

                    const getValue = centralManager.getState('ui', 'theme');
                    addTestResult('中央状态读取', getValue === testValue,
                        getValue === testValue ? '状态读取成功' : '状态读取失败');

                    // 获取统计信息
                    const stats = centralManager.getStats();
                    logToConsole(`📈 中央状态管理器统计: 域数量=${stats.domains.length}, 订阅者=${stats.totalSubscribers}`);

                } else {
                    addTestResult('中央状态管理器测试', false, '中央状态管理器不可用');
                }

            } catch (error) {
                addTestResult('中央状态管理器测试', false, `测试失败: ${error.message}`);
            }
        }

        // 运行完整集成测试
        async function runFullIntegrationTest() {
            clearConsole();
            logToConsole('🎯 开始完整集成测试...');
            
            const startTime = performance.now();
            
            // 依次运行所有测试
            testManagerIntegration();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testCentralStateManager();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testStateManagerAdapter();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const endTime = performance.now();
            const duration = Math.round(endTime - startTime);
            
            logToConsole(`🏁 完整集成测试完成，耗时: ${duration}ms`);
            updateSystemStatus();
        }

        // 更新系统状态
        function updateSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = '';

            const checks = [
                { name: 'OTA命名空间', check: () => !!window.OTA },
                { name: '依赖容器', check: () => !!(window.OTA && window.OTA.container) },
                { name: '服务定位器', check: () => !!(window.OTA && window.OTA.serviceLocator) },
                { name: '中央状态管理器', check: () => !!window.getCentralStateManager() },
                { name: '状态管理器适配器', check: () => !!window.getStateManagerAdapter() },
                { name: 'getService函数', check: () => typeof window.getService === 'function' },
                { name: 'getAppState适配器', check: () => {
                    const appState = window.getAppState();
                    return appState && typeof appState.getMigrationReport === 'function';
                }}
            ];

            checks.forEach(({ name, check }) => {
                const result = check();
                const statusItem = document.createElement('div');
                statusItem.className = `test-result ${result ? 'success' : 'error'}`;
                statusItem.innerHTML = `<strong>${name}</strong>: ${result ? '✅ 正常' : '❌ 异常'}`;
                statusDiv.appendChild(statusItem);
            });
        }

        // 页面加载完成后自动检查系统状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                logToConsole('🔧 OTA系统Manager集成测试页面已加载');
                updateSystemStatus();
                logToConsole('点击按钮开始测试...');
            }, 1000);
        });
    </script>
</body>
</html>
