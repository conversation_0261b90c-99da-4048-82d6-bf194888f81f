/**
 * 智能学习型格式预处理引擎 - 错误分类系统
 * 负责识别、分类和分析用户更正中的错误模式
 * 为学习规则生成提供基础数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return window.OTA.learningConfig || window.learningConfig;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    function getUtils() {
        return window.OTA.utils || window.utils;
    }

    /**
     * 错误类型枚举
     * 定义系统支持的所有错误类型
     */
    const ErrorTypes = {
        // 时间相关错误
        TIME_FORMAT_ERROR: 'time_format_error',
        DATE_FORMAT_ERROR: 'date_format_error',
        DATETIME_PARSING_ERROR: 'datetime_parsing_error',
        
        // 姓名相关错误
        NAME_EXTRACTION_ERROR: 'name_extraction_error',
        NAME_FORMAT_ERROR: 'name_format_error',
        NAME_LANGUAGE_ERROR: 'name_language_error',
        
        // 位置相关错误
        LOCATION_PARSING_ERROR: 'location_parsing_error',
        LOCATION_AMBIGUITY_ERROR: 'location_ambiguity_error',
        LOCATION_FORMAT_ERROR: 'location_format_error',
        
        // 联系信息错误
        CONTACT_INFO_ERROR: 'contact_info_error',
        EMAIL_FORMAT_ERROR: 'email_format_error',
        PHONE_FORMAT_ERROR: 'phone_format_error',
        
        // 价格相关错误
        PRICE_CONVERSION_ERROR: 'price_conversion_error',
        CURRENCY_RECOGNITION_ERROR: 'currency_recognition_error',
        PRICE_FORMAT_ERROR: 'price_format_error',
        
        // 数值相关错误
        PASSENGER_COUNT_ERROR: 'passenger_count_error',
        LUGGAGE_COUNT_ERROR: 'luggage_count_error',
        NUMERIC_PARSING_ERROR: 'numeric_parsing_error',
        
        // 航班信息错误
        FLIGHT_INFO_ERROR: 'flight_info_error',
        FLIGHT_NUMBER_ERROR: 'flight_number_error',
        AIRLINE_RECOGNITION_ERROR: 'airline_recognition_error',
        
        // 服务类型错误
        SERVICE_TYPE_ERROR: 'service_type_error',
        CAR_TYPE_ERROR: 'car_type_error',
        SPECIAL_REQUEST_ERROR: 'special_request_error',
        
        // 通用错误
        UNKNOWN_ERROR: 'unknown_error',
        CONTEXT_ERROR: 'context_error',
        VALIDATION_ERROR: 'validation_error'
    };

    /**
     * 字段类型定义
     * 定义各个字段的类型和特征
     */
    const FieldTypes = {
        customerName: {
            type: 'text',
            category: 'customer_info',
            patterns: [/^[a-zA-Z\s\u4e00-\u9fff]+$/],
            commonErrors: [ErrorTypes.NAME_EXTRACTION_ERROR, ErrorTypes.NAME_FORMAT_ERROR]
        },
        customerContact: {
            type: 'contact',
            category: 'customer_info',
            patterns: [/^\+?[\d\s\-\(\)]+$/, /^[\w\.-]+@[\w\.-]+\.\w+$/],
            commonErrors: [ErrorTypes.CONTACT_INFO_ERROR, ErrorTypes.PHONE_FORMAT_ERROR]
        },
        customerEmail: {
            type: 'email',
            category: 'customer_info',
            patterns: [/^[\w\.-]+@[\w\.-]+\.\w+$/],
            commonErrors: [ErrorTypes.EMAIL_FORMAT_ERROR, ErrorTypes.CONTACT_INFO_ERROR]
        },
        pickup: {
            type: 'location',
            category: 'location',
            patterns: [/^[a-zA-Z0-9\s,\.\-\u4e00-\u9fff]+$/],
            commonErrors: [ErrorTypes.LOCATION_PARSING_ERROR, ErrorTypes.LOCATION_FORMAT_ERROR]
        },
        dropoff: {
            type: 'location',
            category: 'location',
            patterns: [/^[a-zA-Z0-9\s,\.\-\u4e00-\u9fff]+$/],
            commonErrors: [ErrorTypes.LOCATION_PARSING_ERROR, ErrorTypes.LOCATION_FORMAT_ERROR]
        },
        pickupDate: {
            type: 'date',
            category: 'datetime',
            patterns: [/^\d{4}-\d{2}-\d{2}$/, /^\d{2}\/\d{2}\/\d{4}$/, /^\d{2}-\d{2}-\d{4}$/],
            commonErrors: [ErrorTypes.DATE_FORMAT_ERROR, ErrorTypes.DATETIME_PARSING_ERROR]
        },
        pickupTime: {
            type: 'time',
            category: 'datetime',
            patterns: [/^\d{2}:\d{2}$/, /^\d{1,2}:\d{2}\s?(AM|PM)$/i],
            commonErrors: [ErrorTypes.TIME_FORMAT_ERROR, ErrorTypes.DATETIME_PARSING_ERROR]
        },
        passengerCount: {
            type: 'number',
            category: 'numeric',
            patterns: [/^\d+$/],
            commonErrors: [ErrorTypes.PASSENGER_COUNT_ERROR, ErrorTypes.NUMERIC_PARSING_ERROR]
        },
        luggageCount: {
            type: 'number',
            category: 'numeric',
            patterns: [/^\d+$/],
            commonErrors: [ErrorTypes.LUGGAGE_COUNT_ERROR, ErrorTypes.NUMERIC_PARSING_ERROR]
        },
        flightInfo: {
            type: 'flight',
            category: 'flight',
            patterns: [/^[A-Z]{2,3}\d{3,4}$/i, /^[A-Z]{2,3}\s?\d{3,4}$/i],
            commonErrors: [ErrorTypes.FLIGHT_INFO_ERROR, ErrorTypes.FLIGHT_NUMBER_ERROR]
        },
        otaPrice: {
            type: 'price',
            category: 'price',
            patterns: [/^\d+\.?\d*$/, /^[A-Z]{3}\s?\d+\.?\d*$/],
            commonErrors: [ErrorTypes.PRICE_FORMAT_ERROR, ErrorTypes.PRICE_CONVERSION_ERROR]
        }
    };

    /**
     * 错误分类系统类
     * 负责分析和分类用户更正中的错误
     */
    class ErrorClassificationSystem {
        constructor() {
            this.config = getLearningConfig();
            this.logger = getLogger();
            this.utils = getUtils();
            
            this.version = '1.0.0';
            this.errorTypes = ErrorTypes;
            this.fieldTypes = FieldTypes;
            
            // 分类配置
            this.classificationConfig = this.config.get('errorClassification');
            
            // 上下文分析器
            this.contextAnalyzer = new ContextAnalyzer();
            
            // 字段类型识别器
            this.fieldTypeRecognizer = new FieldTypeRecognizer(this.fieldTypes);
            
            // 错误模式缓存
            this.errorPatternCache = new Map();
            
            this.initialize();
        }

        /**
         * 初始化错误分类系统
         */
        initialize() {
            try {
                this.logger?.log('错误分类系统初始化完成', 'info', {
                    version: this.version,
                    supportedErrorTypes: Object.keys(this.errorTypes).length,
                    supportedFieldTypes: Object.keys(this.fieldTypes).length
                });
            } catch (error) {
                this.logger?.logError('错误分类系统初始化失败', error);
            }
        }

        /**
         * 分类错误
         * @param {Object} operationData - 用户操作数据
         * @returns {Object} 错误分类结果
         */
        classifyError(operationData) {
            try {
                const {
                    field,
                    originalValue,
                    correctedValue,
                    context = {}
                } = operationData;

                // 基础验证
                if (!field || originalValue === undefined || correctedValue === undefined) {
                    return this.createErrorResult(ErrorTypes.VALIDATION_ERROR, 0.1);
                }

                // 获取字段类型信息
                const fieldInfo = this.fieldTypeRecognizer.recognizeField(field);
                
                // 分析上下文
                const contextAnalysis = this.contextAnalyzer.analyzeContext(context, field);
                
                // 执行错误分类
                const classificationResult = this.performErrorClassification(
                    field,
                    originalValue,
                    correctedValue,
                    fieldInfo,
                    contextAnalysis
                );

                // 计算置信度
                const confidence = this.calculateClassificationConfidence(
                    classificationResult,
                    fieldInfo,
                    contextAnalysis
                );

                return {
                    errorType: classificationResult.errorType,
                    confidence: confidence,
                    fieldInfo: fieldInfo,
                    contextAnalysis: contextAnalysis,
                    details: classificationResult.details,
                    suggestions: this.generateSuggestions(classificationResult),
                    timestamp: new Date().toISOString()
                };

            } catch (error) {
                this.logger?.logError('错误分类失败', error);
                return this.createErrorResult(ErrorTypes.UNKNOWN_ERROR, 0.1);
            }
        }

        /**
         * 执行错误分类
         * @param {string} field - 字段名
         * @param {string} originalValue - 原始值
         * @param {string} correctedValue - 更正值
         * @param {Object} fieldInfo - 字段信息
         * @param {Object} contextAnalysis - 上下文分析
         * @returns {Object} 分类结果
         */
        performErrorClassification(field, originalValue, correctedValue, fieldInfo, contextAnalysis) {
            // 检查缓存
            const cacheKey = `${field}_${originalValue}_${correctedValue}`;
            if (this.errorPatternCache.has(cacheKey)) {
                return this.errorPatternCache.get(cacheKey);
            }

            let result = null;

            // 根据字段类型进行专门的错误分类
            switch (fieldInfo.category) {
                case 'datetime':
                    result = this.classifyDateTimeError(originalValue, correctedValue, fieldInfo);
                    break;
                case 'customer_info':
                    result = this.classifyCustomerInfoError(originalValue, correctedValue, fieldInfo);
                    break;
                case 'location':
                    result = this.classifyLocationError(originalValue, correctedValue, fieldInfo, contextAnalysis);
                    break;
                case 'numeric':
                    result = this.classifyNumericError(originalValue, correctedValue, fieldInfo);
                    break;
                case 'price':
                    result = this.classifyPriceError(originalValue, correctedValue, fieldInfo);
                    break;
                case 'flight':
                    result = this.classifyFlightError(originalValue, correctedValue, fieldInfo);
                    break;
                default:
                    result = this.classifyGenericError(originalValue, correctedValue, fieldInfo);
            }

            // 缓存结果
            this.errorPatternCache.set(cacheKey, result);
            
            return result;
        }

        /**
         * 分类日期时间错误
         */
        classifyDateTimeError(originalValue, correctedValue, fieldInfo) {
            if (fieldInfo.type === 'date') {
                // 检查日期格式错误
                if (!this.isValidDateFormat(originalValue) && this.isValidDateFormat(correctedValue)) {
                    return {
                        errorType: ErrorTypes.DATE_FORMAT_ERROR,
                        details: {
                            originalFormat: this.detectDateFormat(originalValue),
                            correctedFormat: this.detectDateFormat(correctedValue)
                        }
                    };
                }
            } else if (fieldInfo.type === 'time') {
                // 检查时间格式错误
                if (!this.isValidTimeFormat(originalValue) && this.isValidTimeFormat(correctedValue)) {
                    return {
                        errorType: ErrorTypes.TIME_FORMAT_ERROR,
                        details: {
                            originalFormat: this.detectTimeFormat(originalValue),
                            correctedFormat: this.detectTimeFormat(correctedValue)
                        }
                    };
                }
            }

            return {
                errorType: ErrorTypes.DATETIME_PARSING_ERROR,
                details: { reason: 'general_datetime_parsing_issue' }
            };
        }

        /**
         * 分类客户信息错误
         */
        classifyCustomerInfoError(originalValue, correctedValue, fieldInfo) {
            if (fieldInfo.type === 'email') {
                const emailRegex = /^[\w\.-]+@[\w\.-]+\.\w+$/;
                if (!emailRegex.test(originalValue) && emailRegex.test(correctedValue)) {
                    return {
                        errorType: ErrorTypes.EMAIL_FORMAT_ERROR,
                        details: { reason: 'invalid_email_format' }
                    };
                }
            } else if (fieldInfo.type === 'contact') {
                // 检查是否为电话号码格式错误
                const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
                if (!phoneRegex.test(originalValue) && phoneRegex.test(correctedValue)) {
                    return {
                        errorType: ErrorTypes.PHONE_FORMAT_ERROR,
                        details: { reason: 'invalid_phone_format' }
                    };
                }
            } else {
                // 姓名相关错误
                if (this.containsInvalidNameCharacters(originalValue)) {
                    return {
                        errorType: ErrorTypes.NAME_FORMAT_ERROR,
                        details: { reason: 'invalid_name_characters' }
                    };
                }
            }

            return {
                errorType: ErrorTypes.NAME_EXTRACTION_ERROR,
                details: { reason: 'name_extraction_issue' }
            };
        }

        /**
         * 分类位置错误
         */
        classifyLocationError(originalValue, correctedValue, fieldInfo, contextAnalysis) {
            // 检查位置格式
            if (this.isAmbiguousLocation(originalValue)) {
                return {
                    errorType: ErrorTypes.LOCATION_AMBIGUITY_ERROR,
                    details: { reason: 'ambiguous_location_reference' }
                };
            }

            // 检查位置解析
            if (this.isIncompleteLocation(originalValue) && this.isCompleteLocation(correctedValue)) {
                return {
                    errorType: ErrorTypes.LOCATION_PARSING_ERROR,
                    details: { reason: 'incomplete_location_parsing' }
                };
            }

            return {
                errorType: ErrorTypes.LOCATION_FORMAT_ERROR,
                details: { reason: 'location_format_issue' }
            };
        }

        /**
         * 分类数值错误
         */
        classifyNumericError(originalValue, correctedValue, fieldInfo) {
            const originalNum = parseInt(originalValue);
            const correctedNum = parseInt(correctedValue);

            if (isNaN(originalNum) && !isNaN(correctedNum)) {
                return {
                    errorType: ErrorTypes.NUMERIC_PARSING_ERROR,
                    details: { reason: 'non_numeric_value' }
                };
            }

            if (fieldInfo.field === 'passengerCount') {
                return {
                    errorType: ErrorTypes.PASSENGER_COUNT_ERROR,
                    details: { originalCount: originalNum, correctedCount: correctedNum }
                };
            } else if (fieldInfo.field === 'luggageCount') {
                return {
                    errorType: ErrorTypes.LUGGAGE_COUNT_ERROR,
                    details: { originalCount: originalNum, correctedCount: correctedNum }
                };
            }

            return {
                errorType: ErrorTypes.NUMERIC_PARSING_ERROR,
                details: { reason: 'general_numeric_issue' }
            };
        }

        /**
         * 分类价格错误
         */
        classifyPriceError(originalValue, correctedValue, fieldInfo) {
            // 检查货币识别错误
            const originalCurrency = this.extractCurrency(originalValue);
            const correctedCurrency = this.extractCurrency(correctedValue);

            if (originalCurrency !== correctedCurrency) {
                return {
                    errorType: ErrorTypes.CURRENCY_RECOGNITION_ERROR,
                    details: {
                        originalCurrency: originalCurrency,
                        correctedCurrency: correctedCurrency
                    }
                };
            }

            // 检查价格转换错误
            const originalAmount = this.extractAmount(originalValue);
            const correctedAmount = this.extractAmount(correctedValue);

            if (originalAmount !== correctedAmount) {
                return {
                    errorType: ErrorTypes.PRICE_CONVERSION_ERROR,
                    details: {
                        originalAmount: originalAmount,
                        correctedAmount: correctedAmount
                    }
                };
            }

            return {
                errorType: ErrorTypes.PRICE_FORMAT_ERROR,
                details: { reason: 'price_format_issue' }
            };
        }

        /**
         * 分类航班错误
         */
        classifyFlightError(originalValue, correctedValue, fieldInfo) {
            const flightRegex = /^([A-Z]{2,3})(\d{3,4})$/i;
            const originalMatch = originalValue.match(flightRegex);
            const correctedMatch = correctedValue.match(flightRegex);

            if (!originalMatch && correctedMatch) {
                return {
                    errorType: ErrorTypes.FLIGHT_NUMBER_ERROR,
                    details: { reason: 'invalid_flight_number_format' }
                };
            }

            if (originalMatch && correctedMatch) {
                if (originalMatch[1] !== correctedMatch[1]) {
                    return {
                        errorType: ErrorTypes.AIRLINE_RECOGNITION_ERROR,
                        details: {
                            originalAirline: originalMatch[1],
                            correctedAirline: correctedMatch[1]
                        }
                    };
                }
            }

            return {
                errorType: ErrorTypes.FLIGHT_INFO_ERROR,
                details: { reason: 'general_flight_info_issue' }
            };
        }

        /**
         * 分类通用错误
         */
        classifyGenericError(originalValue, correctedValue, fieldInfo) {
            return {
                errorType: ErrorTypes.UNKNOWN_ERROR,
                details: { reason: 'unclassified_error' }
            };
        }

        /**
         * 计算分类置信度
         */
        calculateClassificationConfidence(classificationResult, fieldInfo, contextAnalysis) {
            let confidence = 0.5; // 基础置信度

            // 基于错误类型调整置信度
            if (classificationResult.errorType !== ErrorTypes.UNKNOWN_ERROR) {
                confidence += 0.3;
            }

            // 基于字段匹配度调整
            if (fieldInfo.commonErrors.includes(classificationResult.errorType)) {
                confidence += 0.2;
            }

            // 基于上下文分析调整
            if (contextAnalysis.confidence > 0.7) {
                confidence += 0.1;
            }

            return Math.min(1.0, confidence);
        }

        /**
         * 生成改进建议
         */
        generateSuggestions(classificationResult) {
            const suggestions = [];

            switch (classificationResult.errorType) {
                case ErrorTypes.DATE_FORMAT_ERROR:
                    suggestions.push('建议使用标准日期格式 YYYY-MM-DD');
                    break;
                case ErrorTypes.TIME_FORMAT_ERROR:
                    suggestions.push('建议使用24小时制时间格式 HH:MM');
                    break;
                case ErrorTypes.EMAIL_FORMAT_ERROR:
                    suggestions.push('检查邮箱格式是否包含@符号和有效域名');
                    break;
                case ErrorTypes.LOCATION_PARSING_ERROR:
                    suggestions.push('提供更完整的地址信息');
                    break;
                default:
                    suggestions.push('建议检查输入格式和内容完整性');
            }

            return suggestions;
        }

        /**
         * 创建错误结果
         */
        createErrorResult(errorType, confidence) {
            return {
                errorType: errorType,
                confidence: confidence,
                fieldInfo: null,
                contextAnalysis: null,
                details: {},
                suggestions: [],
                timestamp: new Date().toISOString()
            };
        }

        // 辅助方法
        isValidDateFormat(value) {
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            return dateRegex.test(value);
        }

        isValidTimeFormat(value) {
            const timeRegex = /^\d{2}:\d{2}$/;
            return timeRegex.test(value);
        }

        detectDateFormat(value) {
            if (/^\d{4}-\d{2}-\d{2}$/.test(value)) return 'YYYY-MM-DD';
            if (/^\d{2}\/\d{2}\/\d{4}$/.test(value)) return 'MM/DD/YYYY';
            if (/^\d{2}-\d{2}-\d{4}$/.test(value)) return 'DD-MM-YYYY';
            return 'unknown';
        }

        detectTimeFormat(value) {
            if (/^\d{2}:\d{2}$/.test(value)) return '24-hour';
            if (/^\d{1,2}:\d{2}\s?(AM|PM)$/i.test(value)) return '12-hour';
            return 'unknown';
        }

        containsInvalidNameCharacters(value) {
            return /[^a-zA-Z\s\u4e00-\u9fff]/.test(value);
        }

        isAmbiguousLocation(value) {
            const ambiguousTerms = ['airport', 'hotel', 'station'];
            return ambiguousTerms.some(term => value.toLowerCase().includes(term));
        }

        isIncompleteLocation(value) {
            return value.length < 5 || !/[,\.]/.test(value);
        }

        isCompleteLocation(value) {
            return value.length > 10 && /[,\.]/.test(value);
        }

        extractCurrency(value) {
            const currencyMatch = value.match(/([A-Z]{3})/);
            return currencyMatch ? currencyMatch[1] : 'MYR';
        }

        extractAmount(value) {
            const amountMatch = value.match(/(\d+\.?\d*)/);
            return amountMatch ? parseFloat(amountMatch[1]) : 0;
        }
    }

    /**
     * 上下文分析器
     * 分析操作的上下文信息
     */
    class ContextAnalyzer {
        analyzeContext(context, field) {
            return {
                hasOrderText: !!context.orderText,
                hasAIResult: !!context.aiAnalysisResult,
                confidence: this.calculateContextConfidence(context),
                relevantInfo: this.extractRelevantInfo(context, field)
            };
        }

        calculateContextConfidence(context) {
            let confidence = 0.3;
            if (context.orderText) confidence += 0.3;
            if (context.aiAnalysisResult) confidence += 0.2;
            if (context.sessionId) confidence += 0.1;
            if (context.userAgent) confidence += 0.1;
            return Math.min(1.0, confidence);
        }

        extractRelevantInfo(context, field) {
            // 从上下文中提取与字段相关的信息
            return {
                field: field,
                hasContext: Object.keys(context).length > 0
            };
        }
    }

    /**
     * 字段类型识别器
     * 识别字段类型和特征
     */
    class FieldTypeRecognizer {
        constructor(fieldTypes) {
            this.fieldTypes = fieldTypes;
        }

        recognizeField(fieldName) {
            return this.fieldTypes[fieldName] || {
                type: 'unknown',
                category: 'general',
                patterns: [],
                commonErrors: [ErrorTypes.UNKNOWN_ERROR]
            };
        }
    }

    // 创建全局实例
    const errorClassificationSystem = new ErrorClassificationSystem();

    // 导出到全局命名空间
    window.OTA.errorClassificationSystem = errorClassificationSystem;
    window.OTA.ErrorTypes = ErrorTypes;
    window.OTA.FieldTypes = FieldTypes;
    
    // 向后兼容
    window.errorClassificationSystem = errorClassificationSystem;
    window.ErrorTypes = ErrorTypes;

    // 工厂函数
    window.getErrorClassificationSystem = function() {
        return window.OTA.errorClassificationSystem || window.errorClassificationSystem;
    };

    console.log('错误分类系统加载完成', {
        version: errorClassificationSystem.version,
        errorTypes: Object.keys(ErrorTypes).length,
        fieldTypes: Object.keys(FieldTypes).length
    });

})();
