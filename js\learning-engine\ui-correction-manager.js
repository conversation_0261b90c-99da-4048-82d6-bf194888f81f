/**
 * 智能学习型格式预处理引擎 - UI更正管理器
 * 负责在多订单面板中添加更正按钮、创建字段编辑模态框
 * 实现更正确认和取消、添加更正历史查看功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return getService('learningConfig');
    }

    function getCorrectionInterface() {
        return getService('correctionInterface');
    }

    function getLogger() {
        return getService('logger');
    }

    function getMultiOrderManager() {
        return getService('multiOrderManager');
    }

    // 服务定位器函数 - 使用统一的服务定位器
    function getService(serviceName) {
        // 优先使用全局服务定位器
        if (window.getService) {
            return window.getService(serviceName);
        }

        // 降级到旧的获取方式
        const serviceMap = {
            'learningConfig': () => window.OTA.learningConfig || window.learningConfig,
            'correctionInterface': () => window.OTA.correctionInterface || window.correctionInterface,
            'logger': () => window.OTA.logger || window.logger,
            'multiOrderManager': () => window.OTA.multiOrderManager || window.multiOrderManager
        };

        const getter = serviceMap[serviceName];
        return getter ? getter() : null;
    }

    /**
     * UI更正管理器类
     * 管理学习系统的用户界面更正功能
     */
    class UICorrectionManager {
        constructor() {
            this.config = getLearningConfig();
            this.correctionInterface = getCorrectionInterface();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            this.uiConfig = this.config.get('ui');
            
            // 当前编辑状态
            this.currentEditSession = null;
            
            // 模态框元素
            this.correctionModal = null;
            this.historyModal = null;
            
            this.initialize();
        }

        /**
         * 初始化UI更正管理器
         */
        initialize() {
            try {
                // 创建模态框
                this.createCorrectionModal();
                this.createHistoryModal();
                
                // 绑定全局事件
                this.bindGlobalEvents();
                
                this.logger?.log('UI更正管理器初始化完成', 'info', {
                    version: this.version,
                    uiEnabled: this.uiConfig.enabled
                });

            } catch (error) {
                this.logger?.logError('UI更正管理器初始化失败', error);
            }
        }

        /**
         * 增强多订单面板，添加更正功能
         * @param {HTMLElement} orderItem - 订单项元素
         * @param {Object} orderData - 订单数据
         * @param {number} orderIndex - 订单索引
         */
        enhanceOrderItem(orderItem, orderData, orderIndex) {
            try {
                if (!this.uiConfig.enabled) {
                    return;
                }

                // 添加学习系统更正按钮
                this.addCorrectionButton(orderItem, orderData, orderIndex);
                
                // 添加字段级别的更正功能
                this.addFieldCorrectionFeatures(orderItem, orderData, orderIndex);

            } catch (error) {
                this.logger?.logError('增强订单项失败', error);
            }
        }

        /**
         * 添加更正按钮
         */
        addCorrectionButton(orderItem, orderData, orderIndex) {
            const actionsContainer = orderItem.querySelector('.order-actions');
            if (!actionsContainer) {
                return;
            }

            // 创建学习系统更正按钮
            const correctionBtn = document.createElement('button');
            correctionBtn.type = 'button';
            correctionBtn.className = 'btn btn-sm btn-warning learning-correction-btn btn-mobile-optimized';
            correctionBtn.innerHTML = '🎓 学习';
            correctionBtn.title = '打开学习系统更正界面';
            correctionBtn.onclick = () => this.openCorrectionModal(orderData, orderIndex);

            // 插入到编辑按钮之后
            const editBtn = actionsContainer.querySelector('.quick-edit-btn');
            if (editBtn) {
                editBtn.parentNode.insertBefore(correctionBtn, editBtn.nextSibling);
            } else {
                actionsContainer.appendChild(correctionBtn);
            }

            // 添加历史查看按钮
            const historyBtn = document.createElement('button');
            historyBtn.type = 'button';
            historyBtn.className = 'btn btn-sm btn-info learning-history-btn btn-mobile-optimized';
            historyBtn.innerHTML = '📚 历史';
            historyBtn.title = '查看学习历史';
            historyBtn.onclick = () => this.openHistoryModal(orderIndex);

            correctionBtn.parentNode.insertBefore(historyBtn, correctionBtn.nextSibling);
        }

        /**
         * 添加字段级别的更正功能
         */
        addFieldCorrectionFeatures(orderItem, orderData, orderIndex) {
            // 为每个字段添加快速更正功能
            const summaryElement = orderItem.querySelector('.order-summary');
            if (!summaryElement) {
                return;
            }

            // 添加字段更正图标
            const fieldElements = summaryElement.querySelectorAll('[data-field]');
            fieldElements.forEach(fieldElement => {
                const fieldName = fieldElement.getAttribute('data-field');
                if (fieldName) {
                    this.addFieldCorrectionIcon(fieldElement, fieldName, orderData, orderIndex);
                }
            });
        }

        /**
         * 添加字段更正图标
         */
        addFieldCorrectionIcon(fieldElement, fieldName, orderData, orderIndex) {
            // 创建更正图标
            const correctionIcon = document.createElement('span');
            correctionIcon.className = 'field-correction-icon';
            correctionIcon.innerHTML = '✏️';
            correctionIcon.title = `更正 ${fieldName} 字段`;
            correctionIcon.style.cssText = `
                margin-left: 5px;
                cursor: pointer;
                opacity: 0.6;
                font-size: 12px;
            `;

            correctionIcon.onclick = (e) => {
                e.stopPropagation();
                this.openFieldCorrectionDialog(fieldName, orderData, orderIndex);
            };

            fieldElement.appendChild(correctionIcon);
        }

        /**
         * 打开更正模态框
         */
        openCorrectionModal(orderData, orderIndex) {
            try {
                this.currentEditSession = {
                    orderData: orderData,
                    orderIndex: orderIndex,
                    corrections: new Map()
                };

                // 填充模态框内容
                this.populateCorrectionModal(orderData);
                
                // 显示模态框
                this.correctionModal.style.display = 'flex';
                
                this.logger?.log('更正模态框已打开', 'info', { orderIndex: orderIndex });

            } catch (error) {
                this.logger?.logError('打开更正模态框失败', error);
            }
        }

        /**
         * 填充更正模态框内容
         */
        populateCorrectionModal(orderData) {
            const modalBody = this.correctionModal.querySelector('.modal-body');
            if (!modalBody) {
                return;
            }

            // 生成字段编辑表单
            const fieldsHTML = this.generateFieldEditForm(orderData);
            modalBody.innerHTML = fieldsHTML;

            // 绑定字段编辑事件
            this.bindFieldEditEvents();
        }

        /**
         * 生成字段编辑表单
         */
        generateFieldEditForm(orderData) {
            const fields = [
                { key: 'customerName', label: '客户姓名', type: 'text' },
                { key: 'customerContact', label: '联系方式', type: 'text' },
                { key: 'customerEmail', label: '邮箱', type: 'email' },
                { key: 'pickup', label: '接送地点', type: 'text' },
                { key: 'dropoff', label: '目的地', type: 'text' },
                { key: 'pickupDate', label: '接送日期', type: 'date' },
                { key: 'pickupTime', label: '接送时间', type: 'time' },
                { key: 'passengerCount', label: '乘客人数', type: 'number' },
                { key: 'luggageCount', label: '行李数量', type: 'number' },
                { key: 'flightInfo', label: '航班信息', type: 'text' },
                { key: 'otaPrice', label: 'OTA价格', type: 'text' }
            ];

            return `
                <div class="learning-correction-form">
                    <div class="form-header">
                        <h4>🎓 智能学习更正</h4>
                        <p class="text-muted">修改字段值以训练学习系统</p>
                    </div>
                    <div class="form-fields">
                        ${fields.map(field => this.generateFieldEditHTML(field, orderData)).join('')}
                    </div>
                    <div class="correction-summary">
                        <h5>更正摘要</h5>
                        <div id="correctionSummary" class="summary-content">
                            <p class="text-muted">尚未进行任何更正</p>
                        </div>
                    </div>
                </div>
            `;
        }

        /**
         * 生成字段编辑HTML
         */
        generateFieldEditHTML(field, orderData) {
            const currentValue = orderData[field.key] || '';
            const fieldId = `correction_${field.key}`;

            return `
                <div class="field-edit-group" data-field="${field.key}">
                    <label for="${fieldId}" class="field-label">
                        ${field.label}
                        <span class="field-status" id="${fieldId}_status"></span>
                    </label>
                    <div class="field-input-group">
                        <input type="${field.type}" 
                               id="${fieldId}" 
                               class="form-control field-correction-input" 
                               value="${currentValue}" 
                               data-original="${currentValue}"
                               data-field="${field.key}"
                               placeholder="输入${field.label}">
                        <button type="button" 
                                class="btn btn-sm btn-outline-secondary reset-field-btn" 
                                onclick="window.OTA.uiCorrectionManager.resetField('${field.key}')"
                                title="重置为原始值">
                            ↺
                        </button>
                    </div>
                    <div class="field-suggestions" id="${fieldId}_suggestions"></div>
                </div>
            `;
        }

        /**
         * 绑定字段编辑事件
         */
        bindFieldEditEvents() {
            const inputs = this.correctionModal.querySelectorAll('.field-correction-input');
            inputs.forEach(input => {
                input.addEventListener('input', (e) => {
                    this.handleFieldChange(e.target);
                });

                input.addEventListener('blur', (e) => {
                    this.validateFieldCorrection(e.target);
                });
            });
        }

        /**
         * 处理字段变化
         */
        handleFieldChange(input) {
            const fieldName = input.getAttribute('data-field');
            const originalValue = input.getAttribute('data-original');
            const currentValue = input.value;

            if (originalValue !== currentValue) {
                // 记录更正
                this.currentEditSession.corrections.set(fieldName, {
                    originalValue: originalValue,
                    correctedValue: currentValue,
                    timestamp: new Date().toISOString()
                });

                // 更新字段状态
                this.updateFieldStatus(fieldName, 'modified');
            } else {
                // 移除更正记录
                this.currentEditSession.corrections.delete(fieldName);
                this.updateFieldStatus(fieldName, 'original');
            }

            // 更新更正摘要
            this.updateCorrectionSummary();
        }

        /**
         * 更新字段状态
         */
        updateFieldStatus(fieldName, status) {
            const statusElement = this.correctionModal.querySelector(`#correction_${fieldName}_status`);
            if (!statusElement) {
                return;
            }

            switch (status) {
                case 'modified':
                    statusElement.innerHTML = '<span class="badge badge-warning">已修改</span>';
                    break;
                case 'original':
                    statusElement.innerHTML = '<span class="badge badge-secondary">原始值</span>';
                    break;
                case 'validated':
                    statusElement.innerHTML = '<span class="badge badge-success">已验证</span>';
                    break;
                case 'error':
                    statusElement.innerHTML = '<span class="badge badge-danger">错误</span>';
                    break;
                default:
                    statusElement.innerHTML = '';
            }
        }

        /**
         * 验证字段更正
         */
        validateFieldCorrection(input) {
            const fieldName = input.getAttribute('data-field');
            const value = input.value;

            // 基本验证
            if (this.isValidFieldValue(fieldName, value)) {
                this.updateFieldStatus(fieldName, 'validated');
                this.showFieldSuggestions(fieldName, []);
            } else {
                this.updateFieldStatus(fieldName, 'error');
                this.showFieldSuggestions(fieldName, ['请检查输入格式']);
            }
        }

        /**
         * 验证字段值
         */
        isValidFieldValue(fieldName, value) {
            if (!value) return true; // 空值允许

            switch (fieldName) {
                case 'customerEmail':
                    return /^[\w\.-]+@[\w\.-]+\.\w+$/.test(value);
                case 'pickupDate':
                    return /^\d{4}-\d{2}-\d{2}$/.test(value);
                case 'pickupTime':
                    return /^\d{2}:\d{2}$/.test(value);
                case 'passengerCount':
                case 'luggageCount':
                    return /^\d+$/.test(value) && parseInt(value) >= 0;
                default:
                    return true;
            }
        }

        /**
         * 显示字段建议
         */
        showFieldSuggestions(fieldName, suggestions) {
            const suggestionsElement = this.correctionModal.querySelector(`#correction_${fieldName}_suggestions`);
            if (!suggestionsElement) {
                return;
            }

            if (suggestions.length === 0) {
                suggestionsElement.innerHTML = '';
                return;
            }

            const suggestionsHTML = suggestions.map(suggestion => 
                `<div class="suggestion-item">${suggestion}</div>`
            ).join('');

            suggestionsElement.innerHTML = `<div class="suggestions-list">${suggestionsHTML}</div>`;
        }

        /**
         * 更新更正摘要
         */
        updateCorrectionSummary() {
            const summaryElement = this.correctionModal.querySelector('#correctionSummary');
            if (!summaryElement) {
                return;
            }

            const corrections = Array.from(this.currentEditSession.corrections.entries());
            
            if (corrections.length === 0) {
                summaryElement.innerHTML = '<p class="text-muted">尚未进行任何更正</p>';
                return;
            }

            const summaryHTML = corrections.map(([field, correction]) => `
                <div class="correction-item">
                    <strong>${field}:</strong>
                    <span class="original-value">"${correction.originalValue}"</span>
                    →
                    <span class="corrected-value">"${correction.correctedValue}"</span>
                </div>
            `).join('');

            summaryElement.innerHTML = summaryHTML;
        }

        /**
         * 重置字段
         */
        resetField(fieldName) {
            const input = this.correctionModal.querySelector(`#correction_${fieldName}`);
            if (!input) {
                return;
            }

            const originalValue = input.getAttribute('data-original');
            input.value = originalValue;

            // 移除更正记录
            this.currentEditSession.corrections.delete(fieldName);
            
            // 更新状态
            this.updateFieldStatus(fieldName, 'original');
            this.updateCorrectionSummary();
        }

        /**
         * 保存更正
         */
        async saveCorrections() {
            try {
                if (!this.currentEditSession || this.currentEditSession.corrections.size === 0) {
                    this.showNotification('没有需要保存的更正', 'warning');
                    return;
                }

                const corrections = Array.from(this.currentEditSession.corrections.entries());
                const results = [];

                // 处理每个更正
                for (const [field, correction] of corrections) {
                    const correctionData = {
                        field: field,
                        originalValue: correction.originalValue,
                        correctedValue: correction.correctedValue,
                        context: {
                            orderIndex: this.currentEditSession.orderIndex,
                            orderData: this.currentEditSession.orderData,
                            sessionId: Date.now()
                        },
                        confidence: 0.9, // 用户手动更正的置信度较高
                        metadata: {
                            source: 'ui_correction_manager',
                            timestamp: correction.timestamp
                        }
                    };

                    const result = this.correctionInterface.handleUserCorrection(correctionData);
                    results.push(result);
                }

                // 显示结果
                const successCount = results.filter(r => r.success).length;
                const totalCount = results.length;

                if (successCount === totalCount) {
                    this.showNotification(`成功保存 ${successCount} 个更正`, 'success');
                    this.closeCorrectionModal();
                } else {
                    this.showNotification(`保存了 ${successCount}/${totalCount} 个更正`, 'warning');
                }

                this.logger?.log('更正保存完成', 'info', {
                    total: totalCount,
                    successful: successCount
                });

            } catch (error) {
                this.logger?.logError('保存更正失败', error);
                this.showNotification('保存更正失败', 'error');
            }
        }

        /**
         * 打开字段更正对话框
         */
        openFieldCorrectionDialog(fieldName, orderData, orderIndex) {
            const currentValue = orderData[fieldName] || '';
            
            const newValue = prompt(`请输入 ${fieldName} 的正确值:`, currentValue);
            
            if (newValue !== null && newValue !== currentValue) {
                this.handleQuickCorrection(fieldName, currentValue, newValue, orderData, orderIndex);
            }
        }

        /**
         * 处理快速更正
         */
        async handleQuickCorrection(fieldName, originalValue, correctedValue, orderData, orderIndex) {
            try {
                const correctionData = {
                    field: fieldName,
                    originalValue: originalValue,
                    correctedValue: correctedValue,
                    context: {
                        orderIndex: orderIndex,
                        orderData: orderData,
                        correctionType: 'quick_correction'
                    },
                    confidence: 0.8,
                    metadata: {
                        source: 'quick_field_correction',
                        timestamp: new Date().toISOString()
                    }
                };

                const result = this.correctionInterface.handleUserCorrection(correctionData);
                
                if (result.success) {
                    this.showNotification(`${fieldName} 更正已保存`, 'success');
                } else {
                    this.showNotification(`${fieldName} 更正保存失败`, 'error');
                }

            } catch (error) {
                this.logger?.logError('快速更正失败', error);
                this.showNotification('更正保存失败', 'error');
            }
        }

        /**
         * 打开历史模态框
         */
        openHistoryModal(orderIndex) {
            try {
                // 获取更正历史
                const history = this.correctionInterface.getCorrectionHistory({
                    limit: 50
                });

                // 填充历史模态框
                this.populateHistoryModal(history);
                
                // 显示模态框
                this.historyModal.style.display = 'flex';

            } catch (error) {
                this.logger?.logError('打开历史模态框失败', error);
            }
        }

        /**
         * 填充历史模态框
         */
        populateHistoryModal(history) {
            const modalBody = this.historyModal.querySelector('.modal-body');
            if (!modalBody) {
                return;
            }

            if (history.length === 0) {
                modalBody.innerHTML = '<p class="text-muted">暂无更正历史</p>';
                return;
            }

            const historyHTML = history.map(entry => `
                <div class="history-item">
                    <div class="history-header">
                        <span class="field-name">${entry.field}</span>
                        <span class="timestamp">${new Date(entry.timestamp).toLocaleString()}</span>
                    </div>
                    <div class="history-content">
                        <div class="value-change">
                            <span class="original">"${entry.originalValue}"</span>
                            →
                            <span class="corrected">"${entry.correctedValue}"</span>
                        </div>
                        <div class="confidence">置信度: ${(entry.confidence * 100).toFixed(1)}%</div>
                    </div>
                </div>
            `).join('');

            modalBody.innerHTML = `<div class="history-list">${historyHTML}</div>`;
        }

        /**
         * 创建更正模态框
         */
        createCorrectionModal() {
            this.correctionModal = document.createElement('div');
            this.correctionModal.className = 'learning-modal correction-modal';
            this.correctionModal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>🎓 智能学习更正</h3>
                        <button type="button" class="close-btn" onclick="window.OTA.uiCorrectionManager.closeCorrectionModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <!-- 内容将动态填充 -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="window.OTA.uiCorrectionManager.closeCorrectionModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="window.OTA.uiCorrectionManager.saveCorrections()">保存更正</button>
                    </div>
                </div>
            `;

            document.body.appendChild(this.correctionModal);
        }

        /**
         * 创建历史模态框
         */
        createHistoryModal() {
            this.historyModal = document.createElement('div');
            this.historyModal.className = 'learning-modal history-modal';
            this.historyModal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>📚 更正历史</h3>
                        <button type="button" class="close-btn" onclick="window.OTA.uiCorrectionManager.closeHistoryModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <!-- 内容将动态填充 -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="window.OTA.uiCorrectionManager.closeHistoryModal()">关闭</button>
                    </div>
                </div>
            `;

            document.body.appendChild(this.historyModal);
        }

        /**
         * 关闭更正模态框
         */
        closeCorrectionModal() {
            if (this.correctionModal) {
                this.correctionModal.style.display = 'none';
                this.currentEditSession = null;
            }
        }

        /**
         * 关闭历史模态框
         */
        closeHistoryModal() {
            if (this.historyModal) {
                this.historyModal.style.display = 'none';
            }
        }

        /**
         * 绑定全局事件
         */
        bindGlobalEvents() {
            // ESC键关闭模态框
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.closeCorrectionModal();
                    this.closeHistoryModal();
                }
            });

            // 点击模态框外部关闭
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('learning-modal')) {
                    if (e.target === this.correctionModal) {
                        this.closeCorrectionModal();
                    } else if (e.target === this.historyModal) {
                        this.closeHistoryModal();
                    }
                }
            });
        }

        /**
         * 显示通知
         */
        showNotification(message, type = 'info') {
            // 使用现有的通知系统或创建简单通知
            if (window.OTA?.uiManager?.showNotification) {
                window.OTA.uiManager.showNotification(message, type);
            } else {
                alert(message);
            }
        }

        /**
         * 获取更正统计
         */
        getCorrectionStats() {
            return this.correctionInterface.getStats();
        }
    }

    // 创建全局实例
    const uiCorrectionManager = new UICorrectionManager();

    // 导出到全局命名空间
    window.OTA.uiCorrectionManager = uiCorrectionManager;
    window.uiCorrectionManager = uiCorrectionManager; // 向后兼容

    // 工厂函数
    window.getUICorrectionManager = function() {
        return window.OTA.uiCorrectionManager || window.uiCorrectionManager;
    };

    console.log('UI更正管理器加载完成', {
        version: uiCorrectionManager.version,
        uiEnabled: uiCorrectionManager.uiConfig.enabled
    });

})();
