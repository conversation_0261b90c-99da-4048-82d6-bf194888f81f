/**
 * 状态管理器集成测试脚本
 * 验证状态管理器适配器和中央状态管理器的集成功能
 */

(function() {
    'use strict';

    // 测试结果收集器
    const testResults = {
        total: 0,
        passed: 0,
        failed: 0,
        results: []
    };

    // 添加测试结果
    function addTestResult(testName, success, message, details = null) {
        testResults.total++;
        if (success) {
            testResults.passed++;
        } else {
            testResults.failed++;
        }

        const result = {
            name: testName,
            success,
            message,
            details,
            timestamp: new Date().toISOString()
        };

        testResults.results.push(result);
        
        // 输出到控制台
        const icon = success ? '✅' : '❌';
        console.log(`${icon} ${testName}: ${message}`);
        if (details) {
            console.log('   详细信息:', details);
        }
    }

    // 测试适配器基本功能
    function testAdapterBasics() {
        console.log('\n🔧 测试状态管理器适配器基本功能...');

        try {
            // 测试1: 检查适配器是否存在
            const adapter = window.getStateManagerAdapter();
            addTestResult('适配器存在性检查', !!adapter,
                adapter ? '状态管理器适配器已正确加载' : '状态管理器适配器未找到');

            if (!adapter) return false;

            // 测试2: 检查适配器是否已初始化
            addTestResult('适配器初始化检查', adapter.initialized,
                adapter.initialized ? '适配器已初始化' : '适配器未初始化');

            // 测试3: 检查路径映射
            const report = adapter.getMigrationReport();
            addTestResult('路径映射检查', report.pathMappings > 0,
                `路径映射数量: ${report.pathMappings}`, report);

            return true;

        } catch (error) {
            addTestResult('适配器基本功能测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 测试兼容性API
    function testCompatibilityAPI() {
        console.log('\n🔄 测试兼容性API...');

        try {
            // 测试1: 检查getAppState是否返回适配器
            const appState = window.getAppState();
            const isAdapter = appState && typeof appState.getMigrationReport === 'function';
            addTestResult('getAppState适配器检查', isAdapter,
                isAdapter ? 'getAppState返回适配器实例' : 'getAppState未返回适配器');

            if (!isAdapter) return false;

            // 测试2: 测试get方法
            const testValue = appState.get('auth.isLoggedIn');
            addTestResult('适配器get方法', testValue !== undefined,
                testValue !== undefined ? 'get方法工作正常' : 'get方法失败');

            // 测试3: 测试set方法
            const originalValue = appState.get('config.theme');
            const testTheme = 'test-theme-' + Date.now();
            const setResult = appState.set('config.theme', testTheme);
            addTestResult('适配器set方法', setResult,
                setResult ? 'set方法工作正常' : 'set方法失败');

            // 测试4: 验证设置的值
            const retrievedValue = appState.get('config.theme');
            addTestResult('适配器数据一致性', retrievedValue === testTheme,
                retrievedValue === testTheme ? '数据一致性验证成功' : '数据一致性验证失败');

            // 恢复原始值
            if (originalValue !== undefined) {
                appState.set('config.theme', originalValue);
            }

            return true;

        } catch (error) {
            addTestResult('兼容性API测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 测试状态监听功能
    function testStateListening() {
        console.log('\n📡 测试状态监听功能...');

        return new Promise((resolve) => {
            try {
                const appState = window.getAppState();
                if (!appState) {
                    addTestResult('状态监听测试', false, 'AppState适配器不可用');
                    resolve(false);
                    return;
                }

                let callbackExecuted = false;
                let receivedValue = null;

                // 测试1: 监听状态变更
                const unsubscribe = appState.on('config.debugMode', (newValue, oldValue) => {
                    callbackExecuted = true;
                    receivedValue = newValue;
                });

                addTestResult('状态监听注册', typeof unsubscribe === 'function',
                    typeof unsubscribe === 'function' ? '状态监听注册成功' : '状态监听注册失败');

                // 测试2: 触发状态变更
                const testValue = !appState.get('config.debugMode');
                appState.set('config.debugMode', testValue);

                // 等待回调执行
                setTimeout(() => {
                    addTestResult('状态变更通知', callbackExecuted,
                        callbackExecuted ? '状态变更通知成功' : '状态变更通知失败');

                    addTestResult('监听回调参数验证', receivedValue === testValue,
                        receivedValue === testValue ? '回调参数验证成功' : '回调参数验证失败',
                        { expected: testValue, received: receivedValue });

                    // 测试3: 取消监听
                    unsubscribe();
                    callbackExecuted = false;
                    appState.set('config.debugMode', !testValue);

                    setTimeout(() => {
                        addTestResult('取消监听验证', !callbackExecuted,
                            !callbackExecuted ? '取消监听验证成功' : '取消监听验证失败');
                        resolve(true);
                    }, 100);
                }, 100);

            } catch (error) {
                addTestResult('状态监听测试', false, `测试过程中出错: ${error.message}`);
                resolve(false);
            }
        });
    }

    // 测试路径映射
    function testPathMapping() {
        console.log('\n🗺️ 测试路径映射功能...');

        try {
            const appState = window.getAppState();
            if (!appState) {
                addTestResult('路径映射测试', false, 'AppState适配器不可用');
                return false;
            }

            // 测试常见路径映射
            const testCases = [
                { oldPath: 'auth.isLoggedIn', expectedDomain: 'auth' },
                { oldPath: 'systemData.backendUsers', expectedDomain: 'cache' },
                { oldPath: 'currentOrder.status', expectedDomain: 'order' },
                { oldPath: 'config.theme', expectedDomain: 'ui' },
                { oldPath: 'system.connected', expectedDomain: 'system' }
            ];

            let mappingSuccess = 0;
            testCases.forEach(({ oldPath, expectedDomain }) => {
                try {
                    // 设置一个测试值
                    const testValue = 'test-' + Date.now();
                    appState.set(oldPath, testValue);
                    
                    // 验证是否能正确读取
                    const retrievedValue = appState.get(oldPath);
                    if (retrievedValue === testValue) {
                        mappingSuccess++;
                    }
                } catch (error) {
                    console.warn(`路径映射测试失败: ${oldPath}`, error.message);
                }
            });

            const mappingRate = Math.round((mappingSuccess / testCases.length) * 100);
            addTestResult('路径映射功能', mappingSuccess > 0,
                `路径映射成功率: ${mappingRate}% (${mappingSuccess}/${testCases.length})`);

            return mappingSuccess > 0;

        } catch (error) {
            addTestResult('路径映射测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 测试迁移报告
    function testMigrationReport() {
        console.log('\n📊 测试迁移报告功能...');

        try {
            const adapter = window.getStateManagerAdapter();
            if (!adapter) {
                addTestResult('迁移报告测试', false, '适配器不可用');
                return false;
            }

            const report = adapter.getMigrationReport();
            addTestResult('迁移报告生成', !!report,
                report ? '迁移报告生成成功' : '迁移报告生成失败', report);

            if (report) {
                // 检查报告内容
                const hasRequiredFields = report.adapterInitialized !== undefined &&
                                        report.centralStateManagerAvailable !== undefined &&
                                        report.pathMappings !== undefined;
                
                addTestResult('迁移报告内容检查', hasRequiredFields,
                    hasRequiredFields ? '迁移报告内容完整' : '迁移报告内容不完整');

                // 显示迁移状态
                console.log('📈 迁移状态:');
                console.log(`  适配器已初始化: ${report.adapterInitialized ? '✅' : '❌'}`);
                console.log(`  中央状态管理器可用: ${report.centralStateManagerAvailable ? '✅' : '❌'}`);
                console.log(`  原始AppState可用: ${report.appStateAvailable ? '✅' : '❌'}`);
                console.log(`  路径映射数量: ${report.pathMappings}`);
                console.log(`  已发出警告数: ${report.warningsIssued}`);
            }

            return !!report;

        } catch (error) {
            addTestResult('迁移报告测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 运行所有测试
    async function runAllTests() {
        console.log('🚀 开始执行状态管理器集成测试...');
        console.log('测试时间:', new Date().toLocaleString());

        const startTime = performance.now();

        // 执行测试
        const adapterOk = testAdapterBasics();
        const compatibilityOk = testCompatibilityAPI();
        const listeningOk = await testStateListening();
        const mappingOk = testPathMapping();
        const reportOk = testMigrationReport();

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // 输出测试总结
        console.log('\n📊 测试总结:');
        console.log(`总测试数: ${testResults.total}`);
        console.log(`通过测试: ${testResults.passed}`);
        console.log(`失败测试: ${testResults.failed}`);
        console.log(`成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
        console.log(`测试耗时: ${duration}ms`);

        // 输出集成状态
        const adapter = window.getStateManagerAdapter();
        if (adapter) {
            const report = adapter.getMigrationReport();
            console.log('\n🔗 集成状态:');
            console.log(`适配器状态: ${report.adapterInitialized ? '✅ 正常' : '❌ 异常'}`);
            console.log(`中央状态管理器: ${report.centralStateManagerAvailable ? '✅ 可用' : '❌ 不可用'}`);
            console.log(`迁移建议数: ${report.recommendations.length}`);
        }

        // 返回测试结果
        return {
            success: testResults.failed === 0,
            stats: {
                total: testResults.total,
                passed: testResults.passed,
                failed: testResults.failed,
                successRate: Math.round((testResults.passed / testResults.total) * 100),
                duration
            },
            results: testResults.results
        };
    }

    // 导出测试函数到全局
    window.testStateManagerIntegration = {
        runAllTests,
        testAdapterBasics,
        testCompatibilityAPI,
        testStateListening,
        testPathMapping,
        testMigrationReport,
        getResults: () => testResults
    };

    console.log('✅ 状态管理器集成测试脚本已加载');

})();
