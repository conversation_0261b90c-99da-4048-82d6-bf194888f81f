<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统 - 依赖注入功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            color: #764ba2;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #764ba2;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OTA系统依赖注入功能测试</h1>
        
        <div class="test-section">
            <h2>📊 测试控制面板</h2>
            <button onclick="runAllTests()">🚀 运行所有测试</button>
            <button onclick="testServiceLocator()">🔍 测试服务定位器</button>
            <button onclick="testDependencyContainer()">📦 测试依赖容器</button>
            <button onclick="testServiceMigration()">🔄 测试服务迁移</button>
            <button onclick="clearResults()">🧹 清空结果</button>
        </div>

        <div class="stats" id="testStats">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 测试结果</h2>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>🔍 系统状态检查</h2>
            <div id="systemStatus"></div>
        </div>
    </div>

    <!-- 加载核心架构模块 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    
    <!-- 加载基础服务 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/api-service.js"></script>
    
    <script>
        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 添加测试结果
        function addTestResult(testName, success, message, details = null) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
            
            let content = `<strong>${testName}</strong>: ${message}`;
            if (details) {
                content += `<br><small>${JSON.stringify(details, null, 2)}</small>`;
            }
            resultDiv.innerHTML = content;
            
            resultsDiv.appendChild(resultDiv);
            
            // 更新统计
            testStats.total++;
            if (success) {
                testStats.passed++;
            } else {
                testStats.failed++;
            }
            updateStats();
        }

        // 更新统计显示
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            
            const successRate = testStats.total > 0 ? 
                Math.round((testStats.passed / testStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            testStats = { total: 0, passed: 0, failed: 0 };
            updateStats();
        }

        // 测试服务定位器
        function testServiceLocator() {
            try {
                // 测试1: 检查服务定位器是否存在
                const hasServiceLocator = window.OTA && window.OTA.serviceLocator;
                addTestResult('服务定位器存在性检查', hasServiceLocator, 
                    hasServiceLocator ? '服务定位器已正确加载' : '服务定位器未找到');

                if (!hasServiceLocator) return;

                // 测试2: 检查getService函数
                const hasGetService = typeof window.getService === 'function';
                addTestResult('getService函数检查', hasGetService,
                    hasGetService ? 'getService函数可用' : 'getService函数未找到');

                // 测试3: 测试服务获取
                if (hasGetService) {
                    const logger = window.getService('logger');
                    addTestResult('Logger服务获取', !!logger,
                        logger ? 'Logger服务获取成功' : 'Logger服务获取失败');

                    const appState = window.getService('appState');
                    addTestResult('AppState服务获取', !!appState,
                        appState ? 'AppState服务获取成功' : 'AppState服务获取失败');
                }

            } catch (error) {
                addTestResult('服务定位器测试', false, `测试过程中出错: ${error.message}`);
            }
        }

        // 测试依赖容器
        function testDependencyContainer() {
            try {
                // 测试1: 检查依赖容器是否存在
                const hasContainer = window.OTA && window.OTA.container;
                addTestResult('依赖容器存在性检查', hasContainer,
                    hasContainer ? '依赖容器已正确加载' : '依赖容器未找到');

                if (!hasContainer) return;

                const container = window.OTA.container;

                // 测试2: 检查容器方法
                const hasMethods = typeof container.register === 'function' && 
                                 typeof container.get === 'function' &&
                                 typeof container.has === 'function';
                addTestResult('容器方法检查', hasMethods,
                    hasMethods ? '容器方法完整' : '容器方法缺失');

                // 测试3: 测试服务注册和获取
                if (hasMethods) {
                    // 注册测试服务
                    container.register('testService', () => ({ test: true, timestamp: Date.now() }));
                    
                    const hasTestService = container.has('testService');
                    addTestResult('服务注册测试', hasTestService,
                        hasTestService ? '测试服务注册成功' : '测试服务注册失败');

                    if (hasTestService) {
                        const testService = container.get('testService');
                        addTestResult('服务获取测试', testService && testService.test,
                            testService ? '测试服务获取成功' : '测试服务获取失败', testService);
                    }
                }

            } catch (error) {
                addTestResult('依赖容器测试', false, `测试过程中出错: ${error.message}`);
            }
        }

        // 测试服务迁移状态
        function testServiceMigration() {
            try {
                const serviceLocator = window.OTA && window.OTA.serviceLocator;
                if (!serviceLocator) {
                    addTestResult('迁移状态测试', false, '服务定位器不可用');
                    return;
                }

                // 获取迁移报告
                if (typeof serviceLocator.getMigrationReport === 'function') {
                    const report = serviceLocator.getMigrationReport();
                    addTestResult('迁移报告生成', true, '迁移报告生成成功', report);
                    
                    // 检查迁移进度
                    const migrationProgress = report.containerServices / report.totalServices;
                    addTestResult('迁移进度检查', migrationProgress > 0.5,
                        `迁移进度: ${Math.round(migrationProgress * 100)}%`);
                } else {
                    addTestResult('迁移报告功能', false, '迁移报告功能不可用');
                }

            } catch (error) {
                addTestResult('服务迁移测试', false, `测试过程中出错: ${error.message}`);
            }
        }

        // 运行所有测试
        function runAllTests() {
            clearResults();
            
            addTestResult('测试开始', true, '开始执行依赖注入功能测试...');
            
            setTimeout(() => testServiceLocator(), 100);
            setTimeout(() => testDependencyContainer(), 200);
            setTimeout(() => testServiceMigration(), 300);
            setTimeout(() => checkSystemStatus(), 400);
        }

        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = '';

            const checks = [
                { name: 'OTA命名空间', check: () => !!window.OTA },
                { name: '依赖容器', check: () => !!(window.OTA && window.OTA.container) },
                { name: '服务定位器', check: () => !!(window.OTA && window.OTA.serviceLocator) },
                { name: 'getService函数', check: () => typeof window.getService === 'function' },
                { name: 'Logger服务', check: () => !!window.getService('logger') },
                { name: 'AppState服务', check: () => !!window.getService('appState') }
            ];

            checks.forEach(({ name, check }) => {
                const result = check();
                const statusItem = document.createElement('div');
                statusItem.className = `test-result ${result ? 'success' : 'error'}`;
                statusItem.innerHTML = `<strong>${name}</strong>: ${result ? '✅ 正常' : '❌ 异常'}`;
                statusDiv.appendChild(statusItem);
            });
        }

        // 页面加载完成后自动检查系统状态
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
