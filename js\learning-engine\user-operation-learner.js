/**
 * 智能学习型格式预处理引擎 - 用户操作学习器
 * 负责记录、分析和学习用户的操作行为
 * 扩展现有Logger.monitoring.userInteractions功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return window.OTA.learningConfig || window.learningConfig;
    }

    function getLearningStorageManager() {
        return window.OTA.learningStorageManager || window.learningStorageManager;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    function getUtils() {
        return window.OTA.utils || window.utils;
    }

    /**
     * 用户操作学习器类
     * 记录和分析用户的操作行为，为学习系统提供数据基础
     */
    class UserOperationLearner {
        constructor() {
            this.config = getLearningConfig();
            this.storageManager = getLearningStorageManager();
            this.logger = getLogger();
            this.utils = getUtils();
            
            this.version = '1.0.0';
            this.sessionId = this.generateSessionId();
            
            // 操作记录配置
            this.recordingConfig = this.config.get('userOperationLearning');
            
            // 内存缓存
            this.operationCache = new Map();
            this.maxCacheSize = 100;
            
            // 统计信息
            this.stats = {
                totalOperations: 0,
                sessionOperations: 0,
                lastOperation: null
            };

            // 初始化
            this.initialize();
        }

        /**
         * 初始化学习器
         */
        initialize() {
            try {
                // 加载现有统计信息
                this.loadStats();
                
                // 设置清理定时器
                this.setupCleanupTimer();
                
                this.logger?.log('用户操作学习器初始化完成', 'info', {
                    version: this.version,
                    sessionId: this.sessionId,
                    recordingEnabled: this.recordingConfig.enabled
                });
                
            } catch (error) {
                this.logger?.logError('用户操作学习器初始化失败', error);
            }
        }

        /**
         * 生成会话ID
         * @returns {string} 会话ID
         */
        generateSessionId() {
            return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 记录用户操作
         * @param {Object} operationData - 操作数据
         * @returns {string|null} 操作ID
         */
        recordOperation(operationData) {
            try {
                // 检查是否启用记录
                if (!this.recordingConfig.enabled) {
                    return null;
                }

                // 验证操作数据
                const validatedData = this.validateOperationData(operationData);
                if (!validatedData) {
                    this.logger?.log('操作数据验证失败', 'warn', operationData);
                    return null;
                }

                // 创建操作记录
                const operation = this.createOperationRecord(validatedData);
                
                // 存储到缓存
                this.operationCache.set(operation.id, operation);
                
                // 管理缓存大小
                this.manageCacheSize();
                
                // 持久化存储
                this.persistOperation(operation);
                
                // 更新统计信息
                this.updateStats(operation);
                
                // 触发学习分析
                this.triggerLearningAnalysis(operation);
                
                this.logger?.log('用户操作记录成功', 'info', {
                    operationId: operation.id,
                    type: operation.type,
                    field: operation.field
                });
                
                return operation.id;
                
            } catch (error) {
                this.logger?.logError('记录用户操作失败', error);
                return null;
            }
        }

        /**
         * 验证操作数据
         * @param {Object} data - 原始操作数据
         * @returns {Object|null} 验证后的数据
         */
        validateOperationData(data) {
            try {
                // 必需字段检查
                const requiredFields = ['type', 'field', 'originalValue', 'correctedValue'];
                for (const field of requiredFields) {
                    if (!data.hasOwnProperty(field)) {
                        this.logger?.log(`缺少必需字段: ${field}`, 'warn');
                        return null;
                    }
                }

                // 类型验证
                const validTypes = ['correction', 'validation', 'feedback'];
                if (!validTypes.includes(data.type)) {
                    this.logger?.log(`无效的操作类型: ${data.type}`, 'warn');
                    return null;
                }

                // 数据清理和标准化
                return {
                    type: data.type.toLowerCase().trim(),
                    field: data.field.trim(),
                    originalValue: this.sanitizeValue(data.originalValue),
                    correctedValue: this.sanitizeValue(data.correctedValue),
                    context: data.context || {},
                    confidence: this.validateConfidence(data.confidence),
                    metadata: data.metadata || {}
                };
                
            } catch (error) {
                this.logger?.logError('操作数据验证异常', error);
                return null;
            }
        }

        /**
         * 清理和标准化值
         * @param {*} value - 原始值
         * @returns {string} 清理后的值
         */
        sanitizeValue(value) {
            if (value === null || value === undefined) {
                return '';
            }
            
            return String(value).trim();
        }

        /**
         * 验证置信度值
         * @param {number} confidence - 置信度
         * @returns {number} 验证后的置信度
         */
        validateConfidence(confidence) {
            if (typeof confidence !== 'number' || isNaN(confidence)) {
                return 0.5; // 默认置信度
            }
            
            return Math.max(0, Math.min(1, confidence));
        }

        /**
         * 创建操作记录
         * @param {Object} validatedData - 验证后的数据
         * @returns {Object} 操作记录
         */
        createOperationRecord(validatedData) {
            const now = new Date().toISOString();
            const operationId = this.generateOperationId();
            
            return {
                id: operationId,
                timestamp: now,
                type: validatedData.type,
                field: validatedData.field,
                originalValue: validatedData.originalValue,
                correctedValue: validatedData.correctedValue,
                context: {
                    ...validatedData.context,
                    sessionId: this.sessionId,
                    userAgent: navigator.userAgent,
                    url: window.location.href
                },
                confidence: validatedData.confidence,
                metadata: {
                    ...validatedData.metadata,
                    source: 'user_operation_learner',
                    duration: 0, // 将在后续计算
                    category: this.categorizeOperation(validatedData)
                }
            };
        }

        /**
         * 生成操作ID
         * @returns {string} 操作ID
         */
        generateOperationId() {
            return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 对操作进行分类
         * @param {Object} data - 操作数据
         * @returns {string} 操作分类
         */
        categorizeOperation(data) {
            // 基于字段和操作类型进行分类
            const fieldCategories = {
                'customerName': 'customer_info',
                'customerContact': 'customer_info',
                'customerEmail': 'customer_info',
                'pickup': 'location',
                'dropoff': 'location',
                'pickupDate': 'datetime',
                'pickupTime': 'datetime',
                'passengerCount': 'numeric',
                'luggageCount': 'numeric',
                'flightInfo': 'flight',
                'otaPrice': 'price'
            };
            
            return fieldCategories[data.field] || 'general';
        }

        /**
         * 管理缓存大小
         */
        manageCacheSize() {
            if (this.operationCache.size > this.maxCacheSize) {
                // 删除最旧的记录
                const oldestKey = this.operationCache.keys().next().value;
                this.operationCache.delete(oldestKey);
            }
        }

        /**
         * 持久化操作记录
         * @param {Object} operation - 操作记录
         */
        persistOperation(operation) {
            try {
                const storageKey = this.config.get('storage.keys.userOperations');
                const existingData = this.storageManager.getData(storageKey) || {
                    version: '1.0.0',
                    operations: [],
                    metadata: { totalOperations: 0 }
                };
                
                // 添加新操作
                existingData.operations.push(operation);
                existingData.metadata.totalOperations++;
                existingData.metadata.lastOperation = operation.timestamp;
                
                // 限制存储的操作数量
                const maxRecords = this.recordingConfig.maxRecordsPerField;
                if (existingData.operations.length > maxRecords) {
                    existingData.operations = existingData.operations.slice(-maxRecords);
                }
                
                // 保存到存储
                this.storageManager.setData(storageKey, existingData);
                
            } catch (error) {
                this.logger?.logError('持久化操作记录失败', error);
            }
        }

        /**
         * 更新统计信息
         * @param {Object} operation - 操作记录
         */
        updateStats(operation) {
            this.stats.totalOperations++;
            this.stats.sessionOperations++;
            this.stats.lastOperation = operation.timestamp;
        }

        /**
         * 触发学习分析
         * @param {Object} operation - 操作记录
         */
        triggerLearningAnalysis(operation) {
            // 检查是否达到学习阈值
            const threshold = this.recordingConfig.recordThreshold;
            const fieldOperations = this.getOperationsByField(operation.field);
            
            if (fieldOperations.length >= threshold) {
                // 触发学习规则生成（将在后续任务中实现）
                this.logger?.log(`字段 ${operation.field} 达到学习阈值，触发分析`, 'info');
            }
        }

        /**
         * 根据字段获取操作记录
         * @param {string} field - 字段名
         * @returns {Array} 操作记录数组
         */
        getOperationsByField(field) {
            const operations = [];
            
            // 从缓存中获取
            for (const operation of this.operationCache.values()) {
                if (operation.field === field) {
                    operations.push(operation);
                }
            }
            
            return operations;
        }

        /**
         * 查询操作记录
         * @param {Object} criteria - 查询条件
         * @returns {Array} 匹配的操作记录
         */
        queryOperations(criteria = {}) {
            try {
                const storageKey = this.config.get('storage.keys.userOperations');
                const data = this.storageManager.getData(storageKey);
                
                if (!data || !data.operations) {
                    return [];
                }
                
                let results = data.operations;
                
                // 应用过滤条件
                if (criteria.field) {
                    results = results.filter(op => op.field === criteria.field);
                }
                
                if (criteria.type) {
                    results = results.filter(op => op.type === criteria.type);
                }
                
                if (criteria.dateFrom) {
                    results = results.filter(op => new Date(op.timestamp) >= new Date(criteria.dateFrom));
                }
                
                if (criteria.dateTo) {
                    results = results.filter(op => new Date(op.timestamp) <= new Date(criteria.dateTo));
                }
                
                // 排序（最新的在前）
                results.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                
                // 限制结果数量
                if (criteria.limit) {
                    results = results.slice(0, criteria.limit);
                }
                
                return results;
                
            } catch (error) {
                this.logger?.logError('查询操作记录失败', error);
                return [];
            }
        }

        /**
         * 加载统计信息
         */
        loadStats() {
            try {
                const storageKey = this.config.get('storage.keys.userOperations');
                const data = this.storageManager.getData(storageKey);
                
                if (data && data.metadata) {
                    this.stats.totalOperations = data.metadata.totalOperations || 0;
                    this.stats.lastOperation = data.metadata.lastOperation;
                }
            } catch (error) {
                this.logger?.logError('加载统计信息失败', error);
            }
        }

        /**
         * 设置清理定时器
         */
        setupCleanupTimer() {
            // 每小时清理一次过期数据
            setInterval(() => {
                this.cleanupExpiredData();
            }, 3600000); // 1小时
        }

        /**
         * 清理过期数据
         */
        cleanupExpiredData() {
            try {
                const retentionDays = this.config.get('storage.retentionDays');
                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
                
                const storageKey = this.config.get('storage.keys.userOperations');
                const data = this.storageManager.getData(storageKey);
                
                if (data && data.operations) {
                    const originalCount = data.operations.length;
                    data.operations = data.operations.filter(op => 
                        new Date(op.timestamp) > cutoffDate
                    );
                    
                    const removedCount = originalCount - data.operations.length;
                    if (removedCount > 0) {
                        this.storageManager.setData(storageKey, data);
                        this.logger?.log(`清理了 ${removedCount} 条过期操作记录`, 'info');
                    }
                }
                
            } catch (error) {
                this.logger?.logError('清理过期数据失败', error);
            }
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return { ...this.stats };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalOperations: 0,
                sessionOperations: 0,
                lastOperation: null
            };
        }
    }

    // 创建全局实例
    const userOperationLearner = new UserOperationLearner();

    // 导出到全局命名空间
    window.OTA.userOperationLearner = userOperationLearner;
    window.userOperationLearner = userOperationLearner; // 向后兼容

    // 工厂函数
    window.getUserOperationLearner = function() {
        return window.OTA.userOperationLearner || window.userOperationLearner;
    };

    console.log('用户操作学习器加载完成', {
        version: userOperationLearner.version,
        sessionId: userOperationLearner.sessionId,
        recordingEnabled: userOperationLearner.recordingConfig.enabled
    });

})();
