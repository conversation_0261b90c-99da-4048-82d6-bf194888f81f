/**
 * OTA系统综合功能测试套件
 * 对整个重构后的系统进行全面功能测试
 * 
 * 测试范围:
 * - 核心架构组件
 * - 依赖注入系统
 * - 状态管理系统
 * - 修复工具集成
 * - Manager功能
 * - 系统启动流程
 */

(function() {
    'use strict';

    /**
     * 综合系统测试类
     */
    class ComprehensiveSystemTest {
        constructor() {
            this.testResults = {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0,
                suites: []
            };
            this.currentSuite = null;
            this.logger = null;
        }

        /**
         * 运行所有测试
         */
        async runAllTests() {
            console.log('🚀 开始运行OTA系统综合功能测试...');
            console.log('测试时间:', new Date().toLocaleString());
            
            const startTime = performance.now();

            try {
                // 初始化测试环境
                await this.initializeTestEnvironment();

                // 运行测试套件
                await this.runCoreArchitectureTests();
                await this.runDependencyInjectionTests();
                await this.runStateManagementTests();
                await this.runRepairSystemTests();
                await this.runManagerIntegrationTests();
                await this.runSystemBootstrapTests();
                await this.runEndToEndTests();

                const endTime = performance.now();
                const duration = Math.round(endTime - startTime);

                // 生成测试报告
                this.generateTestReport(duration);

            } catch (error) {
                console.error('❌ 测试执行失败:', error.message);
                this.addTestResult('测试执行', false, `测试执行失败: ${error.message}`);
            }

            return this.testResults;
        }

        /**
         * 初始化测试环境
         */
        async initializeTestEnvironment() {
            this.startTestSuite('测试环境初始化');

            // 检查基础环境
            this.addTestResult('浏览器环境检查', typeof window !== 'undefined', 
                typeof window !== 'undefined' ? '浏览器环境正常' : '非浏览器环境');

            // 检查性能API
            this.addTestResult('性能API检查', typeof performance !== 'undefined',
                typeof performance !== 'undefined' ? '性能API可用' : '性能API不可用');

            // 检查本地存储
            this.addTestResult('本地存储检查', typeof localStorage !== 'undefined',
                typeof localStorage !== 'undefined' ? '本地存储可用' : '本地存储不可用');

            this.endTestSuite();
        }

        /**
         * 核心架构测试
         */
        async runCoreArchitectureTests() {
            this.startTestSuite('核心架构组件测试');

            // 测试OTA命名空间
            this.addTestResult('OTA命名空间', !!window.OTA,
                window.OTA ? 'OTA命名空间存在' : 'OTA命名空间不存在');

            // 测试依赖容器
            const container = window.OTA && window.OTA.container;
            this.addTestResult('依赖容器', !!container,
                container ? '依赖容器已加载' : '依赖容器未加载');

            if (container) {
                this.addTestResult('容器方法检查', 
                    typeof container.register === 'function' && typeof container.get === 'function',
                    '容器方法完整');
            }

            // 测试服务定位器
            const serviceLocator = window.OTA && window.OTA.serviceLocator;
            this.addTestResult('服务定位器', !!serviceLocator,
                serviceLocator ? '服务定位器已加载' : '服务定位器未加载');

            // 测试getService函数
            this.addTestResult('getService函数', typeof window.getService === 'function',
                typeof window.getService === 'function' ? 'getService函数可用' : 'getService函数不可用');

            // 测试应用启动协调器
            const bootstrap = window.OTA && window.OTA.ApplicationBootstrap;
            this.addTestResult('应用启动协调器', !!bootstrap,
                bootstrap ? '启动协调器已加载' : '启动协调器未加载');

            this.endTestSuite();
        }

        /**
         * 依赖注入系统测试
         */
        async runDependencyInjectionTests() {
            this.startTestSuite('依赖注入系统测试');

            try {
                // 测试服务注册
                if (window.OTA && window.OTA.container) {
                    const container = window.OTA.container;
                    
                    // 注册测试服务
                    container.register('testService', () => ({ test: true, timestamp: Date.now() }));
                    this.addTestResult('服务注册', container.has('testService'),
                        '测试服务注册成功');

                    // 测试服务获取
                    const service = container.get('testService');
                    this.addTestResult('服务获取', service && service.test === true,
                        '测试服务获取成功');

                    // 测试通过getService获取
                    if (window.getService) {
                        const serviceViaLocator = window.getService('testService');
                        this.addTestResult('服务定位器获取', serviceViaLocator && serviceViaLocator.test === true,
                            '通过服务定位器获取成功');
                    }
                }

                // 测试核心服务获取
                const coreServices = ['logger', 'appState'];
                for (const serviceName of coreServices) {
                    try {
                        const service = window.getService(serviceName);
                        this.addTestResult(`核心服务获取: ${serviceName}`, !!service,
                            service ? `${serviceName}服务获取成功` : `${serviceName}服务获取失败`);
                    } catch (error) {
                        this.addTestResult(`核心服务获取: ${serviceName}`, false,
                            `${serviceName}服务获取异常: ${error.message}`);
                    }
                }

            } catch (error) {
                this.addTestResult('依赖注入测试', false, `测试异常: ${error.message}`);
            }

            this.endTestSuite();
        }

        /**
         * 状态管理系统测试
         */
        async runStateManagementTests() {
            this.startTestSuite('状态管理系统测试');

            try {
                // 测试中央状态管理器
                const centralManager = window.getCentralStateManager();
                this.addTestResult('中央状态管理器获取', !!centralManager,
                    centralManager ? '中央状态管理器可用' : '中央状态管理器不可用');

                if (centralManager) {
                    // 初始化（如果需要）
                    if (!centralManager.initialized) {
                        centralManager.init();
                    }

                    this.addTestResult('中央状态管理器初始化', centralManager.initialized,
                        '中央状态管理器已初始化');

                    // 测试状态操作
                    const testValue = 'test-' + Date.now();
                    const setResult = centralManager.setState('ui', 'testValue', testValue);
                    this.addTestResult('状态设置', setResult, '状态设置成功');

                    const getValue = centralManager.getState('ui', 'testValue');
                    this.addTestResult('状态读取', getValue === testValue,
                        getValue === testValue ? '状态读取成功' : '状态读取失败');

                    // 测试状态订阅
                    let subscriptionTriggered = false;
                    const unsubscribe = centralManager.subscribe('ui', 'testSubscription', () => {
                        subscriptionTriggered = true;
                    });

                    centralManager.setState('ui', 'testSubscription', 'test');
                    
                    // 等待订阅触发
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    this.addTestResult('状态订阅', subscriptionTriggered, '状态订阅功能正常');
                    unsubscribe();
                }

                // 测试状态管理器适配器
                const adapter = window.getStateManagerAdapter();
                this.addTestResult('状态管理器适配器获取', !!adapter,
                    adapter ? '适配器可用' : '适配器不可用');

                if (adapter) {
                    if (!adapter.initialized) {
                        adapter.init();
                    }

                    this.addTestResult('适配器初始化', adapter.initialized, '适配器已初始化');
                }

                // 测试AppState兼容性
                const appState = window.getAppState();
                this.addTestResult('AppState兼容性', !!appState,
                    appState ? 'AppState兼容层可用' : 'AppState兼容层不可用');

                if (appState && typeof appState.getMigrationReport === 'function') {
                    const report = appState.getMigrationReport();
                    this.addTestResult('迁移报告', !!report, '迁移报告生成成功');
                }

            } catch (error) {
                this.addTestResult('状态管理测试', false, `测试异常: ${error.message}`);
            }

            this.endTestSuite();
        }

        /**
         * 修复系统测试
         */
        async runRepairSystemTests() {
            this.startTestSuite('修复系统测试');

            try {
                // 测试系统修复中心
                const repairCenter = window.getSystemRepairCenter();
                this.addTestResult('系统修复中心获取', !!repairCenter,
                    repairCenter ? '修复中心可用' : '修复中心不可用');

                if (repairCenter) {
                    // 初始化修复中心
                    if (!repairCenter.initialized) {
                        repairCenter.init();
                    }

                    this.addTestResult('修复中心初始化', repairCenter.initialized,
                        '修复中心已初始化');

                    // 测试修复中心状态
                    const status = repairCenter.getStatus();
                    this.addTestResult('修复中心状态', !!status,
                        `修复工具: ${status.repairTools}, 诊断工具: ${status.diagnosticTools}`);

                    // 测试诊断功能
                    const diagnostic = await repairCenter.runFullDiagnostic();
                    this.addTestResult('系统诊断', !!diagnostic,
                        `诊断完成，健康度: ${diagnostic.healthScore}%`);

                    // 测试便捷命令
                    if (window.repairSystem) {
                        const quickDiagnostic = await window.repairSystem({ diagnose: true });
                        this.addTestResult('便捷诊断命令', !!quickDiagnostic,
                            '便捷诊断命令工作正常');
                    }
                }

            } catch (error) {
                this.addTestResult('修复系统测试', false, `测试异常: ${error.message}`);
            }

            this.endTestSuite();
        }

        /**
         * Manager集成测试
         */
        async runManagerIntegrationTests() {
            this.startTestSuite('Manager集成测试');

            try {
                // 测试核心Manager
                const managers = [
                    'uiManager',
                    'multiOrderManager',
                    'orderHistoryManager',
                    'imageUploadManager',
                    'currencyConverter'
                ];

                for (const managerName of managers) {
                    const manager = window.OTA && window.OTA[managerName];
                    this.addTestResult(`${managerName}存在性`, !!manager,
                        manager ? `${managerName}已加载` : `${managerName}未加载`);
                }

                // 测试Manager工厂函数
                const factoryFunctions = [
                    'getAppState',
                    'getGeminiService',
                    'getAPIService',
                    'getImageUploadManager',
                    'getCurrencyConverter'
                ];

                for (const factoryName of factoryFunctions) {
                    const factory = window[factoryName];
                    this.addTestResult(`${factoryName}工厂函数`, typeof factory === 'function',
                        typeof factory === 'function' ? `${factoryName}工厂函数可用` : `${factoryName}工厂函数不可用`);
                }

            } catch (error) {
                this.addTestResult('Manager集成测试', false, `测试异常: ${error.message}`);
            }

            this.endTestSuite();
        }

        /**
         * 系统启动流程测试
         */
        async runSystemBootstrapTests() {
            this.startTestSuite('系统启动流程测试');

            try {
                // 测试启动协调器
                if (window.OTA && window.OTA.ApplicationBootstrap) {
                    const bootstrap = new window.OTA.ApplicationBootstrap();
                    this.addTestResult('启动协调器创建', !!bootstrap, '启动协调器创建成功');

                    // 测试启动流程（在测试环境中）
                    if (typeof bootstrap.start === 'function') {
                        try {
                            const result = await bootstrap.start();
                            this.addTestResult('启动流程执行', result.success,
                                result.success ? `启动成功，耗时: ${result.duration}ms` : `启动失败: ${result.error}`);
                        } catch (error) {
                            this.addTestResult('启动流程执行', false, `启动异常: ${error.message}`);
                        }
                    }
                }

                // 测试系统健康检查
                if (window.performSystemHealthCheck && typeof window.performSystemHealthCheck === 'function') {
                    try {
                        window.performSystemHealthCheck();
                        this.addTestResult('系统健康检查', true, '系统健康检查执行成功');
                    } catch (error) {
                        this.addTestResult('系统健康检查', false, `健康检查异常: ${error.message}`);
                    }
                }

            } catch (error) {
                this.addTestResult('系统启动测试', false, `测试异常: ${error.message}`);
            }

            this.endTestSuite();
        }

        /**
         * 端到端测试
         */
        async runEndToEndTests() {
            this.startTestSuite('端到端集成测试');

            try {
                // 测试完整的服务获取链路
                if (window.getService) {
                    const logger = window.getService('logger');
                    if (logger && typeof logger.log === 'function') {
                        logger.log('端到端测试日志', 'info');
                        this.addTestResult('服务链路测试', true, 'Logger服务链路正常');
                    } else {
                        this.addTestResult('服务链路测试', false, 'Logger服务链路异常');
                    }
                }

                // 测试状态管理链路
                const appState = window.getAppState();
                if (appState) {
                    const testValue = 'e2e-test-' + Date.now();
                    appState.set('test.e2e', testValue);
                    const retrievedValue = appState.get('test.e2e');
                    this.addTestResult('状态管理链路', retrievedValue === testValue,
                        '状态管理链路正常');
                }

                // 测试修复系统链路
                if (window.repairSystem) {
                    const repairResult = await window.repairSystem({ diagnose: true });
                    this.addTestResult('修复系统链路', !!repairResult,
                        '修复系统链路正常');
                }

                // 测试系统整体稳定性
                let stabilityScore = 0;
                const stabilityChecks = [
                    () => !!window.OTA,
                    () => typeof window.getService === 'function',
                    () => !!window.getAppState(),
                    () => !!window.getCentralStateManager(),
                    () => !!window.getSystemRepairCenter()
                ];

                stabilityChecks.forEach(check => {
                    try {
                        if (check()) stabilityScore++;
                    } catch (error) {
                        // 检查失败
                    }
                });

                const stabilityPercentage = Math.round((stabilityScore / stabilityChecks.length) * 100);
                this.addTestResult('系统稳定性', stabilityPercentage >= 80,
                    `系统稳定性: ${stabilityPercentage}%`);

            } catch (error) {
                this.addTestResult('端到端测试', false, `测试异常: ${error.message}`);
            }

            this.endTestSuite();
        }

        /**
         * 开始测试套件
         */
        startTestSuite(suiteName) {
            this.currentSuite = {
                name: suiteName,
                tests: [],
                startTime: performance.now()
            };
            console.log(`\n📋 开始测试套件: ${suiteName}`);
        }

        /**
         * 结束测试套件
         */
        endTestSuite() {
            if (this.currentSuite) {
                this.currentSuite.endTime = performance.now();
                this.currentSuite.duration = Math.round(this.currentSuite.endTime - this.currentSuite.startTime);
                this.testResults.suites.push(this.currentSuite);
                
                const passed = this.currentSuite.tests.filter(t => t.passed).length;
                const total = this.currentSuite.tests.length;
                console.log(`✅ 测试套件完成: ${this.currentSuite.name} (${passed}/${total} 通过, ${this.currentSuite.duration}ms)`);
                
                this.currentSuite = null;
            }
        }

        /**
         * 添加测试结果
         */
        addTestResult(testName, passed, message) {
            const result = {
                name: testName,
                passed: !!passed,
                message: message || '',
                timestamp: new Date().toISOString()
            };

            if (this.currentSuite) {
                this.currentSuite.tests.push(result);
            }

            this.testResults.total++;
            if (passed) {
                this.testResults.passed++;
            } else {
                this.testResults.failed++;
            }

            const icon = passed ? '✅' : '❌';
            console.log(`  ${icon} ${testName}: ${message}`);
        }

        /**
         * 生成测试报告
         */
        generateTestReport(duration) {
            const successRate = Math.round((this.testResults.passed / this.testResults.total) * 100);
            
            console.log('\n📊 OTA系统综合功能测试报告');
            console.log('='.repeat(50));
            console.log(`测试时间: ${new Date().toLocaleString()}`);
            console.log(`测试耗时: ${duration}ms`);
            console.log(`总测试数: ${this.testResults.total}`);
            console.log(`通过测试: ${this.testResults.passed}`);
            console.log(`失败测试: ${this.testResults.failed}`);
            console.log(`跳过测试: ${this.testResults.skipped}`);
            console.log(`成功率: ${successRate}%`);
            
            console.log('\n📋 测试套件详情:');
            this.testResults.suites.forEach(suite => {
                const suitePassed = suite.tests.filter(t => t.passed).length;
                const suiteTotal = suite.tests.length;
                const suiteRate = Math.round((suitePassed / suiteTotal) * 100);
                console.log(`  ${suite.name}: ${suitePassed}/${suiteTotal} (${suiteRate}%) - ${suite.duration}ms`);
            });

            // 显示失败的测试
            const failedTests = [];
            this.testResults.suites.forEach(suite => {
                suite.tests.forEach(test => {
                    if (!test.passed) {
                        failedTests.push(`${suite.name} > ${test.name}: ${test.message}`);
                    }
                });
            });

            if (failedTests.length > 0) {
                console.log('\n❌ 失败的测试:');
                failedTests.forEach(test => {
                    console.log(`  - ${test}`);
                });
            }

            console.log('\n🎯 测试结论:');
            if (successRate >= 95) {
                console.log('🎉 系统状态优秀！所有核心功能正常工作。');
            } else if (successRate >= 80) {
                console.log('✅ 系统状态良好，存在少量问题需要关注。');
            } else if (successRate >= 60) {
                console.log('⚠️ 系统状态一般，建议进行修复和优化。');
            } else {
                console.log('❌ 系统状态异常，需要立即进行修复。');
            }

            return this.testResults;
        }
    }

    // 导出测试类到全局
    window.ComprehensiveSystemTest = ComprehensiveSystemTest;

    // 提供便捷的测试运行函数
    window.runComprehensiveTest = async function() {
        const test = new ComprehensiveSystemTest();
        return await test.runAllTests();
    };

    console.log('✅ OTA系统综合功能测试套件已加载');

})();
