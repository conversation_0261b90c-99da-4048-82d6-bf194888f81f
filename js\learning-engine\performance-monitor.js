/**
 * 智能学习型格式预处理引擎 - 性能监控系统
 * 负责监控学习系统的性能指标、异常检测、报警机制
 * 集成到现有日志系统，提供详细的性能分析
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return window.OTA.learningConfig || window.learningConfig;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    /**
     * 性能监控器类
     * 监控学习系统的各项性能指标
     */
    class PerformanceMonitor {
        constructor() {
            this.config = getLearningConfig();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            
            // 监控配置
            this.monitorConfig = {
                enabled: true,
                sampleRate: 1.0,                    // 采样率
                metricsInterval: 60000,             // 指标收集间隔（1分钟）
                alertThresholds: {
                    responseTime: 5000,             // 响应时间阈值（5秒）
                    memoryUsage: 100 * 1024 * 1024, // 内存使用阈值（100MB）
                    errorRate: 0.1,                 // 错误率阈值（10%）
                    cacheHitRate: 0.7               // 缓存命中率阈值（70%）
                },
                retentionDays: 7                    // 数据保留天数
            };
            
            // 性能指标存储
            this.metrics = {
                responseTime: [],
                memoryUsage: [],
                operationCount: [],
                errorRate: [],
                cacheHitRate: [],
                learningEffectiveness: []
            };
            
            // 实时监控数据
            this.realTimeMetrics = {
                currentOperations: 0,
                totalOperations: 0,
                totalErrors: 0,
                averageResponseTime: 0,
                peakMemoryUsage: 0,
                lastUpdateTime: Date.now()
            };
            
            // 异常检测
            this.anomalyDetector = {
                enabled: true,
                thresholds: {
                    responseTimeSpike: 3,    // 响应时间突增倍数
                    memoryLeakRate: 0.1,     // 内存泄漏率
                    errorRateSpike: 5        // 错误率突增倍数
                },
                history: []
            };
            
            // 报警系统
            this.alertSystem = {
                enabled: true,
                cooldownPeriod: 300000,  // 5分钟冷却期
                lastAlerts: new Map(),
                handlers: []
            };

            this.initialize();
        }

        /**
         * 初始化性能监控器
         */
        initialize() {
            try {
                if (!this.monitorConfig.enabled) {
                    return;
                }

                // 设置定期指标收集
                this.setupMetricsCollection();
                
                // 设置异常检测
                this.setupAnomalyDetection();
                
                // 设置内存监控
                this.setupMemoryMonitoring();
                
                // 注册默认报警处理器
                this.registerDefaultAlertHandlers();
                
                // 恢复历史数据
                this.restoreHistoricalData();
                
                this.logger?.log('性能监控器初始化完成', 'info', {
                    version: this.version,
                    enabled: this.monitorConfig.enabled,
                    sampleRate: this.monitorConfig.sampleRate
                });

            } catch (error) {
                this.logger?.logError('性能监控器初始化失败', error);
            }
        }

        /**
         * 记录操作性能
         * @param {string} operation - 操作名称
         * @param {number} startTime - 开始时间
         * @param {Object} metadata - 元数据
         */
        recordOperation(operation, startTime, metadata = {}) {
            try {
                if (!this.shouldSample()) {
                    return;
                }

                const endTime = performance.now();
                const duration = endTime - startTime;
                
                // 记录响应时间
                this.recordResponseTime(operation, duration);
                
                // 更新实时指标
                this.updateRealTimeMetrics(operation, duration, metadata);
                
                // 检查性能阈值
                this.checkPerformanceThresholds(operation, duration, metadata);
                
                // 详细日志
                this.logger?.log('操作性能记录', 'debug', {
                    operation: operation,
                    duration: duration,
                    metadata: metadata
                });

            } catch (error) {
                this.logger?.logError('记录操作性能失败', error);
            }
        }

        /**
         * 记录错误
         * @param {string} operation - 操作名称
         * @param {Error} error - 错误对象
         * @param {Object} context - 上下文
         */
        recordError(operation, error, context = {}) {
            try {
                this.realTimeMetrics.totalErrors++;
                
                // 计算错误率
                const errorRate = this.realTimeMetrics.totalErrors / Math.max(this.realTimeMetrics.totalOperations, 1);
                
                // 记录错误率指标
                this.recordMetric('errorRate', errorRate);
                
                // 检查错误率阈值
                if (errorRate > this.monitorConfig.alertThresholds.errorRate) {
                    this.triggerAlert('high_error_rate', {
                        operation: operation,
                        errorRate: errorRate,
                        error: error.message,
                        context: context
                    });
                }
                
                // 异常检测
                this.detectErrorRateAnomaly(errorRate);
                
                this.logger?.logError('操作错误记录', error, {
                    operation: operation,
                    errorRate: errorRate,
                    context: context
                });

            } catch (monitorError) {
                this.logger?.logError('记录错误失败', monitorError);
            }
        }

        /**
         * 记录内存使用
         * @param {number} memoryUsage - 内存使用量（字节）
         */
        recordMemoryUsage(memoryUsage) {
            try {
                this.recordMetric('memoryUsage', memoryUsage);
                
                // 更新峰值内存使用
                if (memoryUsage > this.realTimeMetrics.peakMemoryUsage) {
                    this.realTimeMetrics.peakMemoryUsage = memoryUsage;
                }
                
                // 检查内存阈值
                if (memoryUsage > this.monitorConfig.alertThresholds.memoryUsage) {
                    this.triggerAlert('high_memory_usage', {
                        memoryUsage: memoryUsage,
                        threshold: this.monitorConfig.alertThresholds.memoryUsage,
                        percentage: (memoryUsage / this.monitorConfig.alertThresholds.memoryUsage * 100).toFixed(1) + '%'
                    });
                }
                
                // 内存泄漏检测
                this.detectMemoryLeak(memoryUsage);

            } catch (error) {
                this.logger?.logError('记录内存使用失败', error);
            }
        }

        /**
         * 记录缓存性能
         * @param {number} hitRate - 命中率
         * @param {Object} cacheStats - 缓存统计
         */
        recordCachePerformance(hitRate, cacheStats = {}) {
            try {
                this.recordMetric('cacheHitRate', hitRate);
                
                // 检查缓存命中率
                if (hitRate < this.monitorConfig.alertThresholds.cacheHitRate) {
                    this.triggerAlert('low_cache_hit_rate', {
                        hitRate: hitRate,
                        threshold: this.monitorConfig.alertThresholds.cacheHitRate,
                        cacheStats: cacheStats
                    });
                }

            } catch (error) {
                this.logger?.logError('记录缓存性能失败', error);
            }
        }

        /**
         * 记录学习效果
         * @param {number} effectiveness - 学习效果分数
         * @param {Object} details - 详细信息
         */
        recordLearningEffectiveness(effectiveness, details = {}) {
            try {
                this.recordMetric('learningEffectiveness', effectiveness);
                
                this.logger?.log('学习效果记录', 'info', {
                    effectiveness: effectiveness,
                    details: details
                });

            } catch (error) {
                this.logger?.logError('记录学习效果失败', error);
            }
        }

        /**
         * 记录响应时间
         * @param {string} operation - 操作名称
         * @param {number} duration - 持续时间
         */
        recordResponseTime(operation, duration) {
            this.recordMetric('responseTime', duration, { operation: operation });
            
            // 更新平均响应时间
            const responseTimeMetrics = this.metrics.responseTime;
            if (responseTimeMetrics.length > 0) {
                const totalTime = responseTimeMetrics.reduce((sum, metric) => sum + metric.value, 0);
                this.realTimeMetrics.averageResponseTime = totalTime / responseTimeMetrics.length;
            }
            
            // 检查响应时间阈值
            if (duration > this.monitorConfig.alertThresholds.responseTime) {
                this.triggerAlert('slow_response_time', {
                    operation: operation,
                    duration: duration,
                    threshold: this.monitorConfig.alertThresholds.responseTime
                });
            }
            
            // 响应时间异常检测
            this.detectResponseTimeAnomaly(duration);
        }

        /**
         * 记录指标
         * @param {string} metricType - 指标类型
         * @param {number} value - 值
         * @param {Object} metadata - 元数据
         */
        recordMetric(metricType, value, metadata = {}) {
            if (!this.metrics[metricType]) {
                this.metrics[metricType] = [];
            }
            
            const metric = {
                timestamp: Date.now(),
                value: value,
                metadata: metadata
            };
            
            this.metrics[metricType].push(metric);
            
            // 限制历史数据量
            this.limitMetricHistory(metricType);
        }

        /**
         * 更新实时指标
         * @param {string} operation - 操作名称
         * @param {number} duration - 持续时间
         * @param {Object} metadata - 元数据
         */
        updateRealTimeMetrics(operation, duration, metadata) {
            this.realTimeMetrics.totalOperations++;
            this.realTimeMetrics.lastUpdateTime = Date.now();
            
            // 如果是正在进行的操作
            if (metadata.status === 'started') {
                this.realTimeMetrics.currentOperations++;
            } else if (metadata.status === 'completed' || metadata.status === 'failed') {
                this.realTimeMetrics.currentOperations = Math.max(0, this.realTimeMetrics.currentOperations - 1);
            }
        }

        /**
         * 检查性能阈值
         * @param {string} operation - 操作名称
         * @param {number} duration - 持续时间
         * @param {Object} metadata - 元数据
         */
        checkPerformanceThresholds(operation, duration, metadata) {
            // 检查响应时间
            if (duration > this.monitorConfig.alertThresholds.responseTime) {
                this.logger?.log('响应时间超过阈值', 'warn', {
                    operation: operation,
                    duration: duration,
                    threshold: this.monitorConfig.alertThresholds.responseTime
                });
            }
        }

        /**
         * 异常检测 - 响应时间
         * @param {number} currentResponseTime - 当前响应时间
         */
        detectResponseTimeAnomaly(currentResponseTime) {
            const recentMetrics = this.getRecentMetrics('responseTime', 10);
            if (recentMetrics.length < 5) {
                return; // 数据不足
            }
            
            const averageResponseTime = recentMetrics.reduce((sum, m) => sum + m.value, 0) / recentMetrics.length;
            const threshold = averageResponseTime * this.anomalyDetector.thresholds.responseTimeSpike;
            
            if (currentResponseTime > threshold) {
                this.triggerAlert('response_time_anomaly', {
                    currentResponseTime: currentResponseTime,
                    averageResponseTime: averageResponseTime,
                    threshold: threshold,
                    spike: currentResponseTime / averageResponseTime
                });
            }
        }

        /**
         * 异常检测 - 内存泄漏
         * @param {number} currentMemoryUsage - 当前内存使用
         */
        detectMemoryLeak(currentMemoryUsage) {
            const recentMetrics = this.getRecentMetrics('memoryUsage', 20);
            if (recentMetrics.length < 10) {
                return;
            }
            
            // 计算内存增长趋势
            const oldAverage = recentMetrics.slice(0, 10).reduce((sum, m) => sum + m.value, 0) / 10;
            const newAverage = recentMetrics.slice(-10).reduce((sum, m) => sum + m.value, 0) / 10;
            const growthRate = (newAverage - oldAverage) / oldAverage;
            
            if (growthRate > this.anomalyDetector.thresholds.memoryLeakRate) {
                this.triggerAlert('memory_leak_detected', {
                    oldAverage: oldAverage,
                    newAverage: newAverage,
                    growthRate: (growthRate * 100).toFixed(2) + '%',
                    currentUsage: currentMemoryUsage
                });
            }
        }

        /**
         * 异常检测 - 错误率
         * @param {number} currentErrorRate - 当前错误率
         */
        detectErrorRateAnomaly(currentErrorRate) {
            const recentMetrics = this.getRecentMetrics('errorRate', 10);
            if (recentMetrics.length < 5) {
                return;
            }
            
            const averageErrorRate = recentMetrics.reduce((sum, m) => sum + m.value, 0) / recentMetrics.length;
            const threshold = averageErrorRate * this.anomalyDetector.thresholds.errorRateSpike;
            
            if (currentErrorRate > threshold && currentErrorRate > 0.05) { // 至少5%错误率才报警
                this.triggerAlert('error_rate_anomaly', {
                    currentErrorRate: (currentErrorRate * 100).toFixed(2) + '%',
                    averageErrorRate: (averageErrorRate * 100).toFixed(2) + '%',
                    threshold: (threshold * 100).toFixed(2) + '%'
                });
            }
        }

        /**
         * 触发报警
         * @param {string} alertType - 报警类型
         * @param {Object} data - 报警数据
         */
        triggerAlert(alertType, data) {
            try {
                if (!this.alertSystem.enabled) {
                    return;
                }
                
                // 检查冷却期
                const lastAlert = this.alertSystem.lastAlerts.get(alertType);
                const now = Date.now();
                
                if (lastAlert && (now - lastAlert) < this.alertSystem.cooldownPeriod) {
                    return; // 在冷却期内，不重复报警
                }
                
                // 记录报警时间
                this.alertSystem.lastAlerts.set(alertType, now);
                
                // 创建报警对象
                const alert = {
                    type: alertType,
                    timestamp: new Date().toISOString(),
                    severity: this.getAlertSeverity(alertType),
                    data: data,
                    id: this.generateAlertId()
                };
                
                // 执行报警处理器
                this.alertSystem.handlers.forEach(handler => {
                    try {
                        handler(alert);
                    } catch (error) {
                        this.logger?.logError('报警处理器执行失败', error);
                    }
                });
                
                // 记录报警日志
                this.logger?.log('性能报警触发', 'warn', alert);

            } catch (error) {
                this.logger?.logError('触发报警失败', error);
            }
        }

        /**
         * 获取报警严重程度
         * @param {string} alertType - 报警类型
         * @returns {string} 严重程度
         */
        getAlertSeverity(alertType) {
            const severityMap = {
                'high_error_rate': 'critical',
                'high_memory_usage': 'warning',
                'slow_response_time': 'warning',
                'low_cache_hit_rate': 'info',
                'response_time_anomaly': 'warning',
                'memory_leak_detected': 'critical',
                'error_rate_anomaly': 'critical'
            };
            
            return severityMap[alertType] || 'info';
        }

        /**
         * 生成报警ID
         * @returns {string} 报警ID
         */
        generateAlertId() {
            return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 注册报警处理器
         * @param {Function} handler - 处理器函数
         */
        registerAlertHandler(handler) {
            if (typeof handler === 'function') {
                this.alertSystem.handlers.push(handler);
            }
        }

        /**
         * 注册默认报警处理器
         */
        registerDefaultAlertHandlers() {
            // 控制台报警处理器
            this.registerAlertHandler((alert) => {
                const message = `[${alert.severity.toUpperCase()}] ${alert.type}: ${JSON.stringify(alert.data)}`;
                
                switch (alert.severity) {
                    case 'critical':
                        console.error(message);
                        break;
                    case 'warning':
                        console.warn(message);
                        break;
                    default:
                        console.info(message);
                }
            });
            
            // 日志系统报警处理器
            this.registerAlertHandler((alert) => {
                this.logger?.log(`性能报警: ${alert.type}`, alert.severity === 'critical' ? 'error' : 'warn', alert);
            });
        }

        /**
         * 获取最近的指标
         * @param {string} metricType - 指标类型
         * @param {number} count - 数量
         * @returns {Array} 指标数组
         */
        getRecentMetrics(metricType, count = 10) {
            const metrics = this.metrics[metricType] || [];
            return metrics.slice(-count);
        }

        /**
         * 限制指标历史数据
         * @param {string} metricType - 指标类型
         */
        limitMetricHistory(metricType) {
            const metrics = this.metrics[metricType];
            const maxEntries = 1000; // 每种指标最多保留1000条记录
            
            if (metrics.length > maxEntries) {
                this.metrics[metricType] = metrics.slice(-maxEntries);
            }
            
            // 清理过期数据
            const cutoffTime = Date.now() - (this.monitorConfig.retentionDays * 24 * 60 * 60 * 1000);
            this.metrics[metricType] = metrics.filter(metric => metric.timestamp > cutoffTime);
        }

        /**
         * 是否应该采样
         * @returns {boolean} 是否采样
         */
        shouldSample() {
            return Math.random() < this.monitorConfig.sampleRate;
        }

        /**
         * 设置指标收集
         */
        setupMetricsCollection() {
            setInterval(() => {
                this.collectSystemMetrics();
            }, this.monitorConfig.metricsInterval);
        }

        /**
         * 收集系统指标
         */
        collectSystemMetrics() {
            try {
                // 收集内存使用情况
                if (performance.memory) {
                    this.recordMemoryUsage(performance.memory.usedJSHeapSize);
                }
                
                // 收集缓存性能（如果缓存管理器可用）
                const cacheManager = window.OTA?.intelligentCacheManager;
                if (cacheManager) {
                    const cacheStats = cacheManager.getStats();
                    this.recordCachePerformance(cacheStats.hitRate, cacheStats);
                }
                
                // 收集学习效果（如果评估器可用）
                const evaluator = window.OTA?.learningEffectivenessEvaluator;
                if (evaluator) {
                    const currentEvaluation = evaluator.getCurrentEvaluation();
                    if (currentEvaluation) {
                        this.recordLearningEffectiveness(currentEvaluation.overallScore, currentEvaluation);
                    }
                }

            } catch (error) {
                this.logger?.logError('收集系统指标失败', error);
            }
        }

        /**
         * 设置异常检测
         */
        setupAnomalyDetection() {
            if (!this.anomalyDetector.enabled) {
                return;
            }
            
            // 定期运行异常检测
            setInterval(() => {
                this.runAnomalyDetection();
            }, 60000); // 每分钟检测一次
        }

        /**
         * 运行异常检测
         */
        runAnomalyDetection() {
            try {
                // 检测各种异常模式
                this.detectPerformanceDegradation();
                this.detectUnusualPatterns();
                
            } catch (error) {
                this.logger?.logError('异常检测失败', error);
            }
        }

        /**
         * 检测性能退化
         */
        detectPerformanceDegradation() {
            // 比较最近的性能与历史基线
            const recentResponseTimes = this.getRecentMetrics('responseTime', 20);
            const historicalResponseTimes = this.metrics.responseTime.slice(-100, -20);
            
            if (recentResponseTimes.length >= 10 && historicalResponseTimes.length >= 10) {
                const recentAverage = recentResponseTimes.reduce((sum, m) => sum + m.value, 0) / recentResponseTimes.length;
                const historicalAverage = historicalResponseTimes.reduce((sum, m) => sum + m.value, 0) / historicalResponseTimes.length;
                
                if (recentAverage > historicalAverage * 1.5) {
                    this.triggerAlert('performance_degradation', {
                        recentAverage: recentAverage,
                        historicalAverage: historicalAverage,
                        degradation: ((recentAverage / historicalAverage - 1) * 100).toFixed(1) + '%'
                    });
                }
            }
        }

        /**
         * 检测异常模式
         */
        detectUnusualPatterns() {
            // 检测操作数量异常
            const currentOperations = this.realTimeMetrics.currentOperations;
            if (currentOperations > 100) { // 假设100为异常阈值
                this.triggerAlert('high_concurrent_operations', {
                    currentOperations: currentOperations,
                    threshold: 100
                });
            }
        }

        /**
         * 设置内存监控
         */
        setupMemoryMonitoring() {
            if (performance.memory) {
                setInterval(() => {
                    this.recordMemoryUsage(performance.memory.usedJSHeapSize);
                }, 30000); // 每30秒监控一次内存
            }
        }

        /**
         * 恢复历史数据
         */
        restoreHistoricalData() {
            try {
                const storageKey = `${this.config.get('storage.keyPrefix')}performance_metrics`;
                const data = localStorage.getItem(storageKey);
                
                if (data) {
                    const historicalData = JSON.parse(data);
                    
                    // 恢复指标数据
                    Object.keys(this.metrics).forEach(metricType => {
                        if (historicalData[metricType]) {
                            this.metrics[metricType] = historicalData[metricType];
                            this.limitMetricHistory(metricType);
                        }
                    });
                    
                    this.logger?.log('历史性能数据恢复完成', 'info');
                }

            } catch (error) {
                this.logger?.logError('恢复历史数据失败', error);
            }
        }

        /**
         * 保存性能数据
         */
        savePerformanceData() {
            try {
                const storageKey = `${this.config.get('storage.keyPrefix')}performance_metrics`;
                localStorage.setItem(storageKey, JSON.stringify(this.metrics));
                
            } catch (error) {
                this.logger?.logError('保存性能数据失败', error);
            }
        }

        /**
         * 获取性能报告
         * @param {Object} options - 选项
         * @returns {Object} 性能报告
         */
        getPerformanceReport(options = {}) {
            const timeRange = options.timeRange || 24 * 60 * 60 * 1000; // 默认24小时
            const endTime = Date.now();
            const startTime = endTime - timeRange;
            
            const report = {
                timeRange: {
                    start: new Date(startTime).toISOString(),
                    end: new Date(endTime).toISOString()
                },
                summary: this.realTimeMetrics,
                metrics: {},
                alerts: this.getRecentAlerts(timeRange)
            };
            
            // 计算各指标的统计信息
            Object.keys(this.metrics).forEach(metricType => {
                const metrics = this.metrics[metricType].filter(m => 
                    m.timestamp >= startTime && m.timestamp <= endTime
                );
                
                if (metrics.length > 0) {
                    const values = metrics.map(m => m.value);
                    report.metrics[metricType] = {
                        count: metrics.length,
                        min: Math.min(...values),
                        max: Math.max(...values),
                        average: values.reduce((sum, v) => sum + v, 0) / values.length,
                        latest: values[values.length - 1]
                    };
                }
            });
            
            return report;
        }

        /**
         * 获取最近的报警
         * @param {number} timeRange - 时间范围
         * @returns {Array} 报警列表
         */
        getRecentAlerts(timeRange) {
            // 简化实现，实际应该从存储中获取
            return [];
        }

        /**
         * 获取实时指标
         * @returns {Object} 实时指标
         */
        getRealTimeMetrics() {
            return { ...this.realTimeMetrics };
        }

        /**
         * 重置统计
         */
        resetStats() {
            this.realTimeMetrics = {
                currentOperations: 0,
                totalOperations: 0,
                totalErrors: 0,
                averageResponseTime: 0,
                peakMemoryUsage: 0,
                lastUpdateTime: Date.now()
            };
            
            Object.keys(this.metrics).forEach(metricType => {
                this.metrics[metricType] = [];
            });
            
            this.logger?.log('性能统计已重置', 'info');
        }

        /**
         * 启用/禁用监控
         * @param {boolean} enabled - 是否启用
         */
        setEnabled(enabled) {
            this.monitorConfig.enabled = enabled;
            this.logger?.log(`性能监控${enabled ? '已启用' : '已禁用'}`, 'info');
        }

        /**
         * 设置采样率
         * @param {number} sampleRate - 采样率（0-1）
         */
        setSampleRate(sampleRate) {
            this.monitorConfig.sampleRate = Math.max(0, Math.min(1, sampleRate));
            this.logger?.log('采样率已更新', 'info', { sampleRate: this.monitorConfig.sampleRate });
        }
    }

    // 创建全局实例
    const performanceMonitor = new PerformanceMonitor();

    // 导出到全局命名空间
    window.OTA.performanceMonitor = performanceMonitor;
    window.performanceMonitor = performanceMonitor; // 向后兼容

    // 工厂函数
    window.getPerformanceMonitor = function() {
        return window.OTA.performanceMonitor || window.performanceMonitor;
    };

    // 定期保存性能数据
    setInterval(() => {
        performanceMonitor.savePerformanceData();
    }, 5 * 60 * 1000); // 每5分钟保存一次

    console.log('性能监控器加载完成', {
        version: performanceMonitor.version,
        enabled: performanceMonitor.monitorConfig.enabled,
        sampleRate: performanceMonitor.monitorConfig.sampleRate
    });

})();
