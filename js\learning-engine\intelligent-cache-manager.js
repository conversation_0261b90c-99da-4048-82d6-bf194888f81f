/**
 * 智能学习型格式预处理引擎 - 智能缓存管理器
 * 负责智能缓存策略、预测性预加载、缓存失效管理
 * 提高系统响应速度和用户体验
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return getService('learningConfig');
    }

    function getUserOperationLearner() {
        return getService('userOperationLearner');
    }

    function getPatternMatchingEngine() {
        return getService('patternMatchingEngine');
    }

    function getLogger() {
        return getService('logger');
    }

    // 服务定位器函数
    function getService(serviceName) {
        const serviceMap = {
            'learningConfig': () => window.OTA.learningConfig || window.learningConfig,
            'userOperationLearner': () => window.OTA.userOperationLearner || window.userOperationLearner,
            'patternMatchingEngine': () => window.OTA.patternMatchingEngine || window.patternMatchingEngine,
            'logger': () => window.OTA.logger || window.logger
        };
        
        const getter = serviceMap[serviceName];
        return getter ? getter() : null;
    }

    /**
     * 智能缓存管理器类
     * 提供智能缓存策略和预测性预加载功能
     */
    class IntelligentCacheManager {
        constructor() {
            this.config = getLearningConfig();
            this.operationLearner = getUserOperationLearner();
            this.patternMatcher = getPatternMatchingEngine();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            
            // 缓存配置
            this.cacheConfig = {
                maxSize: 1000,                    // 最大缓存项数
                maxMemoryUsage: 50 * 1024 * 1024, // 50MB内存限制
                defaultTTL: 30 * 60 * 1000,       // 30分钟默认TTL
                cleanupInterval: 5 * 60 * 1000,   // 5分钟清理间隔
                preloadThreshold: 0.7,            // 预加载阈值
                hitRateTarget: 0.8                // 目标命中率
            };
            
            // 多级缓存存储
            this.caches = {
                memory: new Map(),      // 内存缓存（最快）
                session: new Map(),     // 会话缓存（中等）
                persistent: new Map()   // 持久缓存（最慢但持久）
            };
            
            // 缓存元数据
            this.metadata = new Map();
            
            // 访问模式分析
            this.accessPatterns = new Map();
            
            // 预加载队列
            this.preloadQueue = [];
            
            // 统计信息
            this.stats = {
                hits: 0,
                misses: 0,
                evictions: 0,
                preloads: 0,
                memoryUsage: 0
            };

            this.initialize();
        }

        /**
         * 初始化智能缓存管理器
         */
        initialize() {
            try {
                // 恢复持久缓存
                this.restorePersistentCache();
                
                // 设置定期清理
                this.setupPeriodicCleanup();
                
                // 设置预加载处理
                this.setupPreloadProcessor();
                
                // 监听内存使用
                this.setupMemoryMonitoring();
                
                this.logger?.log('智能缓存管理器初始化完成', 'info', {
                    version: this.version,
                    maxSize: this.cacheConfig.maxSize,
                    maxMemoryUsage: this.cacheConfig.maxMemoryUsage / (1024 * 1024) + 'MB'
                });

            } catch (error) {
                this.logger?.logError('智能缓存管理器初始化失败', error);
            }
        }

        /**
         * 获取缓存项
         * @param {string} key - 缓存键
         * @param {Object} options - 选项
         * @returns {*} 缓存值或null
         */
        get(key, options = {}) {
            try {
                const startTime = performance.now();
                
                // 记录访问模式
                this.recordAccess(key);
                
                // 按优先级查找缓存
                const cacheTypes = ['memory', 'session', 'persistent'];
                
                for (const cacheType of cacheTypes) {
                    const cache = this.caches[cacheType];
                    const item = cache.get(key);
                    
                    if (item && this.isValidCacheItem(item)) {
                        // 更新访问时间
                        this.updateAccessTime(key, cacheType);
                        
                        // 如果从较慢的缓存获取，提升到更快的缓存
                        if (cacheType !== 'memory') {
                            this.promoteToFasterCache(key, item, cacheType);
                        }
                        
                        // 记录命中
                        this.stats.hits++;
                        
                        // 触发预测性预加载
                        this.triggerPredictivePreload(key);
                        
                        this.logger?.log('缓存命中', 'debug', {
                            key: key,
                            cacheType: cacheType,
                            responseTime: performance.now() - startTime
                        });
                        
                        return item.value;
                    }
                }
                
                // 缓存未命中
                this.stats.misses++;
                
                this.logger?.log('缓存未命中', 'debug', {
                    key: key,
                    responseTime: performance.now() - startTime
                });
                
                return null;

            } catch (error) {
                this.logger?.logError('获取缓存失败', error);
                this.stats.misses++;
                return null;
            }
        }

        /**
         * 设置缓存项
         * @param {string} key - 缓存键
         * @param {*} value - 缓存值
         * @param {Object} options - 选项
         * @returns {boolean} 是否成功
         */
        set(key, value, options = {}) {
            try {
                const cacheItem = this.createCacheItem(value, options);
                const cacheType = this.determineCacheType(key, value, options);
                
                // 检查内存使用
                if (cacheType === 'memory' && !this.checkMemoryLimit(cacheItem)) {
                    // 内存不足，降级到会话缓存
                    cacheType = 'session';
                }
                
                // 存储到指定缓存
                this.caches[cacheType].set(key, cacheItem);
                
                // 更新元数据
                this.updateMetadata(key, cacheType, cacheItem);
                
                // 检查缓存大小限制
                this.enforceSizeLimits();
                
                this.logger?.log('缓存设置成功', 'debug', {
                    key: key,
                    cacheType: cacheType,
                    size: this.estimateSize(cacheItem)
                });
                
                return true;

            } catch (error) {
                this.logger?.logError('设置缓存失败', error);
                return false;
            }
        }

        /**
         * 删除缓存项
         * @param {string} key - 缓存键
         * @returns {boolean} 是否成功
         */
        delete(key) {
            try {
                let deleted = false;
                
                // 从所有缓存中删除
                Object.values(this.caches).forEach(cache => {
                    if (cache.delete(key)) {
                        deleted = true;
                    }
                });
                
                // 删除元数据
                this.metadata.delete(key);
                this.accessPatterns.delete(key);
                
                if (deleted) {
                    this.logger?.log('缓存删除成功', 'debug', { key: key });
                }
                
                return deleted;

            } catch (error) {
                this.logger?.logError('删除缓存失败', error);
                return false;
            }
        }

        /**
         * 预加载缓存
         * @param {string} key - 缓存键
         * @param {Function} loader - 加载函数
         * @param {Object} options - 选项
         */
        preload(key, loader, options = {}) {
            try {
                // 检查是否已存在
                if (this.get(key)) {
                    return;
                }
                
                // 添加到预加载队列
                this.preloadQueue.push({
                    key: key,
                    loader: loader,
                    options: options,
                    priority: options.priority || 1,
                    timestamp: Date.now()
                });
                
                // 按优先级排序
                this.preloadQueue.sort((a, b) => b.priority - a.priority);
                
                this.logger?.log('预加载任务已添加', 'debug', {
                    key: key,
                    queueSize: this.preloadQueue.length
                });

            } catch (error) {
                this.logger?.logError('添加预加载任务失败', error);
            }
        }

        /**
         * 智能失效缓存
         * @param {string|RegExp|Function} pattern - 失效模式
         */
        invalidate(pattern) {
            try {
                let invalidatedCount = 0;
                
                const shouldInvalidate = (key) => {
                    if (typeof pattern === 'string') {
                        return key === pattern || key.includes(pattern);
                    } else if (pattern instanceof RegExp) {
                        return pattern.test(key);
                    } else if (typeof pattern === 'function') {
                        return pattern(key);
                    }
                    return false;
                };
                
                // 从所有缓存中失效匹配的项
                Object.values(this.caches).forEach(cache => {
                    const keysToDelete = [];
                    for (const key of cache.keys()) {
                        if (shouldInvalidate(key)) {
                            keysToDelete.push(key);
                        }
                    }
                    
                    keysToDelete.forEach(key => {
                        cache.delete(key);
                        this.metadata.delete(key);
                        invalidatedCount++;
                    });
                });
                
                this.logger?.log('缓存失效完成', 'info', {
                    pattern: pattern.toString(),
                    invalidatedCount: invalidatedCount
                });

            } catch (error) {
                this.logger?.logError('缓存失效失败', error);
            }
        }

        /**
         * 创建缓存项
         * @param {*} value - 值
         * @param {Object} options - 选项
         * @returns {Object} 缓存项
         */
        createCacheItem(value, options) {
            const now = Date.now();
            const ttl = options.ttl || this.cacheConfig.defaultTTL;
            
            return {
                value: value,
                createdAt: now,
                lastAccessed: now,
                accessCount: 0,
                expiresAt: now + ttl,
                size: this.estimateSize(value),
                metadata: options.metadata || {}
            };
        }

        /**
         * 确定缓存类型
         * @param {string} key - 键
         * @param {*} value - 值
         * @param {Object} options - 选项
         * @returns {string} 缓存类型
         */
        determineCacheType(key, value, options) {
            // 根据选项指定
            if (options.cacheType) {
                return options.cacheType;
            }
            
            // 根据数据特征自动选择
            const size = this.estimateSize(value);
            const isPersistent = options.persistent || false;
            
            if (isPersistent) {
                return 'persistent';
            } else if (size > 1024 * 1024) { // 大于1MB
                return 'session';
            } else {
                return 'memory';
            }
        }

        /**
         * 估算数据大小
         * @param {*} data - 数据
         * @returns {number} 估算大小（字节）
         */
        estimateSize(data) {
            try {
                if (data === null || data === undefined) {
                    return 0;
                }
                
                if (typeof data === 'string') {
                    return data.length * 2; // Unicode字符
                }
                
                if (typeof data === 'number') {
                    return 8;
                }
                
                if (typeof data === 'boolean') {
                    return 4;
                }
                
                if (typeof data === 'object') {
                    return JSON.stringify(data).length * 2;
                }
                
                return 100; // 默认估算

            } catch (error) {
                return 100; // 出错时的默认值
            }
        }

        /**
         * 检查缓存项是否有效
         * @param {Object} item - 缓存项
         * @returns {boolean} 是否有效
         */
        isValidCacheItem(item) {
            if (!item) {
                return false;
            }
            
            // 检查过期时间
            if (item.expiresAt && Date.now() > item.expiresAt) {
                return false;
            }
            
            return true;
        }

        /**
         * 更新访问时间
         * @param {string} key - 键
         * @param {string} cacheType - 缓存类型
         */
        updateAccessTime(key, cacheType) {
            const cache = this.caches[cacheType];
            const item = cache.get(key);
            
            if (item) {
                item.lastAccessed = Date.now();
                item.accessCount++;
                cache.set(key, item);
            }
        }

        /**
         * 提升到更快的缓存
         * @param {string} key - 键
         * @param {Object} item - 缓存项
         * @param {string} currentCacheType - 当前缓存类型
         */
        promoteToFasterCache(key, item, currentCacheType) {
            try {
                // 只有频繁访问的项才提升
                if (item.accessCount < 3) {
                    return;
                }
                
                let targetCacheType = null;
                
                if (currentCacheType === 'persistent') {
                    targetCacheType = 'session';
                } else if (currentCacheType === 'session') {
                    targetCacheType = 'memory';
                }
                
                if (targetCacheType && this.checkMemoryLimit(item)) {
                    this.caches[targetCacheType].set(key, item);
                    this.updateMetadata(key, targetCacheType, item);
                    
                    this.logger?.log('缓存提升', 'debug', {
                        key: key,
                        from: currentCacheType,
                        to: targetCacheType
                    });
                }

            } catch (error) {
                this.logger?.logError('缓存提升失败', error);
            }
        }

        /**
         * 记录访问模式
         * @param {string} key - 键
         */
        recordAccess(key) {
            const now = Date.now();
            
            if (!this.accessPatterns.has(key)) {
                this.accessPatterns.set(key, {
                    accessTimes: [],
                    frequency: 0,
                    lastAccess: now
                });
            }
            
            const pattern = this.accessPatterns.get(key);
            pattern.accessTimes.push(now);
            pattern.frequency++;
            pattern.lastAccess = now;
            
            // 限制访问时间记录数量
            if (pattern.accessTimes.length > 100) {
                pattern.accessTimes = pattern.accessTimes.slice(-50);
            }
        }

        /**
         * 触发预测性预加载
         * @param {string} key - 当前访问的键
         */
        triggerPredictivePreload(key) {
            try {
                // 基于访问模式预测下一个可能访问的键
                const relatedKeys = this.predictRelatedKeys(key);
                
                relatedKeys.forEach(relatedKey => {
                    if (!this.get(relatedKey) && !this.isInPreloadQueue(relatedKey)) {
                        // 添加到预加载队列（需要外部提供加载器）
                        this.logger?.log('预测性预加载触发', 'debug', {
                            currentKey: key,
                            predictedKey: relatedKey
                        });
                    }
                });

            } catch (error) {
                this.logger?.logError('预测性预加载失败', error);
            }
        }

        /**
         * 预测相关键
         * @param {string} key - 当前键
         * @returns {Array} 相关键列表
         */
        predictRelatedKeys(key) {
            const relatedKeys = [];
            
            // 基于模式匹配预测
            for (const [patternKey, pattern] of this.accessPatterns.entries()) {
                if (patternKey !== key) {
                    const similarity = this.patternMatcher.calculateSimilarity(key, patternKey);
                    if (similarity > 0.7) {
                        relatedKeys.push(patternKey);
                    }
                }
            }
            
            // 基于时间序列预测
            const timeBasedPredictions = this.predictByTimeSequence(key);
            relatedKeys.push(...timeBasedPredictions);
            
            return relatedKeys.slice(0, 5); // 限制预测数量
        }

        /**
         * 基于时间序列预测
         * @param {string} key - 当前键
         * @returns {Array} 预测键列表
         */
        predictByTimeSequence(key) {
            // 简化实现：查找在当前键访问后经常被访问的键
            const predictions = [];
            const currentPattern = this.accessPatterns.get(key);
            
            if (!currentPattern) {
                return predictions;
            }
            
            const recentAccess = currentPattern.lastAccess;
            const timeWindow = 5 * 60 * 1000; // 5分钟窗口
            
            for (const [otherKey, otherPattern] of this.accessPatterns.entries()) {
                if (otherKey !== key) {
                    // 检查是否在时间窗口内被访问
                    const timeDiff = otherPattern.lastAccess - recentAccess;
                    if (timeDiff > 0 && timeDiff < timeWindow) {
                        predictions.push(otherKey);
                    }
                }
            }
            
            return predictions;
        }

        /**
         * 检查是否在预加载队列中
         * @param {string} key - 键
         * @returns {boolean} 是否在队列中
         */
        isInPreloadQueue(key) {
            return this.preloadQueue.some(item => item.key === key);
        }

        /**
         * 检查内存限制
         * @param {Object} item - 缓存项
         * @returns {boolean} 是否在限制内
         */
        checkMemoryLimit(item) {
            const currentUsage = this.calculateMemoryUsage();
            const itemSize = item.size || this.estimateSize(item.value);
            
            return (currentUsage + itemSize) <= this.cacheConfig.maxMemoryUsage;
        }

        /**
         * 计算内存使用量
         * @returns {number} 内存使用量（字节）
         */
        calculateMemoryUsage() {
            let totalUsage = 0;
            
            this.caches.memory.forEach(item => {
                totalUsage += item.size || 0;
            });
            
            this.stats.memoryUsage = totalUsage;
            return totalUsage;
        }

        /**
         * 强制执行大小限制
         */
        enforceSizeLimits() {
            Object.entries(this.caches).forEach(([cacheType, cache]) => {
                if (cache.size > this.cacheConfig.maxSize) {
                    this.evictLeastUsed(cacheType, cache.size - this.cacheConfig.maxSize);
                }
            });
        }

        /**
         * 驱逐最少使用的项
         * @param {string} cacheType - 缓存类型
         * @param {number} count - 驱逐数量
         */
        evictLeastUsed(cacheType, count) {
            const cache = this.caches[cacheType];
            const items = Array.from(cache.entries());
            
            // 按访问时间和频率排序
            items.sort((a, b) => {
                const scoreA = this.calculateEvictionScore(a[1]);
                const scoreB = this.calculateEvictionScore(b[1]);
                return scoreA - scoreB;
            });
            
            // 驱逐最少使用的项
            for (let i = 0; i < Math.min(count, items.length); i++) {
                const [key] = items[i];
                cache.delete(key);
                this.metadata.delete(key);
                this.stats.evictions++;
                
                this.logger?.log('缓存驱逐', 'debug', {
                    key: key,
                    cacheType: cacheType
                });
            }
        }

        /**
         * 计算驱逐分数
         * @param {Object} item - 缓存项
         * @returns {number} 分数（越低越容易被驱逐）
         */
        calculateEvictionScore(item) {
            const now = Date.now();
            const age = now - item.createdAt;
            const timeSinceLastAccess = now - item.lastAccessed;
            const accessFrequency = item.accessCount;
            
            // 综合考虑年龄、最后访问时间和访问频率
            return (age * 0.3 + timeSinceLastAccess * 0.5) / (accessFrequency + 1);
        }

        /**
         * 更新元数据
         * @param {string} key - 键
         * @param {string} cacheType - 缓存类型
         * @param {Object} item - 缓存项
         */
        updateMetadata(key, cacheType, item) {
            this.metadata.set(key, {
                cacheType: cacheType,
                size: item.size,
                createdAt: item.createdAt,
                lastAccessed: item.lastAccessed,
                accessCount: item.accessCount
            });
        }

        /**
         * 恢复持久缓存
         */
        restorePersistentCache() {
            try {
                const storageKey = `${this.config.get('storage.keyPrefix')}persistent_cache`;
                const data = localStorage.getItem(storageKey);
                
                if (data) {
                    const persistentData = JSON.parse(data);
                    
                    Object.entries(persistentData).forEach(([key, item]) => {
                        if (this.isValidCacheItem(item)) {
                            this.caches.persistent.set(key, item);
                            this.updateMetadata(key, 'persistent', item);
                        }
                    });
                    
                    this.logger?.log('持久缓存恢复完成', 'info', {
                        restoredCount: this.caches.persistent.size
                    });
                }

            } catch (error) {
                this.logger?.logError('恢复持久缓存失败', error);
            }
        }

        /**
         * 保存持久缓存
         */
        savePersistentCache() {
            try {
                const storageKey = `${this.config.get('storage.keyPrefix')}persistent_cache`;
                const persistentData = Object.fromEntries(this.caches.persistent);
                
                localStorage.setItem(storageKey, JSON.stringify(persistentData));
                
                this.logger?.log('持久缓存保存完成', 'debug', {
                    savedCount: this.caches.persistent.size
                });

            } catch (error) {
                this.logger?.logError('保存持久缓存失败', error);
            }
        }

        /**
         * 设置定期清理
         */
        setupPeriodicCleanup() {
            setInterval(() => {
                this.performCleanup();
            }, this.cacheConfig.cleanupInterval);
        }

        /**
         * 执行清理
         */
        performCleanup() {
            try {
                let cleanedCount = 0;
                
                // 清理过期项
                Object.entries(this.caches).forEach(([cacheType, cache]) => {
                    const keysToDelete = [];
                    
                    for (const [key, item] of cache.entries()) {
                        if (!this.isValidCacheItem(item)) {
                            keysToDelete.push(key);
                        }
                    }
                    
                    keysToDelete.forEach(key => {
                        cache.delete(key);
                        this.metadata.delete(key);
                        cleanedCount++;
                    });
                });
                
                // 保存持久缓存
                this.savePersistentCache();
                
                if (cleanedCount > 0) {
                    this.logger?.log('定期清理完成', 'debug', {
                        cleanedCount: cleanedCount
                    });
                }

            } catch (error) {
                this.logger?.logError('定期清理失败', error);
            }
        }

        /**
         * 设置预加载处理器
         */
        setupPreloadProcessor() {
            setInterval(() => {
                this.processPreloadQueue();
            }, 1000); // 每秒处理一次
        }

        /**
         * 处理预加载队列
         */
        processPreloadQueue() {
            if (this.preloadQueue.length === 0) {
                return;
            }
            
            // 处理一个预加载任务
            const task = this.preloadQueue.shift();
            
            try {
                // 检查是否仍然需要预加载
                if (!this.get(task.key)) {
                    task.loader().then(value => {
                        this.set(task.key, value, task.options);
                        this.stats.preloads++;
                        
                        this.logger?.log('预加载完成', 'debug', {
                            key: task.key
                        });
                    }).catch(error => {
                        this.logger?.logError('预加载失败', error);
                    });
                }

            } catch (error) {
                this.logger?.logError('处理预加载任务失败', error);
            }
        }

        /**
         * 设置内存监控
         */
        setupMemoryMonitoring() {
            setInterval(() => {
                const memoryUsage = this.calculateMemoryUsage();
                const usagePercentage = memoryUsage / this.cacheConfig.maxMemoryUsage;
                
                if (usagePercentage > 0.9) {
                    this.logger?.log('内存使用率过高，开始清理', 'warn', {
                        usage: memoryUsage,
                        percentage: (usagePercentage * 100).toFixed(1) + '%'
                    });
                    
                    // 强制清理内存缓存
                    this.evictLeastUsed('memory', Math.ceil(this.caches.memory.size * 0.2));
                }
            }, 30000); // 每30秒检查一次
        }

        /**
         * 获取缓存统计
         * @returns {Object} 统计信息
         */
        getStats() {
            const hitRate = this.stats.hits + this.stats.misses > 0 ? 
                this.stats.hits / (this.stats.hits + this.stats.misses) : 0;
            
            return {
                ...this.stats,
                hitRate: hitRate,
                cacheSize: {
                    memory: this.caches.memory.size,
                    session: this.caches.session.size,
                    persistent: this.caches.persistent.size
                },
                memoryUsagePercentage: (this.stats.memoryUsage / this.cacheConfig.maxMemoryUsage * 100).toFixed(1) + '%',
                preloadQueueSize: this.preloadQueue.length
            };
        }

        /**
         * 清空所有缓存
         */
        clear() {
            Object.values(this.caches).forEach(cache => cache.clear());
            this.metadata.clear();
            this.accessPatterns.clear();
            this.preloadQueue = [];
            
            // 重置统计
            this.stats = {
                hits: 0,
                misses: 0,
                evictions: 0,
                preloads: 0,
                memoryUsage: 0
            };
            
            this.logger?.log('所有缓存已清空', 'info');
        }

        /**
         * 获取缓存键列表
         * @param {string} cacheType - 缓存类型（可选）
         * @returns {Array} 键列表
         */
        getKeys(cacheType = null) {
            if (cacheType) {
                return Array.from(this.caches[cacheType].keys());
            }
            
            const allKeys = new Set();
            Object.values(this.caches).forEach(cache => {
                cache.forEach((_, key) => allKeys.add(key));
            });
            
            return Array.from(allKeys);
        }
    }

    // 创建全局实例
    const intelligentCacheManager = new IntelligentCacheManager();

    // 导出到全局命名空间
    window.OTA.intelligentCacheManager = intelligentCacheManager;
    window.intelligentCacheManager = intelligentCacheManager; // 向后兼容

    // 工厂函数
    window.getIntelligentCacheManager = function() {
        return window.OTA.intelligentCacheManager || window.intelligentCacheManager;
    };

    console.log('智能缓存管理器加载完成', {
        version: intelligentCacheManager.version,
        maxSize: intelligentCacheManager.cacheConfig.maxSize,
        maxMemoryUsage: intelligentCacheManager.cacheConfig.maxMemoryUsage / (1024 * 1024) + 'MB'
    });

})();
