---
type: "always_apply"
---

# OTA订单处理系统 - AI代理开发指南

这是一个专为GoMyHire构建的OTA订单智能处理系统，采用原生JavaScript模块化架构，无构建步骤的纯静态部署。

## 🏗️ 系统架构概览

### 核心架构模式
- **传统Script标签模块系统**: 使用`window.OTA`命名空间组织所有模块，避免ES6模块的兼容性问题
- **事件驱动架构**: 模块间通过DOM事件和状态变更通信，确保松耦合
- **状态集中管理**: `app-state.js`作为单一数据源，所有状态变更触发事件通知
- **分层服务架构**: UI层 → Manager层 → Service层 → API层，严格分离职责

### 实际模块加载顺序（按index.html）
必须严格按照以下顺序加载，依赖关系已经过生产验证：
```html
<!-- 1. 基础层：工具和日志 -->
<script src="js/utils.js"></script>
<script src="js/logger.js"></script>
<script src="js/monitoring-wrapper.js"></script>
<!-- 2. 映射数据层 -->
<script src="js/ota-channel-mapping.js"></script>
<!-- 3. 核心状态管理 -->
<script src="js/app-state.js"></script>
<!-- 4. 服务层 -->
<script src="js/api-service.js"></script>
<script src="js/gemini-service.js"></script>
<!-- 5. 功能模块层 -->
<script src="js/order-history-manager.js"></script>
<script src="js/image-upload-manager.js"></script>
<script src="js/currency-converter.js"></script>
<!-- 6. UI组件层 -->
<script src="js/multi-order-manager.js"></script>
<script src="js/multi-select-dropdown.js"></script>
<script src="js/grid-resizer.js"></script>
<script src="js/i18n.js"></script>
<!-- 7. 管理器层 -->
<script src="js/managers/form-manager.js"></script>
<script src="js/managers/price-manager.js"></script>
<script src="js/managers/event-manager.js"></script>
<script src="js/managers/state-manager.js"></script>
<!-- 8. UI主控制器 -->
<script src="js/ui-manager.js"></script>
<!-- 9. 主入口 -->
<script src="main.js"></script>
```

## 🧩 关键架构决策

### 命名空间模式
所有模块必须挂载到`window.OTA`命名空间，支持向后兼容：
```javascript
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};
window.OTA.managers.FormManager = FormManager;
// 向后兼容
window.formManager = window.OTA.managers.FormManager;
```

### 依赖注入模式
由于script标签加载的异步性，使用延迟获取模式：
```javascript
function getAppState() {
    return window.OTA.appState || window.appState;
}
```

### 状态管理约定
- 所有状态变更必须通过`appState.setState()`方法
- 状态变更会自动触发相应的DOM事件通知
- UI组件监听状态事件进行更新，不直接修改状态
- 关键状态: `auth`, `systemData`, `currentOrder`, `config`

### 静态数据优先原则
API Service包含完整的静态映射数据，避免重复API调用：
- `backendUsers` - 后台用户列表（已固化到代码中）
- `subCategories` - 订单类别映射
- `carTypes` - 车型数据
- `drivingRegions` - 驾驶区域
- 只有在静态数据不可用时才调用API

## 🔧 开发工作流程

### 必读memory-bank文档（优先级顺序）
开始任何开发任务前，检查以下文档（部分可能为空但预留了结构）：
1. `memory-bank/activeContext.md` - 当前工作焦点（可能为空）
2. `memory-bank/systemPatterns.md` - 系统架构决策（可能为空）
3. `memory-bank/progress.md` - 项目进度和已知问题（可能为空）
4. `memory-bank/techContext.md` - 技术约束和配置（可能为空）

### 调试和监控体系
- **全局监控**: `js/monitoring-wrapper.js`提供工厂函数监控、性能分析、错误跟踪
- **按钮诊断**: `js/button-diagnostics.js`验证UI组件功能
- **实时状态验证**: 浏览器控制台访问`window.OTA.appState.getState()`
- **监控控制台命令**: `monitoring.report()`, `monitoring.setDebug(true)`

### 测试页面模式
系统提供多个专用测试页面用于隔离测试：
- `test-date-and-history.html` - 日期和历史功能测试
- `test-dropdown-fix.html` - 下拉菜单功能测试
- `test-responsible-person.html` - 负责人功能测试
- `debug-order-creation.html` - 订单创建调试页面

## 🔧 开发工作流程

### 必读memory-bank文档（优先级顺序）
开始任何开发任务前，检查以下文档（部分可能为空但预留了结构）：
1. `memory-bank/activeContext.md` - 当前工作焦点（可能为空）
2. `memory-bank/systemPatterns.md` - 系统架构决策（可能为空）
3. `memory-bank/progress.md` - 项目进度和已知问题（可能为空）
4. `memory-bank/techContext.md` - 技术约束和配置（可能为空）

### 调试和监控体系
- **全局监控**: `js/monitoring-wrapper.js`提供工厂函数监控、性能分析、错误跟踪
- **按钮诊断**: `js/button-diagnostics.js`验证UI组件功能
- **实时状态验证**: 浏览器控制台访问`window.OTA.appState.getState()`
- **监控控制台命令**: `monitoring.report()`, `monitoring.setDebug(true)`

### 测试页面模式
系统提供多个专用测试页面用于隔离测试：
- `test-date-and-history.html` - 日期和历史功能测试
- `test-dropdown-fix.html` - 下拉菜单功能测试
- `test-responsible-person.html` - 负责人功能测试
- `debug-order-creation.html` - 订单创建调试页面

### 错误诊断和修复流程
1. 查看`docs/reports/`中的相关修复报告
2. 使用监控系统确定问题范围
3. 检查控制台错误和性能警告
4. 更新相应的报告文档记录修复内容

### API集成模式
- **静态数据优先**: `api-service.js`包含完整的静态映射数据，避免重复API调用
- **错误恢复**: 所有API调用都有fallback机制和重试逻辑
- **认证处理**: Token自动刷新和过期处理已内置

## 🎯 项目特定约定

### 文件组织规范
- `js/managers/` - 业务逻辑管理器（状态、事件、表单、价格）
- `js/` - 核心服务和工具类
- `docs/reports/` - 修复报告和系统文档
- `memory-bank/` - 项目上下文和知识库

### 错误处理模式
```javascript
// 标准错误处理模式
try {
    const result = await apiCall();
    logger.log('操作成功', 'success', result);
} catch (error) {
    logger.logError('操作失败', error);
    // 显示用户友好的错误信息
    uiManager.showError('操作失败，请重试');
}
```

### 国际化支持
- 使用`js/i18n.js`进行多语言支持
- 所有用户面向的文本都要通过i18n函数处理
- 默认语言为中文，支持英文切换

## 🚀 部署和环境

### Netlify配置要点
- **无构建步骤**: 纯静态文件部署
- **CSP策略**: 严格的内容安全策略，注意API域名白名单
- **缓存策略**: 静态资源永久缓存，HTML文件无缓存

### 环境变量和API密钥
- Gemini API密钥在运行时通过UI配置
- GoMyHire API使用用户提供的认证token
- 所有敏感信息不在代码中硬编码

## ⚠️ 关键注意事项

### 性能优化
- 大文件（>800行）需要拆分
- 避免循环依赖，特别注意manager层的相互引用
- 使用`utils.performanceMonitor`跟踪关键操作性能

### 安全考虑
- 所有用户输入都经过验证和清理
- API调用都有超时和错误边界保护
- 本地存储数据加密存储敏感信息

### 维护性原则
- 每个功能修改都要更新相关的报告文档
- 使用中文注释解释业务逻辑
- 保持单一职责原则，一个文件只做一件事

---

**记住**: 这是一个生产环境系统，任何修改都可能影响真实的订单处理。优先考虑稳定性和可维护性，而不是快速功能实现。