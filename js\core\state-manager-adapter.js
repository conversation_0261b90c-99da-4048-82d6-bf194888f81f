/**
 * 状态管理器适配器
 * 提供从旧的AppState到新的中央状态管理器的平滑迁移
 * 
 * 功能特性:
 * - 兼容现有的AppState API
 * - 自动路由到中央状态管理器
 * - 提供迁移警告和建议
 * - 支持渐进式迁移
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 状态管理器适配器类
     * 桥接旧的AppState API和新的中央状态管理器
     */
    class StateManagerAdapter {
        constructor() {
            this.centralStateManager = null;
            this.appState = null;
            this.migrationWarnings = new Set();
            this.pathMappings = new Map();
            this.logger = null;
            this.initialized = false;
        }

        /**
         * 初始化适配器
         */
        init() {
            if (this.initialized) {
                console.warn('状态管理器适配器已经初始化');
                return;
            }

            // 获取依赖
            this.centralStateManager = window.getCentralStateManager ? window.getCentralStateManager() : null;
            this.appState = window.getService ? window.getService('appState') : null;
            this.logger = window.getService ? window.getService('logger') : null;

            // 设置路径映射
            this.setupPathMappings();

            // 如果中央状态管理器可用，初始化它
            if (this.centralStateManager && !this.centralStateManager.initialized) {
                this.centralStateManager.init();
            }

            this.initialized = true;
            this.log('状态管理器适配器初始化完成', 'success');
        }

        /**
         * 设置路径映射
         * 将旧的AppState路径映射到新的中央状态管理器路径
         */
        setupPathMappings() {
            // 认证相关
            this.pathMappings.set('auth.isLoggedIn', 'auth.isLoggedIn');
            this.pathMappings.set('auth.token', 'auth.token');
            this.pathMappings.set('auth.user', 'auth.user');
            this.pathMappings.set('auth.tokenExpiry', 'auth.tokenExpiry');

            // 系统数据映射到缓存域
            this.pathMappings.set('systemData.backendUsers', 'cache.backendUsers');
            this.pathMappings.set('systemData.subCategories', 'cache.subCategories');
            this.pathMappings.set('systemData.carTypes', 'cache.carTypes');
            this.pathMappings.set('systemData.drivingRegions', 'cache.drivingRegions');
            this.pathMappings.set('systemData.languages', 'cache.languages');
            this.pathMappings.set('systemData.lastUpdated', 'cache.lastUpdated');

            // 当前订单映射到订单域
            this.pathMappings.set('currentOrder.rawInput', 'order.current.rawInput');
            this.pathMappings.set('currentOrder.parsedData', 'order.current.parsedData');
            this.pathMappings.set('currentOrder.formData', 'order.current.formData');
            this.pathMappings.set('currentOrder.validationErrors', 'order.current.validationErrors');
            this.pathMappings.set('currentOrder.status', 'order.current.status');

            // 配置映射
            this.pathMappings.set('config.theme', 'ui.theme');
            this.pathMappings.set('config.language', 'ui.language');
            this.pathMappings.set('config.debugMode', 'ui.debugMode');
            this.pathMappings.set('config.autoSave', 'config.autoSave');
            this.pathMappings.set('config.defaultBackendUserId', 'config.defaultBackendUserId');
            this.pathMappings.set('config.geminiApiKey', 'config.geminiApiKey');

            // 系统状态映射
            this.pathMappings.set('system.connected', 'system.connected');
            this.pathMappings.set('system.lastApiCall', 'system.lastApiCall');
            this.pathMappings.set('system.apiCallCount', 'system.apiCallCount');
            this.pathMappings.set('system.errors', 'system.errors');
        }

        /**
         * 映射路径到中央状态管理器格式
         * @param {string} oldPath - 旧的路径格式
         * @returns {Object} {domain, path} 新的路径格式
         */
        mapPath(oldPath) {
            const mappedPath = this.pathMappings.get(oldPath);
            if (mappedPath) {
                const [domain, ...pathParts] = mappedPath.split('.');
                return { domain, path: pathParts.join('.') };
            }

            // 如果没有映射，尝试智能推断
            const [firstPart, ...restParts] = oldPath.split('.');
            
            // 根据第一部分推断域
            const domainMap = {
                'auth': 'auth',
                'systemData': 'cache',
                'currentOrder': 'order',
                'config': 'config',
                'system': 'system'
            };

            const domain = domainMap[firstPart] || 'config';
            const path = restParts.join('.');

            return { domain, path };
        }

        /**
         * 获取状态值（兼容AppState API）
         * @param {string} path - 状态路径
         * @returns {any} 状态值
         */
        get(path) {
            // 如果中央状态管理器可用，优先使用
            if (this.centralStateManager && this.centralStateManager.initialized) {
                const { domain, path: mappedPath } = this.mapPath(path);
                const value = this.centralStateManager.getState(domain, mappedPath);
                
                // 发出迁移建议（每个路径只警告一次）
                if (!this.migrationWarnings.has(path)) {
                    this.log(`建议迁移: 使用 window.OTA.state.get('${domain}.${mappedPath}') 替代 appState.get('${path}')`, 'info');
                    this.migrationWarnings.add(path);
                }
                
                return value;
            }

            // 降级到原始AppState
            if (this.appState && typeof this.appState.get === 'function') {
                return this.appState.get(path);
            }

            this.log(`无法获取状态: ${path}`, 'error');
            return null;
        }

        /**
         * 设置状态值（兼容AppState API）
         * @param {string} path - 状态路径
         * @param {any} value - 新值
         * @param {boolean} save - 是否保存到本地存储
         */
        set(path, value, save = true) {
            // 如果中央状态管理器可用，优先使用
            if (this.centralStateManager && this.centralStateManager.initialized) {
                const { domain, path: mappedPath } = this.mapPath(path);
                const result = this.centralStateManager.setState(domain, mappedPath, value, { persist: save });
                
                // 发出迁移建议（每个路径只警告一次）
                if (!this.migrationWarnings.has(path)) {
                    this.log(`建议迁移: 使用 window.OTA.state.set('${domain}.${mappedPath}', value) 替代 appState.set('${path}', value)`, 'info');
                    this.migrationWarnings.add(path);
                }
                
                return result;
            }

            // 降级到原始AppState
            if (this.appState && typeof this.appState.set === 'function') {
                return this.appState.set(path, value, save);
            }

            this.log(`无法设置状态: ${path}`, 'error');
            return false;
        }

        /**
         * 监听状态变化（兼容AppState API）
         * @param {string} path - 状态路径
         * @param {Function} callback - 回调函数
         * @returns {Function} 取消监听函数
         */
        on(path, callback) {
            // 如果中央状态管理器可用，优先使用
            if (this.centralStateManager && this.centralStateManager.initialized) {
                const { domain, path: mappedPath } = this.mapPath(path);
                const unsubscribe = this.centralStateManager.subscribe(domain, mappedPath, callback);
                
                // 发出迁移建议（每个路径只警告一次）
                if (!this.migrationWarnings.has(`on:${path}`)) {
                    this.log(`建议迁移: 使用 window.OTA.state.subscribe('${domain}.${mappedPath}', callback) 替代 appState.on('${path}', callback)`, 'info');
                    this.migrationWarnings.add(`on:${path}`);
                }
                
                return unsubscribe;
            }

            // 降级到原始AppState
            if (this.appState && typeof this.appState.on === 'function') {
                return this.appState.on(path, callback);
            }

            this.log(`无法监听状态: ${path}`, 'error');
            return () => {};
        }

        /**
         * 清除认证状态（兼容AppState API）
         */
        clearAuth() {
            if (this.centralStateManager && this.centralStateManager.initialized) {
                this.centralStateManager.setState('auth', 'isLoggedIn', false);
                this.centralStateManager.setState('auth', 'token', null);
                this.centralStateManager.setState('auth', 'user', null);
                this.centralStateManager.setState('auth', 'tokenExpiry', null);
                this.log('认证状态已清除（中央状态管理器）', 'info');
                return;
            }

            if (this.appState && typeof this.appState.clearAuth === 'function') {
                this.appState.clearAuth();
                this.log('认证状态已清除（AppState）', 'info');
                return;
            }

            this.log('无法清除认证状态', 'error');
        }

        /**
         * 获取迁移报告
         * @returns {Object} 迁移状态报告
         */
        getMigrationReport() {
            return {
                adapterInitialized: this.initialized,
                centralStateManagerAvailable: !!(this.centralStateManager && this.centralStateManager.initialized),
                appStateAvailable: !!(this.appState),
                pathMappings: this.pathMappings.size,
                warningsIssued: this.migrationWarnings.size,
                warnings: Array.from(this.migrationWarnings),
                recommendations: [
                    '逐步将Manager文件迁移到直接使用中央状态管理器',
                    '使用 window.OTA.state.* API 替代 appState.* API',
                    '测试迁移后的功能确保兼容性'
                ]
            };
        }

        /**
         * 日志记录
         * @param {string} message - 消息
         * @param {string} level - 日志级别
         */
        log(message, level = 'info') {
            const prefix = '[StateManagerAdapter]';
            if (this.logger && this.logger.log) {
                this.logger.log(`${prefix} ${message}`, level);
            } else {
                console.log(`${prefix} ${message}`);
            }
        }
    }

    // 创建全局实例
    const stateManagerAdapter = new StateManagerAdapter();

    // 导出到OTA命名空间
    window.OTA.stateManagerAdapter = stateManagerAdapter;

    // 提供便捷的全局访问函数
    window.getStateManagerAdapter = function() {
        return window.OTA.stateManagerAdapter;
    };

    // 替换现有的getAppState函数，使其返回适配器
    const originalGetAppState = window.getAppState;
    window.getAppState = function() {
        // 如果适配器未初始化，先初始化
        if (!stateManagerAdapter.initialized) {
            stateManagerAdapter.init();
        }
        return stateManagerAdapter;
    };

    // 保留原始的AppState访问方式（用于调试）
    window.getOriginalAppState = originalGetAppState;

    console.log('✅ 状态管理器适配器已加载');

})();
