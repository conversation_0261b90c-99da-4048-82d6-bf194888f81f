/**
 * 依赖容器 - 统一依赖管理
 * 解决当前系统中多重依赖获取方式的混乱问题
 * 
 * 使用方式:
 * 1. 注册依赖: container.register('serviceName', factory)
 * 2. 获取依赖: container.get('serviceName')
 * 3. 检查状态: container.isInitialized()
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 依赖容器类
     * 管理所有系统依赖的注册、创建和获取
     */
    class DependencyContainer {
        constructor() {
            this.services = new Map();
            this.instances = new Map();
            this.initialized = false;
            this.initializationOrder = [];
            this.logger = null; // 延迟获取logger避免循环依赖
        }

        /**
         * 注册服务工厂函数
         * @param {string} name - 服务名称
         * @param {Function} factory - 工厂函数
         * @param {Object} options - 选项
         */
        register(name, factory, options = {}) {
            if (this.services.has(name)) {
                this.log(`警告: 服务 ${name} 已存在，将被覆盖`, 'warning');
            }

            this.services.set(name, {
                factory,
                singleton: options.singleton !== false, // 默认单例
                dependencies: options.dependencies || [],
                initialized: false
            });

            this.log(`已注册服务: ${name}`, 'info');
        }

        /**
         * 获取服务实例
         * @param {string} name - 服务名称
         * @returns {any} 服务实例
         */
        get(name) {
            // 检查是否已有实例
            if (this.instances.has(name)) {
                return this.instances.get(name);
            }

            // 检查服务是否已注册
            if (!this.services.has(name)) {
                throw new Error(`服务 ${name} 未注册`);
            }

            const serviceConfig = this.services.get(name);
            
            // 检查循环依赖
            if (this.initializationOrder.includes(name)) {
                throw new Error(`检测到循环依赖: ${this.initializationOrder.join(' -> ')} -> ${name}`);
            }

            // 添加到初始化顺序
            this.initializationOrder.push(name);

            try {
                // 创建实例
                const instance = serviceConfig.factory();
                
                // 如果是单例，缓存实例
                if (serviceConfig.singleton) {
                    this.instances.set(name, instance);
                }

                serviceConfig.initialized = true;
                this.log(`已创建服务实例: ${name}`, 'info');

                return instance;
            } catch (error) {
                this.log(`创建服务 ${name} 失败: ${error.message}`, 'error');
                throw error;
            } finally {
                // 从初始化顺序中移除
                this.initializationOrder.pop();
            }
        }

        /**
         * 检查服务是否已注册
         * @param {string} name - 服务名称
         * @returns {boolean}
         */
        has(name) {
            return this.services.has(name);
        }

        /**
         * 获取所有已注册的服务名称
         * @returns {string[]}
         */
        getRegisteredServices() {
            return Array.from(this.services.keys());
        }

        /**
         * 获取所有已创建的实例名称
         * @returns {string[]}
         */
        getCreatedInstances() {
            return Array.from(this.instances.keys());
        }

        /**
         * 清除所有实例（保留注册信息）
         */
        clearInstances() {
            this.instances.clear();
            this.services.forEach(service => {
                service.initialized = false;
            });
            this.log('已清除所有服务实例', 'info');
        }

        /**
         * 重置容器（清除所有注册和实例）
         */
        reset() {
            this.services.clear();
            this.instances.clear();
            this.initialized = false;
            this.initializationOrder = [];
            this.log('依赖容器已重置', 'info');
        }

        /**
         * 初始化所有已注册的服务
         */
        initializeAll() {
            const serviceNames = this.getRegisteredServices();
            const results = [];

            for (const name of serviceNames) {
                try {
                    this.get(name);
                    results.push({ name, success: true });
                } catch (error) {
                    results.push({ name, success: false, error: error.message });
                    this.log(`初始化服务 ${name} 失败: ${error.message}`, 'error');
                }
            }

            this.initialized = true;
            this.log(`批量初始化完成，成功: ${results.filter(r => r.success).length}/${results.length}`, 'info');
            
            return results;
        }

        /**
         * 获取容器状态信息
         * @returns {Object}
         */
        getStatus() {
            return {
                initialized: this.initialized,
                registeredServices: this.getRegisteredServices().length,
                createdInstances: this.getCreatedInstances().length,
                services: this.getRegisteredServices(),
                instances: this.getCreatedInstances()
            };
        }

        /**
         * 日志记录（延迟获取logger避免循环依赖）
         * @param {string} message - 消息
         * @param {string} level - 日志级别
         */
        log(message, level = 'info') {
            // 延迟获取logger
            if (!this.logger && this.instances.has('logger')) {
                this.logger = this.instances.get('logger');
            }

            if (this.logger && typeof this.logger.log === 'function') {
                this.logger.log(`[DependencyContainer] ${message}`, level);
            } else {
                // 降级到console
                console.log(`[DependencyContainer] ${message}`);
            }
        }

        /**
         * 诊断依赖关系
         * @returns {Object} 诊断结果
         */
        diagnose() {
            const diagnosis = {
                healthy: true,
                issues: [],
                recommendations: []
            };

            // 检查未初始化的服务
            const uninitialized = [];
            this.services.forEach((config, name) => {
                if (!config.initialized && !this.instances.has(name)) {
                    uninitialized.push(name);
                }
            });

            if (uninitialized.length > 0) {
                diagnosis.issues.push(`未初始化的服务: ${uninitialized.join(', ')}`);
                diagnosis.recommendations.push('考虑调用 initializeAll() 或按需初始化服务');
            }

            // 检查孤立实例（已创建但未注册）
            const orphaned = [];
            this.instances.forEach((instance, name) => {
                if (!this.services.has(name)) {
                    orphaned.push(name);
                }
            });

            if (orphaned.length > 0) {
                diagnosis.issues.push(`孤立实例: ${orphaned.join(', ')}`);
                diagnosis.recommendations.push('清理未注册的实例或补充注册信息');
                diagnosis.healthy = false;
            }

            return diagnosis;
        }
    }

    // 创建全局唯一的依赖容器实例
    const container = new DependencyContainer();

    // 暴露到OTA命名空间
    window.OTA.container = container;

    // 提供便捷的全局函数
    window.OTA.getService = function(name) {
        return container.get(name);
    };

    window.OTA.registerService = function(name, factory, options) {
        return container.register(name, factory, options);
    };

    // 向后兼容：暴露到全局
    window.getService = window.OTA.getService;
    window.registerService = window.OTA.registerService;

    console.log('✅ 依赖容器已初始化');

})();
