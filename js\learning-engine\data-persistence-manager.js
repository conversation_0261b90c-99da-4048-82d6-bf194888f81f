/**
 * 智能学习型格式预处理引擎 - 数据持久化管理器
 * 负责数据压缩、导入导出、完整性检查等高级持久化功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return getService('learningConfig');
    }

    function getLearningStorageManager() {
        return getService('learningStorageManager');
    }

    function getLogger() {
        return getService('logger');
    }

    // 服务定位器函数
    function getService(serviceName) {
        const serviceMap = {
            'learningConfig': () => window.OTA.learningConfig || window.learningConfig,
            'learningStorageManager': () => window.OTA.learningStorageManager || window.learningStorageManager,
            'logger': () => window.OTA.logger || window.logger
        };
        
        const getter = serviceMap[serviceName];
        return getter ? getter() : null;
    }

    /**
     * 数据持久化管理器类
     * 提供高级的数据持久化功能
     */
    class DataPersistenceManager {
        constructor() {
            this.config = getLearningConfig();
            this.storageManager = getLearningStorageManager();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            
            // 压缩配置
            this.compressionConfig = {
                enabled: this.config.get('storage.compressionEnabled'),
                threshold: 1024, // 1KB以上的数据才压缩
                algorithm: 'lz-string' // 使用LZ-String算法
            };

            // 完整性检查配置
            this.integrityConfig = {
                enabled: true,
                checksumAlgorithm: 'simple-hash',
                validationInterval: 24 * 60 * 60 * 1000 // 24小时
            };

            // 备份配置
            this.backupConfig = {
                enabled: this.config.get('storage.backupEnabled'),
                maxBackups: 5,
                autoBackupInterval: 7 * 24 * 60 * 60 * 1000 // 7天
            };

            this.initialize();
        }

        /**
         * 初始化数据持久化管理器
         */
        initialize() {
            try {
                // 检查数据完整性
                this.performIntegrityCheck();
                
                // 设置自动备份
                this.setupAutoBackup();
                
                // 清理过期数据
                this.cleanupExpiredData();
                
                this.logger?.log('数据持久化管理器初始化完成', 'info', {
                    version: this.version,
                    compression: this.compressionConfig.enabled,
                    backup: this.backupConfig.enabled
                });

            } catch (error) {
                this.logger?.logError('数据持久化管理器初始化失败', error);
            }
        }

        /**
         * 压缩数据
         * @param {string} data - 原始数据
         * @returns {string} 压缩后的数据
         */
        compressData(data) {
            try {
                if (!this.compressionConfig.enabled || !data) {
                    return data;
                }

                if (data.length < this.compressionConfig.threshold) {
                    return data;
                }

                // 使用简单的压缩算法（实际项目中可以使用LZ-String等库）
                const compressed = this.simpleCompress(data);
                
                this.logger?.log('数据压缩完成', 'info', {
                    originalSize: data.length,
                    compressedSize: compressed.length,
                    ratio: (compressed.length / data.length * 100).toFixed(2) + '%'
                });

                return compressed;

            } catch (error) {
                this.logger?.logError('数据压缩失败', error);
                return data;
            }
        }

        /**
         * 解压数据
         * @param {string} compressedData - 压缩的数据
         * @returns {string} 解压后的数据
         */
        decompressData(compressedData) {
            try {
                if (!this.compressionConfig.enabled || !compressedData) {
                    return compressedData;
                }

                // 检查是否为压缩数据
                if (!this.isCompressedData(compressedData)) {
                    return compressedData;
                }

                const decompressed = this.simpleDecompress(compressedData);
                return decompressed;

            } catch (error) {
                this.logger?.logError('数据解压失败', error);
                return compressedData;
            }
        }

        /**
         * 简单压缩算法（基于重复字符压缩）
         */
        simpleCompress(data) {
            // 标记为压缩数据
            let compressed = 'COMPRESSED:';
            
            // 简单的RLE压缩
            let result = '';
            let count = 1;
            let current = data[0];
            
            for (let i = 1; i < data.length; i++) {
                if (data[i] === current && count < 9) {
                    count++;
                } else {
                    result += count > 1 ? `${count}${current}` : current;
                    current = data[i];
                    count = 1;
                }
            }
            result += count > 1 ? `${count}${current}` : current;
            
            return compressed + result;
        }

        /**
         * 简单解压算法
         */
        simpleDecompress(compressedData) {
            if (!compressedData.startsWith('COMPRESSED:')) {
                return compressedData;
            }
            
            const data = compressedData.substring(11); // 移除'COMPRESSED:'前缀
            let result = '';
            
            for (let i = 0; i < data.length; i++) {
                const char = data[i];
                if (/\d/.test(char) && i + 1 < data.length) {
                    const count = parseInt(char);
                    const repeatChar = data[i + 1];
                    result += repeatChar.repeat(count);
                    i++; // 跳过下一个字符
                } else {
                    result += char;
                }
            }
            
            return result;
        }

        /**
         * 检查是否为压缩数据
         */
        isCompressedData(data) {
            return typeof data === 'string' && data.startsWith('COMPRESSED:');
        }

        /**
         * 计算数据校验和
         * @param {string} data - 数据
         * @returns {string} 校验和
         */
        calculateChecksum(data) {
            if (!data) return '';
            
            let hash = 0;
            for (let i = 0; i < data.length; i++) {
                const char = data.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return hash.toString(16);
        }

        /**
         * 验证数据完整性
         * @param {string} data - 数据
         * @param {string} expectedChecksum - 期望的校验和
         * @returns {boolean} 验证结果
         */
        verifyIntegrity(data, expectedChecksum) {
            const actualChecksum = this.calculateChecksum(data);
            return actualChecksum === expectedChecksum;
        }

        /**
         * 执行完整性检查
         */
        performIntegrityCheck() {
            try {
                if (!this.integrityConfig.enabled) {
                    return;
                }

                const storageKeys = this.config.get('storage.keys');
                let corruptedCount = 0;
                let checkedCount = 0;

                Object.entries(storageKeys).forEach(([name, key]) => {
                    try {
                        const data = this.storageManager.getData(key);
                        if (data) {
                            checkedCount++;
                            
                            // 检查数据结构完整性
                            if (!this.validateDataStructure(data, name)) {
                                corruptedCount++;
                                this.logger?.log(`数据结构验证失败: ${name}`, 'warn');
                            }
                        }
                    } catch (error) {
                        corruptedCount++;
                        this.logger?.logError(`完整性检查失败: ${name}`, error);
                    }
                });

                this.logger?.log('数据完整性检查完成', 'info', {
                    checked: checkedCount,
                    corrupted: corruptedCount,
                    integrity: corruptedCount === 0 ? 'good' : 'issues_found'
                });

                if (corruptedCount > 0) {
                    this.handleDataCorruption(corruptedCount);
                }

            } catch (error) {
                this.logger?.logError('执行完整性检查失败', error);
            }
        }

        /**
         * 验证数据结构
         */
        validateDataStructure(data, dataType) {
            try {
                switch (dataType) {
                    case 'userOperations':
                        return data.version && Array.isArray(data.operations) && data.metadata;
                    case 'learningRules':
                        return data.version && Array.isArray(data.rules) && data.metadata;
                    case 'systemStats':
                        return data.version && data.stats && data.history;
                    case 'userPreferences':
                        return data.version && data.preferences;
                    default:
                        return true; // 未知类型默认通过
                }
            } catch (error) {
                return false;
            }
        }

        /**
         * 处理数据损坏
         */
        handleDataCorruption(corruptedCount) {
            this.logger?.log(`检测到 ${corruptedCount} 个损坏的数据项，尝试修复`, 'warn');
            
            // 尝试从备份恢复
            this.restoreFromBackup();
        }

        /**
         * 创建数据备份
         */
        createBackup() {
            try {
                if (!this.backupConfig.enabled) {
                    return;
                }

                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const backupKey = `${this.config.get('storage.keyPrefix')}backup-${timestamp}`;
                
                // 导出所有数据
                const allData = this.storageManager.exportAllData();
                
                // 压缩备份数据
                const compressedData = this.compressData(JSON.stringify(allData));
                
                // 添加完整性校验
                const checksum = this.calculateChecksum(compressedData);
                const backupData = {
                    data: compressedData,
                    checksum: checksum,
                    timestamp: timestamp,
                    version: this.version
                };

                localStorage.setItem(backupKey, JSON.stringify(backupData));
                
                // 清理旧备份
                this.cleanupOldBackups();
                
                this.logger?.log('数据备份创建完成', 'success', {
                    backupKey: backupKey,
                    dataSize: compressedData.length
                });

                return backupKey;

            } catch (error) {
                this.logger?.logError('创建数据备份失败', error);
                return null;
            }
        }

        /**
         * 从备份恢复数据
         */
        restoreFromBackup(backupKey = null) {
            try {
                // 如果没有指定备份，查找最新的备份
                if (!backupKey) {
                    backupKey = this.findLatestBackup();
                }

                if (!backupKey) {
                    this.logger?.log('没有找到可用的备份', 'warn');
                    return false;
                }

                const backupData = JSON.parse(localStorage.getItem(backupKey));
                if (!backupData) {
                    this.logger?.log('备份数据不存在', 'error');
                    return false;
                }

                // 验证备份完整性
                if (!this.verifyIntegrity(backupData.data, backupData.checksum)) {
                    this.logger?.log('备份数据完整性验证失败', 'error');
                    return false;
                }

                // 解压数据
                const decompressedData = this.decompressData(backupData.data);
                const restoredData = JSON.parse(decompressedData);

                // 导入数据
                const success = this.storageManager.importData(restoredData);
                
                if (success) {
                    this.logger?.log('数据恢复完成', 'success', {
                        backupKey: backupKey,
                        timestamp: backupData.timestamp
                    });
                } else {
                    this.logger?.log('数据恢复失败', 'error');
                }

                return success;

            } catch (error) {
                this.logger?.logError('从备份恢复数据失败', error);
                return false;
            }
        }

        /**
         * 查找最新的备份
         */
        findLatestBackup() {
            const prefix = `${this.config.get('storage.keyPrefix')}backup-`;
            const backups = [];

            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    backups.push(key);
                }
            }

            // 按时间戳排序，返回最新的
            backups.sort((a, b) => {
                const timeA = a.substring(prefix.length);
                const timeB = b.substring(prefix.length);
                return timeB.localeCompare(timeA);
            });

            return backups[0] || null;
        }

        /**
         * 清理旧备份
         */
        cleanupOldBackups() {
            try {
                const prefix = `${this.config.get('storage.keyPrefix')}backup-`;
                const backups = [];

                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith(prefix)) {
                        backups.push(key);
                    }
                }

                // 按时间戳排序
                backups.sort((a, b) => {
                    const timeA = a.substring(prefix.length);
                    const timeB = b.substring(prefix.length);
                    return timeB.localeCompare(timeA);
                });

                // 删除超出限制的备份
                const maxBackups = this.backupConfig.maxBackups;
                if (backups.length > maxBackups) {
                    const toDelete = backups.slice(maxBackups);
                    toDelete.forEach(key => {
                        localStorage.removeItem(key);
                    });

                    this.logger?.log(`清理了 ${toDelete.length} 个旧备份`, 'info');
                }

            } catch (error) {
                this.logger?.logError('清理旧备份失败', error);
            }
        }

        /**
         * 设置自动备份
         */
        setupAutoBackup() {
            if (!this.backupConfig.enabled) {
                return;
            }

            // 设置定期备份
            setInterval(() => {
                this.createBackup();
            }, this.backupConfig.autoBackupInterval);

            this.logger?.log('自动备份已设置', 'info', {
                interval: this.backupConfig.autoBackupInterval / (24 * 60 * 60 * 1000) + ' 天'
            });
        }

        /**
         * 清理过期数据
         */
        cleanupExpiredData() {
            try {
                const retentionDays = this.config.get('storage.retentionDays');
                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

                let cleanedCount = 0;

                // 清理用户操作记录
                const operationsKey = this.config.get('storage.keys.userOperations');
                const operationsData = this.storageManager.getData(operationsKey);
                
                if (operationsData && operationsData.operations) {
                    const originalCount = operationsData.operations.length;
                    operationsData.operations = operationsData.operations.filter(op => 
                        new Date(op.timestamp) > cutoffDate
                    );
                    cleanedCount += originalCount - operationsData.operations.length;
                    
                    if (cleanedCount > 0) {
                        this.storageManager.setData(operationsKey, operationsData);
                    }
                }

                if (cleanedCount > 0) {
                    this.logger?.log(`清理了 ${cleanedCount} 条过期数据`, 'info');
                }

            } catch (error) {
                this.logger?.logError('清理过期数据失败', error);
            }
        }

        /**
         * 导出学习数据
         * @param {Object} options - 导出选项
         * @returns {Object} 导出的数据
         */
        exportLearningData(options = {}) {
            try {
                const exportData = this.storageManager.exportAllData();
                
                // 添加导出元数据
                exportData.exportMetadata = {
                    exportedAt: new Date().toISOString(),
                    exportedBy: 'DataPersistenceManager',
                    version: this.version,
                    options: options
                };

                // 如果需要压缩
                if (options.compress) {
                    const compressed = this.compressData(JSON.stringify(exportData));
                    return {
                        compressed: true,
                        data: compressed,
                        checksum: this.calculateChecksum(compressed)
                    };
                }

                return exportData;

            } catch (error) {
                this.logger?.logError('导出学习数据失败', error);
                return null;
            }
        }

        /**
         * 导入学习数据
         * @param {Object} importData - 导入的数据
         * @returns {boolean} 导入结果
         */
        importLearningData(importData) {
            try {
                let dataToImport = importData;

                // 如果是压缩数据，先解压
                if (importData.compressed) {
                    // 验证完整性
                    if (!this.verifyIntegrity(importData.data, importData.checksum)) {
                        throw new Error('导入数据完整性验证失败');
                    }
                    
                    const decompressed = this.decompressData(importData.data);
                    dataToImport = JSON.parse(decompressed);
                }

                // 验证数据格式
                if (!dataToImport.version || !dataToImport.data) {
                    throw new Error('无效的导入数据格式');
                }

                // 创建备份
                this.createBackup();

                // 导入数据
                const success = this.storageManager.importData(dataToImport);
                
                if (success) {
                    this.logger?.log('学习数据导入完成', 'success');
                } else {
                    this.logger?.log('学习数据导入失败', 'error');
                }

                return success;

            } catch (error) {
                this.logger?.logError('导入学习数据失败', error);
                return false;
            }
        }

        /**
         * 获取存储统计信息
         */
        getStorageStats() {
            const stats = this.storageManager.getStorageUsage();
            
            // 添加持久化相关统计
            stats.persistence = {
                compressionEnabled: this.compressionConfig.enabled,
                backupEnabled: this.backupConfig.enabled,
                lastBackup: this.getLastBackupTime(),
                backupCount: this.getBackupCount(),
                integrityStatus: 'good' // 简化状态
            };

            return stats;
        }

        /**
         * 获取最后备份时间
         */
        getLastBackupTime() {
            const latestBackup = this.findLatestBackup();
            if (!latestBackup) {
                return null;
            }

            const prefix = `${this.config.get('storage.keyPrefix')}backup-`;
            const timestamp = latestBackup.substring(prefix.length);
            return timestamp.replace(/-/g, ':');
        }

        /**
         * 获取备份数量
         */
        getBackupCount() {
            const prefix = `${this.config.get('storage.keyPrefix')}backup-`;
            let count = 0;

            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    count++;
                }
            }

            return count;
        }
    }

    // 创建全局实例
    const dataPersistenceManager = new DataPersistenceManager();

    // 导出到全局命名空间
    window.OTA.dataPersistenceManager = dataPersistenceManager;
    window.dataPersistenceManager = dataPersistenceManager; // 向后兼容

    // 工厂函数
    window.getDataPersistenceManager = function() {
        return window.OTA.dataPersistenceManager || window.dataPersistenceManager;
    };

    console.log('数据持久化管理器加载完成', {
        version: dataPersistenceManager.version,
        compression: dataPersistenceManager.compressionConfig.enabled,
        backup: dataPersistenceManager.backupConfig.enabled
    });

})();
