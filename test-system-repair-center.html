<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统 - 系统修复中心测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            color: #764ba2;
            text-align: center;
            margin-bottom: 30px;
        }
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        button.success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #764ba2;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
            font-size: 0.9em;
        }
        .results-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .result-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .result-healthy {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .result-critical {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 0.85em;
        }
        .section-title {
            color: #764ba2;
            border-bottom: 2px solid #764ba2;
            padding-bottom: 5px;
            margin: 20px 0 10px 0;
        }
        .toggle-section {
            margin: 10px 0;
        }
        .toggle-section input[type="checkbox"] {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OTA系统修复中心测试</h1>
        
        <div class="control-panel">
            <button onclick="initializeRepairCenter()">🚀 初始化修复中心</button>
            <button onclick="runFullDiagnostic()">🔍 完整系统诊断</button>
            <button onclick="performAutoRepair()" class="success">🔧 自动修复</button>
            <button onclick="getRepairCenterStatus()">📊 获取状态</button>
            <button onclick="testSpecificTool()">🛠️ 测试特定工具</button>
            <button onclick="getRepairHistory()">📋 修复历史</button>
            <button onclick="toggleAutoRepair()">⚙️ 切换自动修复</button>
            <button onclick="clearConsole()" class="danger">🧹 清空控制台</button>
        </div>

        <div class="toggle-section">
            <label>
                <input type="checkbox" id="autoRepairToggle"> 启用自动修复
            </label>
        </div>

        <div class="stats" id="repairStats">
            <div class="stat-card">
                <div class="stat-number" id="totalTools">0</div>
                <div class="stat-label">修复工具</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="diagnosticTools">0</div>
                <div class="stat-label">诊断工具</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="healthChecks">0</div>
                <div class="stat-label">健康检查</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="repairHistory">0</div>
                <div class="stat-label">修复历史</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="systemHealth">未知</div>
                <div class="stat-label">系统健康度</div>
            </div>
        </div>

        <div class="results-section">
            <h3 class="section-title">📋 测试控制台</h3>
            <div id="console"></div>
        </div>

        <div class="results-section">
            <h3 class="section-title">🔍 诊断结果</h3>
            <div id="diagnosticResults"></div>
        </div>

        <div class="results-section">
            <h3 class="section-title">🔧 修复结果</h3>
            <div id="repairResults"></div>
        </div>
    </div>

    <!-- 加载核心架构模块 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/central-state-manager.js"></script>
    <script src="js/core/state-manager-adapter.js"></script>
    <script src="js/core/system-repair-center.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    
    <!-- 加载基础服务 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    
    <!-- 加载现有修复工具（如果存在） -->
    <script src="js/comprehensive-button-fix.js" onerror="console.log('按钮修复工具未找到')"></script>
    <script src="js/responsible-person-fix.js" onerror="console.log('负责人修复工具未找到')"></script>
    
    <script>
        // 自定义控制台输出
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
            const line = `[${timestamp}] ${prefix} ${message}\n`;
            consoleDiv.textContent += line;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            originalLog(message);
        }
        
        console.log = logToConsole;
        console.error = (msg) => logToConsole(msg, 'error');
        console.warn = (msg) => logToConsole(msg, 'warn');
        
        function clearConsole() {
            consoleDiv.textContent = '';
        }

        // 更新统计信息
        function updateStats() {
            const repairCenter = window.getSystemRepairCenter();
            if (repairCenter && repairCenter.initialized) {
                const status = repairCenter.getStatus();
                document.getElementById('totalTools').textContent = status.repairTools;
                document.getElementById('diagnosticTools').textContent = status.diagnosticTools;
                document.getElementById('healthChecks').textContent = status.healthChecks;
                document.getElementById('repairHistory').textContent = status.repairHistory;
                
                // 更新自动修复开关状态
                document.getElementById('autoRepairToggle').checked = status.autoRepairEnabled;
            }
        }

        // 初始化修复中心
        function initializeRepairCenter() {
            logToConsole('🚀 初始化系统修复中心...', 'info');
            
            try {
                const repairCenter = window.getSystemRepairCenter();
                if (repairCenter) {
                    if (!repairCenter.initialized) {
                        repairCenter.init();
                        logToConsole('✅ 系统修复中心初始化成功', 'success');
                    } else {
                        logToConsole('ℹ️ 系统修复中心已经初始化', 'info');
                    }
                    updateStats();
                } else {
                    logToConsole('❌ 系统修复中心不可用', 'error');
                }
            } catch (error) {
                logToConsole(`❌ 初始化失败: ${error.message}`, 'error');
            }
        }

        // 运行完整诊断
        async function runFullDiagnostic() {
            logToConsole('🔍 开始运行完整系统诊断...', 'info');
            
            try {
                const repairCenter = window.getSystemRepairCenter();
                if (!repairCenter || !repairCenter.initialized) {
                    logToConsole('❌ 修复中心未初始化', 'error');
                    return;
                }

                const report = await repairCenter.runFullDiagnostic();
                
                logToConsole(`📊 诊断完成，健康度: ${report.healthScore}%`, 
                    report.overall === 'healthy' ? 'success' : 'warn');

                // 显示诊断结果
                displayDiagnosticResults(report);
                
                // 更新健康度显示
                document.getElementById('systemHealth').textContent = report.healthScore + '%';
                
            } catch (error) {
                logToConsole(`❌ 诊断失败: ${error.message}`, 'error');
            }
        }

        // 执行自动修复
        async function performAutoRepair() {
            logToConsole('🔧 开始执行自动修复...', 'info');
            
            try {
                const repairCenter = window.getSystemRepairCenter();
                if (!repairCenter || !repairCenter.initialized) {
                    logToConsole('❌ 修复中心未初始化', 'error');
                    return;
                }

                // 启用自动修复（如果未启用）
                if (!repairCenter.autoRepairEnabled) {
                    repairCenter.enableAutoRepair();
                }

                const result = await repairCenter.performAutoRepair();
                
                const successRate = Math.round((result.fixedIssues / result.totalIssues) * 100);
                logToConsole(`🔧 自动修复完成，成功率: ${successRate}%`, 
                    successRate >= 80 ? 'success' : 'warn');

                // 显示修复结果
                displayRepairResults(result);
                updateStats();
                
            } catch (error) {
                logToConsole(`❌ 自动修复失败: ${error.message}`, 'error');
            }
        }

        // 获取修复中心状态
        function getRepairCenterStatus() {
            logToConsole('📊 获取修复中心状态...', 'info');
            
            try {
                const repairCenter = window.getSystemRepairCenter();
                if (repairCenter) {
                    const status = repairCenter.getStatus();
                    logToConsole(`📊 状态信息: ${JSON.stringify(status, null, 2)}`, 'info');
                    updateStats();
                } else {
                    logToConsole('❌ 修复中心不可用', 'error');
                }
            } catch (error) {
                logToConsole(`❌ 获取状态失败: ${error.message}`, 'error');
            }
        }

        // 测试特定工具
        async function testSpecificTool() {
            logToConsole('🛠️ 测试特定修复工具...', 'info');
            
            try {
                const repairCenter = window.getSystemRepairCenter();
                if (!repairCenter || !repairCenter.initialized) {
                    logToConsole('❌ 修复中心未初始化', 'error');
                    return;
                }

                // 测试按钮修复工具
                const result = await repairCenter.fixButtons();
                logToConsole(`🔧 按钮修复结果: ${result.message}`, 
                    result.success ? 'success' : 'error');
                
            } catch (error) {
                logToConsole(`❌ 工具测试失败: ${error.message}`, 'error');
            }
        }

        // 获取修复历史
        function getRepairHistory() {
            logToConsole('📋 获取修复历史...', 'info');
            
            try {
                const repairCenter = window.getSystemRepairCenter();
                if (repairCenter) {
                    const history = repairCenter.getRepairHistory(5);
                    if (history.length > 0) {
                        logToConsole(`📋 最近${history.length}次修复记录:`, 'info');
                        history.forEach((record, index) => {
                            logToConsole(`${index + 1}. ${record.timestamp} - 修复${record.fixedIssues}个问题`, 'info');
                        });
                    } else {
                        logToConsole('📋 暂无修复历史记录', 'info');
                    }
                } else {
                    logToConsole('❌ 修复中心不可用', 'error');
                }
            } catch (error) {
                logToConsole(`❌ 获取历史失败: ${error.message}`, 'error');
            }
        }

        // 切换自动修复
        function toggleAutoRepair() {
            try {
                const repairCenter = window.getSystemRepairCenter();
                if (repairCenter) {
                    const checkbox = document.getElementById('autoRepairToggle');
                    if (checkbox.checked) {
                        repairCenter.enableAutoRepair();
                        logToConsole('✅ 自动修复已启用', 'success');
                    } else {
                        repairCenter.disableAutoRepair();
                        logToConsole('⚠️ 自动修复已禁用', 'warn');
                    }
                    updateStats();
                } else {
                    logToConsole('❌ 修复中心不可用', 'error');
                }
            } catch (error) {
                logToConsole(`❌ 切换失败: ${error.message}`, 'error');
            }
        }

        // 显示诊断结果
        function displayDiagnosticResults(report) {
            const resultsDiv = document.getElementById('diagnosticResults');
            resultsDiv.innerHTML = '';

            // 整体状态
            const overallDiv = document.createElement('div');
            overallDiv.className = `result-item result-${report.overall}`;
            overallDiv.innerHTML = `<strong>整体健康度:</strong> ${report.healthScore}% (${report.overall})`;
            resultsDiv.appendChild(overallDiv);

            // 分类结果
            Object.values(report.categories).forEach(category => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = `result-item result-${category.status}`;
                categoryDiv.innerHTML = `<strong>${category.name}:</strong> ${category.status} (${category.checks.length}项检查)`;
                resultsDiv.appendChild(categoryDiv);
            });

            // 问题列表
            if (report.issues.length > 0) {
                const issuesTitle = document.createElement('div');
                issuesTitle.innerHTML = '<strong>发现的问题:</strong>';
                issuesTitle.className = 'result-item result-info';
                resultsDiv.appendChild(issuesTitle);

                report.issues.forEach(issue => {
                    const issueDiv = document.createElement('div');
                    issueDiv.className = `result-item result-${issue.severity === 'critical' ? 'critical' : 'warning'}`;
                    issueDiv.innerHTML = `• ${issue.message}`;
                    resultsDiv.appendChild(issueDiv);
                });
            }
        }

        // 显示修复结果
        function displayRepairResults(result) {
            const resultsDiv = document.getElementById('repairResults');
            resultsDiv.innerHTML = '';

            // 修复统计
            const statsDiv = document.createElement('div');
            statsDiv.className = 'result-item result-info';
            statsDiv.innerHTML = `<strong>修复统计:</strong> 总问题${result.totalIssues}个，已修复${result.fixedIssues}个，失败${result.failedFixes}个`;
            resultsDiv.appendChild(statsDiv);

            // 修复详情
            result.results.forEach(repair => {
                const repairDiv = document.createElement('div');
                repairDiv.className = `result-item result-${repair.success ? 'healthy' : 'critical'}`;
                repairDiv.innerHTML = `<strong>${repair.tool}:</strong> ${repair.message}`;
                resultsDiv.appendChild(repairDiv);
            });
        }

        // 自动修复开关事件
        document.getElementById('autoRepairToggle').addEventListener('change', toggleAutoRepair);

        // 页面加载完成后自动初始化
        window.addEventListener('load', () => {
            setTimeout(() => {
                logToConsole('🔧 系统修复中心测试页面已加载', 'info');
                logToConsole('点击"初始化修复中心"开始测试...', 'info');
            }, 500);
        });
    </script>
</body>
</html>
