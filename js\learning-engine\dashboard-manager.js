/**
 * 智能学习型格式预处理引擎 - 管理面板控制器
 * 负责管理面板的数据更新、用户交互、图表渲染等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 服务定位器函数
    function getService(serviceName) {
        const serviceMap = {
            'learningConfig': () => window.OTA?.learningConfig || window.learningConfig,
            'performanceMonitor': () => window.OTA?.performanceMonitor || window.performanceMonitor,
            'intelligentCacheManager': () => window.OTA?.intelligentCacheManager || window.intelligentCacheManager,
            'learningEffectivenessEvaluator': () => window.OTA?.learningEffectivenessEvaluator || window.learningEffectivenessEvaluator,
            'performanceOptimizer': () => window.OTA?.performanceOptimizer || window.performanceOptimizer,
            'logger': () => window.OTA?.logger || window.logger,
            'ruleGenerationEngine': () => window.OTA?.ruleGenerationEngine || window.ruleGenerationEngine,
            'predictiveCorrector': () => window.OTA?.predictiveCorrector || window.predictiveCorrector
        };
        
        const getter = serviceMap[serviceName];
        return getter ? getter() : null;
    }

    /**
     * 管理面板控制器类
     */
    class DashboardManager {
        constructor() {
            this.version = '1.0.0';
            this.updateInterval = 5000; // 5秒更新间隔
            this.charts = {};
            this.currentLogFilter = 'all';
            this.systemStartTime = Date.now();
            
            // 获取依赖模块
            this.config = getService('learningConfig');
            this.performanceMonitor = getService('performanceMonitor');
            this.cacheManager = getService('intelligentCacheManager');
            this.evaluator = getService('learningEffectivenessEvaluator');
            this.optimizer = getService('performanceOptimizer');
            this.logger = getService('logger');

            this.initialize();
        }

        /**
         * 初始化管理面板
         */
        initialize() {
            try {
                // 初始化图表
                this.initializeCharts();
                
                // 开始数据更新循环
                this.startDataUpdateLoop();
                
                // 初始化事件监听器
                this.setupEventListeners();
                
                // 加载初始数据
                this.loadInitialData();
                
                console.log('管理面板初始化完成', { version: this.version });

            } catch (error) {
                console.error('管理面板初始化失败', error);
            }
        }

        /**
         * 初始化图表
         */
        initializeCharts() {
            // 性能监控图表
            const performanceCtx = document.getElementById('performance-chart');
            if (performanceCtx) {
                this.charts.performance = new Chart(performanceCtx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '响应时间 (ms)',
                            data: [],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            tension: 0.4
                        }, {
                            label: '内存使用 (MB)',
                            data: [],
                            borderColor: '#ed8936',
                            backgroundColor: 'rgba(237, 137, 54, 0.1)',
                            tension: 0.4,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: { display: true, text: '响应时间 (ms)' }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: { display: true, text: '内存使用 (MB)' },
                                grid: { drawOnChartArea: false }
                            }
                        },
                        plugins: {
                            legend: { display: true, position: 'top' }
                        }
                    }
                });
            }

            // 缓存统计图表
            const cacheCtx = document.getElementById('cache-chart');
            if (cacheCtx) {
                this.charts.cache = new Chart(cacheCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['命中', '未命中'],
                        datasets: [{
                            data: [0, 0],
                            backgroundColor: ['#48bb78', '#f56565'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: true, position: 'bottom' }
                        }
                    }
                });
            }
        }

        /**
         * 开始数据更新循环
         */
        startDataUpdateLoop() {
            setInterval(() => {
                this.updateDashboardData();
            }, this.updateInterval);
        }

        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 配置变更监听
            const cacheLimit = document.getElementById('cache-limit');
            if (cacheLimit) {
                cacheLimit.addEventListener('change', () => {
                    this.updateCacheLimit(parseInt(cacheLimit.value));
                });
            }

            const retentionDays = document.getElementById('retention-days');
            if (retentionDays) {
                retentionDays.addEventListener('change', () => {
                    this.updateRetentionDays(parseInt(retentionDays.value));
                });
            }
        }

        /**
         * 加载初始数据
         */
        loadInitialData() {
            this.updateSystemStatus();
            this.updateLearningMetrics();
            this.updatePerformanceMetrics();
            this.updateCacheMetrics();
            this.updateSystemLogs();
        }

        /**
         * 更新面板数据
         */
        updateDashboardData() {
            try {
                this.updateSystemStatus();
                this.updateLearningMetrics();
                this.updatePerformanceMetrics();
                this.updateCacheMetrics();
                this.updateCharts();
                this.checkForAlerts();
            } catch (error) {
                console.error('更新面板数据失败', error);
            }
        }

        /**
         * 更新系统状态
         */
        updateSystemStatus() {
            try {
                // 运行时间
                const uptime = Date.now() - this.systemStartTime;
                const uptimeText = this.formatUptime(uptime);
                this.updateElement('uptime', uptimeText);

                // 总操作数
                const totalOps = this.performanceMonitor?.getRealTimeMetrics()?.totalOperations || 0;
                this.updateElement('total-operations', totalOps.toLocaleString());

                // 成功率
                const metrics = this.performanceMonitor?.getRealTimeMetrics();
                if (metrics && metrics.totalOperations > 0) {
                    const successRate = ((metrics.totalOperations - metrics.totalErrors) / metrics.totalOperations * 100).toFixed(1);
                    this.updateElement('success-rate', successRate + '%');
                } else {
                    this.updateElement('success-rate', '--');
                }

            } catch (error) {
                console.error('更新系统状态失败', error);
            }
        }

        /**
         * 更新学习指标
         */
        updateLearningMetrics() {
            try {
                // 获取最新评估结果
                const evaluation = this.evaluator?.getCurrentEvaluation();
                
                if (evaluation) {
                    // 学习分数
                    const score = (evaluation.overallScore * 100).toFixed(1);
                    this.updateElement('learning-score', score + '%');
                    
                    // 准确率
                    const accuracy = evaluation.metrics?.accuracy?.score;
                    if (accuracy !== undefined) {
                        this.updateElement('accuracy-rate', (accuracy * 100).toFixed(1) + '%');
                    }
                    
                    // 更新学习进度条
                    const progressBar = document.getElementById('learning-progress');
                    if (progressBar) {
                        progressBar.style.width = score + '%';
                    }
                } else {
                    this.updateElement('learning-score', '--');
                    this.updateElement('accuracy-rate', '--');
                }

                // 规则数量
                const ruleEngine = getService('ruleGenerationEngine');
                if (ruleEngine) {
                    const ruleCount = ruleEngine.getAllRules().length;
                    this.updateElement('rule-count', ruleCount.toLocaleString());
                }

                // 预测准确率
                const predictor = getService('predictiveCorrector');
                if (predictor) {
                    const stats = predictor.getPredictionStats();
                    if (stats.totalPredictions > 0) {
                        const predictionAccuracy = (stats.successfulPredictions / stats.totalPredictions * 100).toFixed(1);
                        this.updateElement('prediction-accuracy', predictionAccuracy + '%');
                    }
                }

            } catch (error) {
                console.error('更新学习指标失败', error);
            }
        }

        /**
         * 更新性能指标
         */
        updatePerformanceMetrics() {
            try {
                const metrics = this.performanceMonitor?.getRealTimeMetrics();
                
                if (metrics) {
                    // 平均响应时间
                    const responseTime = metrics.averageResponseTime || 0;
                    this.updateElement('response-time', responseTime.toFixed(0) + 'ms');
                    
                    // 错误率
                    const errorRate = metrics.totalOperations > 0 ? 
                        (metrics.totalErrors / metrics.totalOperations * 100).toFixed(2) : '0.00';
                    this.updateElement('error-rate', errorRate + '%');
                }

                // 内存使用
                if (performance.memory) {
                    const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                    this.updateElement('memory-usage', memoryMB + 'MB');
                }

                // 缓存命中率
                if (this.cacheManager) {
                    const cacheStats = this.cacheManager.getStats();
                    const hitRate = (cacheStats.hitRate * 100).toFixed(1);
                    this.updateElement('cache-hit-rate', hitRate + '%');
                }

            } catch (error) {
                console.error('更新性能指标失败', error);
            }
        }

        /**
         * 更新缓存指标
         */
        updateCacheMetrics() {
            try {
                if (!this.cacheManager) return;

                const stats = this.cacheManager.getStats();
                
                // 缓存大小
                const totalSize = stats.cacheSize.memory + stats.cacheSize.session + stats.cacheSize.persistent;
                this.updateElement('cache-size', totalSize.toLocaleString());
                
                // 缓存命中和未命中
                this.updateElement('cache-hits', stats.hits.toLocaleString());
                this.updateElement('cache-misses', stats.misses.toLocaleString());
                
                // 预加载队列
                this.updateElement('preload-queue', stats.preloadQueueSize.toLocaleString());

            } catch (error) {
                console.error('更新缓存指标失败', error);
            }
        }

        /**
         * 更新图表
         */
        updateCharts() {
            try {
                this.updatePerformanceChart();
                this.updateCacheChart();
            } catch (error) {
                console.error('更新图表失败', error);
            }
        }

        /**
         * 更新性能图表
         */
        updatePerformanceChart() {
            const chart = this.charts.performance;
            if (!chart || !this.performanceMonitor) return;

            const now = new Date();
            const timeLabel = now.toLocaleTimeString();
            
            const metrics = this.performanceMonitor.getRealTimeMetrics();
            const responseTime = metrics?.averageResponseTime || 0;
            const memoryMB = performance.memory ? 
                performance.memory.usedJSHeapSize / 1024 / 1024 : 0;

            // 添加新数据点
            chart.data.labels.push(timeLabel);
            chart.data.datasets[0].data.push(responseTime);
            chart.data.datasets[1].data.push(memoryMB);

            // 限制数据点数量
            const maxPoints = 20;
            if (chart.data.labels.length > maxPoints) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
                chart.data.datasets[1].data.shift();
            }

            chart.update('none');
        }

        /**
         * 更新缓存图表
         */
        updateCacheChart() {
            const chart = this.charts.cache;
            if (!chart || !this.cacheManager) return;

            const stats = this.cacheManager.getStats();
            chart.data.datasets[0].data = [stats.hits, stats.misses];
            chart.update('none');
        }

        /**
         * 更新系统日志
         */
        updateSystemLogs() {
            try {
                const logContainer = document.getElementById('log-container');
                if (!logContainer) return;

                // 模拟日志数据（实际应该从日志系统获取）
                const logs = this.getRecentLogs();
                
                logContainer.innerHTML = '';
                logs.forEach(log => {
                    const logEntry = document.createElement('div');
                    logEntry.className = `log-entry log-${log.level}`;
                    logEntry.innerHTML = `
                        <span style="color: #718096;">[${log.timestamp}]</span>
                        <span style="font-weight: 600;">[${log.level.toUpperCase()}]</span>
                        ${log.message}
                    `;
                    logContainer.appendChild(logEntry);
                });

                // 滚动到底部
                logContainer.scrollTop = logContainer.scrollHeight;

            } catch (error) {
                console.error('更新系统日志失败', error);
            }
        }

        /**
         * 检查报警
         */
        checkForAlerts() {
            try {
                const alertsContainer = document.getElementById('alerts-container');
                if (!alertsContainer) return;

                // 清除旧的报警
                alertsContainer.innerHTML = '';

                // 检查各种报警条件
                const alerts = [];

                // 内存使用报警
                if (performance.memory) {
                    const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
                    if (memoryUsage > 100) {
                        alerts.push({
                            type: 'warning',
                            message: `内存使用过高: ${memoryUsage.toFixed(1)}MB`
                        });
                    }
                }

                // 缓存命中率报警
                if (this.cacheManager) {
                    const stats = this.cacheManager.getStats();
                    if (stats.hitRate < 0.7) {
                        alerts.push({
                            type: 'warning',
                            message: `缓存命中率过低: ${(stats.hitRate * 100).toFixed(1)}%`
                        });
                    }
                }

                // 显示报警
                alerts.forEach(alert => {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = `alert alert-${alert.type}`;
                    alertDiv.textContent = alert.message;
                    alertsContainer.appendChild(alertDiv);
                });

            } catch (error) {
                console.error('检查报警失败', error);
            }
        }

        /**
         * 获取最近的日志
         */
        getRecentLogs() {
            // 模拟日志数据
            const now = new Date();
            return [
                {
                    timestamp: new Date(now.getTime() - 5000).toLocaleTimeString(),
                    level: 'info',
                    message: '学习系统正常运行'
                },
                {
                    timestamp: new Date(now.getTime() - 15000).toLocaleTimeString(),
                    level: 'success',
                    message: '性能优化完成，响应时间提升15%'
                },
                {
                    timestamp: new Date(now.getTime() - 30000).toLocaleTimeString(),
                    level: 'info',
                    message: '新增学习规则: 地址格式标准化'
                },
                {
                    timestamp: new Date(now.getTime() - 45000).toLocaleTimeString(),
                    level: 'warning',
                    message: '缓存命中率下降，建议优化缓存策略'
                },
                {
                    timestamp: new Date(now.getTime() - 60000).toLocaleTimeString(),
                    level: 'info',
                    message: '用户操作记录: 手动更正客户姓名'
                }
            ];
        }

        /**
         * 更新元素内容
         */
        updateElement(id, content) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = content;
            }
        }

        /**
         * 格式化运行时间
         */
        formatUptime(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (days > 0) {
                return `${days}天 ${hours % 24}小时`;
            } else if (hours > 0) {
                return `${hours}小时 ${minutes % 60}分钟`;
            } else if (minutes > 0) {
                return `${minutes}分钟 ${seconds % 60}秒`;
            } else {
                return `${seconds}秒`;
            }
        }

        /**
         * 更新缓存限制
         */
        updateCacheLimit(limit) {
            if (this.cacheManager) {
                this.cacheManager.cacheConfig.maxSize = limit;
                console.log('缓存大小限制已更新:', limit);
            }
        }

        /**
         * 更新保留天数
         */
        updateRetentionDays(days) {
            if (this.config) {
                this.config.set('storage.retentionDays', days);
                console.log('数据保留天数已更新:', days);
            }
        }
    }

    // 创建全局实例
    const dashboardManager = new DashboardManager();

    // 导出到全局命名空间
    window.OTA.dashboardManager = dashboardManager;
    window.dashboardManager = dashboardManager;

    // 全局函数供HTML调用
    window.refreshSystemStatus = function() {
        const spinner = document.getElementById('refresh-spinner');
        if (spinner) {
            spinner.classList.remove('hidden');
        }
        
        setTimeout(() => {
            dashboardManager.updateSystemStatus();
            if (spinner) {
                spinner.classList.add('hidden');
            }
        }, 1000);
    };

    window.runLearningEvaluation = function() {
        if (dashboardManager.evaluator) {
            dashboardManager.evaluator.performEvaluation();
            setTimeout(() => {
                dashboardManager.updateLearningMetrics();
            }, 2000);
        }
    };

    window.optimizePerformance = function() {
        if (dashboardManager.optimizer) {
            dashboardManager.optimizer.performOptimization();
        }
    };

    window.optimizeCache = function() {
        if (dashboardManager.cacheManager) {
            dashboardManager.cacheManager.performCleanup();
            dashboardManager.updateCacheMetrics();
        }
    };

    window.clearCache = function() {
        if (confirm('确定要清理缓存吗？这可能会影响系统性能。')) {
            if (dashboardManager.cacheManager) {
                dashboardManager.cacheManager.clear();
                dashboardManager.updateCacheMetrics();
            }
        }
    };

    window.clearAllCache = function() {
        if (confirm('确定要清空所有缓存吗？这将显著影响系统性能。')) {
            if (dashboardManager.cacheManager) {
                dashboardManager.cacheManager.clear();
                dashboardManager.updateCacheMetrics();
            }
        }
    };

    window.toggleLearning = function() {
        const toggle = document.getElementById('learning-enabled');
        toggle.classList.toggle('active');
        const enabled = toggle.classList.contains('active');
        
        if (dashboardManager.config) {
            dashboardManager.config.set('learningSystem.enabled', enabled);
        }
        console.log('学习系统', enabled ? '已启用' : '已禁用');
    };

    window.toggleAutoOptimization = function() {
        const toggle = document.getElementById('auto-optimization');
        toggle.classList.toggle('active');
        const enabled = toggle.classList.contains('active');
        
        if (dashboardManager.optimizer) {
            dashboardManager.optimizer.setAutoOptimization(enabled);
        }
        console.log('自动优化', enabled ? '已启用' : '已禁用');
    };

    window.togglePerformanceMonitoring = function() {
        const toggle = document.getElementById('performance-monitoring');
        toggle.classList.toggle('active');
        const enabled = toggle.classList.contains('active');
        
        if (dashboardManager.performanceMonitor) {
            dashboardManager.performanceMonitor.setEnabled(enabled);
        }
        console.log('性能监控', enabled ? '已启用' : '已禁用');
    };

    window.saveConfiguration = function() {
        alert('配置已保存');
    };

    window.resetConfiguration = function() {
        if (confirm('确定要重置所有配置吗？')) {
            // 重置所有开关到默认状态
            document.querySelectorAll('.toggle-switch').forEach(toggle => {
                toggle.classList.add('active');
            });
            
            // 重置输入字段
            document.getElementById('cache-limit').value = '1000';
            document.getElementById('retention-days').value = '30';
            
            alert('配置已重置');
        }
    };

    window.filterLogs = function(level) {
        dashboardManager.currentLogFilter = level;
        
        // 更新按钮状态
        document.querySelectorAll('[id^="filter-"]').forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-secondary');
        });
        document.getElementById(`filter-${level}`).classList.remove('btn-secondary');
        document.getElementById(`filter-${level}`).classList.add('btn-primary');
        
        dashboardManager.updateSystemLogs();
    };

    window.refreshLogs = function() {
        dashboardManager.updateSystemLogs();
    };

    window.exportLogs = function() {
        alert('日志导出功能开发中...');
    };

    window.clearLogs = function() {
        if (confirm('确定要清空所有日志吗？')) {
            const logContainer = document.getElementById('log-container');
            if (logContainer) {
                logContainer.innerHTML = '<div class="log-entry log-info">日志已清空</div>';
            }
        }
    };

    window.exportSystemReport = function() {
        alert('系统报告导出功能开发中...');
    };

    window.viewLearningHistory = function() {
        alert('学习历史查看功能开发中...');
    };

    console.log('管理面板控制器加载完成', {
        version: dashboardManager.version
    });

})();
