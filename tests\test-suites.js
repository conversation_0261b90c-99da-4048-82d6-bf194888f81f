/**
 * 智能学习型格式预处理引擎 - 测试用例集合
 * 包含所有模块的单元测试和集成测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

/**
 * 加载所有测试套件
 */
function loadTestSuites() {
    // 等待所有模块加载完成
    setTimeout(() => {
        loadLearningConfigTests();
        loadStorageManagerTests();
        loadOperationLearnerTests();
        loadErrorClassificationTests();
        loadPatternMatchingTests();
        loadRuleGenerationTests();
        loadPredictiveCorrectorTests();
        loadCacheManagerTests();
        loadPerformanceMonitorTests();
        loadIntegrationTests();
        
        // 更新统计
        testFramework.updateStats();
        testFramework.log('所有测试套件已加载完成', 'success');
    }, 1000);
}

/**
 * 学习配置测试
 */
function loadLearningConfigTests() {
    describe('学习配置模块', () => {
        let config;

        beforeEach(() => {
            config = window.OTA?.learningConfig || window.learningConfig;
        });

        it('应该能够获取配置实例', () => {
            expect(config).toBeTruthy();
            expect(typeof config.get).toBe('function');
            expect(typeof config.set).toBe('function');
        });

        it('应该能够获取默认配置值', () => {
            const enabled = config.get('learningSystem.enabled');
            expect(typeof enabled).toBe('boolean');
        });

        it('应该能够设置和获取配置值', () => {
            config.set('test.value', 'test123');
            const value = config.get('test.value');
            expect(value).toBe('test123');
        });

        it('应该能够获取嵌套配置值', () => {
            const storageKeys = config.get('storage.keys');
            expect(typeof storageKeys).toBe('object');
        });

        it('应该在获取不存在的配置时返回undefined', () => {
            const nonExistent = config.get('non.existent.key');
            expect(nonExistent).toBe(undefined);
        });
    });
}

/**
 * 存储管理器测试
 */
function loadStorageManagerTests() {
    describe('存储管理器', () => {
        let storageManager;

        beforeEach(() => {
            storageManager = window.OTA?.learningStorageManager || window.learningStorageManager;
        });

        it('应该能够获取存储管理器实例', () => {
            expect(storageManager).toBeTruthy();
            expect(typeof storageManager.setData).toBe('function');
            expect(typeof storageManager.getData).toBe('function');
        });

        it('应该能够存储和获取数据', () => {
            const testData = { test: 'data', number: 123 };
            storageManager.setData('test-key', testData);
            const retrieved = storageManager.getData('test-key');
            expect(retrieved).toEqual(testData);
        });

        it('应该能够删除数据', () => {
            storageManager.setData('delete-test', 'value');
            storageManager.removeData('delete-test');
            const retrieved = storageManager.getData('delete-test');
            expect(retrieved).toBe(null);
        });

        it('应该能够获取存储使用情况', () => {
            const usage = storageManager.getStorageUsage();
            expect(typeof usage).toBe('object');
            expect(typeof usage.used).toBe('number');
            expect(typeof usage.available).toBe('number');
        });

        it('应该能够清空所有数据', () => {
            storageManager.setData('clear-test-1', 'value1');
            storageManager.setData('clear-test-2', 'value2');
            storageManager.clearAllData();
            
            const value1 = storageManager.getData('clear-test-1');
            const value2 = storageManager.getData('clear-test-2');
            expect(value1).toBe(null);
            expect(value2).toBe(null);
        });
    });
}

/**
 * 用户操作学习器测试
 */
function loadOperationLearnerTests() {
    describe('用户操作学习器', () => {
        let operationLearner;

        beforeEach(() => {
            operationLearner = window.OTA?.userOperationLearner || window.userOperationLearner;
        });

        it('应该能够获取操作学习器实例', () => {
            expect(operationLearner).toBeTruthy();
            expect(typeof operationLearner.recordOperation).toBe('function');
            expect(typeof operationLearner.queryOperations).toBe('function');
        });

        it('应该能够记录用户操作', () => {
            const operation = {
                type: 'correction',
                field: 'customerName',
                originalValue: 'john doe',
                correctedValue: 'John Doe',
                context: { orderType: 'pickup' }
            };

            const result = operationLearner.recordOperation(operation);
            expect(result).toBeTruthy();
        });

        it('应该能够查询操作记录', () => {
            // 先记录一些操作
            operationLearner.recordOperation({
                type: 'correction',
                field: 'customerName',
                originalValue: 'test1',
                correctedValue: 'Test1'
            });

            const operations = operationLearner.queryOperations({
                field: 'customerName',
                limit: 10
            });

            expect(Array.isArray(operations)).toBeTruthy();
        });

        it('应该能够获取操作统计', () => {
            const stats = operationLearner.getOperationStats();
            expect(typeof stats).toBe('object');
            expect(typeof stats.totalOperations).toBe('number');
        });

        it('应该能够清理旧操作', () => {
            const beforeCount = operationLearner.getOperationCount();
            operationLearner.cleanupOldOperations();
            const afterCount = operationLearner.getOperationCount();
            expect(afterCount).toBeGreaterThan(-1); // 可能为0或更多
        });
    });
}

/**
 * 错误分类系统测试
 */
function loadErrorClassificationTests() {
    describe('错误分类系统', () => {
        let errorClassifier;

        beforeEach(() => {
            errorClassifier = window.OTA?.errorClassificationSystem || window.errorClassificationSystem;
        });

        it('应该能够获取错误分类器实例', () => {
            expect(errorClassifier).toBeTruthy();
            expect(typeof errorClassifier.classifyError).toBe('function');
        });

        it('应该能够分类错误', () => {
            const errorData = {
                field: 'customerName',
                originalValue: 'john',
                correctedValue: 'John',
                context: {}
            };

            const classification = errorClassifier.classifyError(errorData);
            expect(typeof classification).toBe('object');
            expect(typeof classification.category).toBe('string');
            expect(typeof classification.confidence).toBe('number');
        });

        it('应该能够获取错误类型列表', () => {
            const errorTypes = errorClassifier.getErrorTypes();
            expect(Array.isArray(errorTypes)).toBeTruthy();
            expect(errorTypes.length).toBeGreaterThan(0);
        });

        it('应该能够获取分类统计', () => {
            const stats = errorClassifier.getClassificationStats();
            expect(typeof stats).toBe('object');
        });
    });
}

/**
 * 模式匹配引擎测试
 */
function loadPatternMatchingTests() {
    describe('模式匹配引擎', () => {
        let patternMatcher;

        beforeEach(() => {
            patternMatcher = window.OTA?.patternMatchingEngine || window.patternMatchingEngine;
        });

        it('应该能够获取模式匹配器实例', () => {
            expect(patternMatcher).toBeTruthy();
            expect(typeof patternMatcher.calculateSimilarity).toBe('function');
        });

        it('应该能够计算字符串相似度', () => {
            const similarity = patternMatcher.calculateSimilarity('hello', 'hallo');
            expect(typeof similarity).toBe('number');
            expect(similarity).toBeGreaterThan(0);
            expect(similarity).toBeGreaterThan(0.5); // 应该有一定相似度
        });

        it('应该能够找到相似模式', () => {
            const patterns = ['John Doe', 'Jane Smith', 'Bob Johnson'];
            const matches = patternMatcher.findSimilarPatterns('Jon Doe', patterns);
            expect(Array.isArray(matches)).toBeTruthy();
        });

        it('相同字符串的相似度应该为1', () => {
            const similarity = patternMatcher.calculateSimilarity('test', 'test');
            expect(similarity).toBe(1);
        });

        it('完全不同字符串的相似度应该较低', () => {
            const similarity = patternMatcher.calculateSimilarity('abc', 'xyz');
            expect(similarity).toBeGreaterThan(-1); // 至少不会出错
        });
    });
}

/**
 * 规则生成引擎测试
 */
function loadRuleGenerationTests() {
    describe('规则生成引擎', () => {
        let ruleEngine;

        beforeEach(() => {
            ruleEngine = window.OTA?.ruleGenerationEngine || window.ruleGenerationEngine;
        });

        it('应该能够获取规则引擎实例', () => {
            expect(ruleEngine).toBeTruthy();
            expect(typeof ruleEngine.generateRule).toBe('function');
            expect(typeof ruleEngine.getAllRules).toBe('function');
        });

        it('应该能够生成规则', () => {
            const operationData = {
                field: 'customerName',
                originalValue: 'john',
                correctedValue: 'John',
                context: { frequency: 5 }
            };

            const rule = ruleEngine.generateRule(operationData);
            expect(typeof rule).toBe('object');
        });

        it('应该能够获取所有规则', () => {
            const rules = ruleEngine.getAllRules();
            expect(Array.isArray(rules)).toBeTruthy();
        });

        it('应该能够按字段获取规则', () => {
            const rules = ruleEngine.getRulesByField('customerName');
            expect(Array.isArray(rules)).toBeTruthy();
        });

        it('应该能够删除规则', () => {
            // 先生成一个规则
            const rule = ruleEngine.generateRule({
                field: 'testField',
                originalValue: 'test',
                correctedValue: 'Test'
            });

            if (rule && rule.id) {
                const deleted = ruleEngine.deleteRule(rule.id);
                expect(deleted).toBeTruthy();
            }
        });
    });
}

/**
 * 预测性校正器测试
 */
function loadPredictiveCorrectorTests() {
    describe('预测性校正器', () => {
        let predictor;

        beforeEach(() => {
            predictor = window.OTA?.predictiveCorrector || window.predictiveCorrector;
        });

        it('应该能够获取预测器实例', () => {
            expect(predictor).toBeTruthy();
            expect(typeof predictor.predictCorrection).toBe('function');
        });

        it('应该能够预测校正', () => {
            const prediction = predictor.predictCorrection('customerName', 'john doe', {});
            expect(typeof prediction).toBe('object');
            expect(typeof prediction.hasPrediction).toBe('boolean');
        });

        it('应该能够执行自动校正', () => {
            const predictionResult = {
                autoCorrect: true,
                topPrediction: {
                    originalValue: 'test',
                    correctedValue: 'Test',
                    confidence: 0.9
                }
            };

            const result = predictor.performAutoCorrection(predictionResult);
            expect(typeof result).toBe('object');
            expect(typeof result.success).toBe('boolean');
        });

        it('应该能够获取预测统计', () => {
            const stats = predictor.getPredictionStats();
            expect(typeof stats).toBe('object');
            expect(typeof stats.totalPredictions).toBe('number');
        });
    });
}

/**
 * 缓存管理器测试
 */
function loadCacheManagerTests() {
    describe('智能缓存管理器', () => {
        let cacheManager;

        beforeEach(() => {
            cacheManager = window.OTA?.intelligentCacheManager || window.intelligentCacheManager;
        });

        it('应该能够获取缓存管理器实例', () => {
            expect(cacheManager).toBeTruthy();
            expect(typeof cacheManager.get).toBe('function');
            expect(typeof cacheManager.set).toBe('function');
        });

        it('应该能够设置和获取缓存', () => {
            const testValue = { data: 'test', timestamp: Date.now() };
            const success = cacheManager.set('test-key', testValue);
            expect(success).toBeTruthy();

            const retrieved = cacheManager.get('test-key');
            expect(retrieved).toEqual(testValue);
        });

        it('应该能够删除缓存', () => {
            cacheManager.set('delete-test', 'value');
            const deleted = cacheManager.delete('delete-test');
            expect(deleted).toBeTruthy();

            const retrieved = cacheManager.get('delete-test');
            expect(retrieved).toBe(null);
        });

        it('应该能够获取缓存统计', () => {
            const stats = cacheManager.getStats();
            expect(typeof stats).toBe('object');
            expect(typeof stats.hits).toBe('number');
            expect(typeof stats.misses).toBe('number');
        });

        it('应该能够清空缓存', () => {
            cacheManager.set('clear-test-1', 'value1');
            cacheManager.set('clear-test-2', 'value2');
            cacheManager.clear();

            const value1 = cacheManager.get('clear-test-1');
            const value2 = cacheManager.get('clear-test-2');
            expect(value1).toBe(null);
            expect(value2).toBe(null);
        });
    });
}

/**
 * 性能监控器测试
 */
function loadPerformanceMonitorTests() {
    describe('性能监控器', () => {
        let performanceMonitor;

        beforeEach(() => {
            performanceMonitor = window.OTA?.performanceMonitor || window.performanceMonitor;
        });

        it('应该能够获取性能监控器实例', () => {
            expect(performanceMonitor).toBeTruthy();
            expect(typeof performanceMonitor.recordOperation).toBe('function');
        });

        it('应该能够记录操作性能', () => {
            const startTime = performance.now();
            performanceMonitor.recordOperation('test-operation', startTime, { test: true });
            // 不应该抛出异常
        });

        it('应该能够记录错误', () => {
            const error = new Error('测试错误');
            performanceMonitor.recordError('test-operation', error, { test: true });
            // 不应该抛出异常
        });

        it('应该能够获取实时指标', () => {
            const metrics = performanceMonitor.getRealTimeMetrics();
            expect(typeof metrics).toBe('object');
            expect(typeof metrics.totalOperations).toBe('number');
        });

        it('应该能够获取性能报告', () => {
            const report = performanceMonitor.getPerformanceReport();
            expect(typeof report).toBe('object');
            expect(typeof report.summary).toBe('object');
        });
    });
}

/**
 * 集成测试
 */
function loadIntegrationTests() {
    describe('系统集成测试', () => {
        it('应该能够完成完整的学习流程', async () => {
            // 模拟用户操作
            const operationLearner = window.OTA?.userOperationLearner || window.userOperationLearner;
            const ruleEngine = window.OTA?.ruleGenerationEngine || window.ruleGenerationEngine;
            const predictor = window.OTA?.predictiveCorrector || window.predictiveCorrector;

            if (!operationLearner || !ruleEngine || !predictor) {
                throw new Error('缺少必要的模块');
            }

            // 1. 记录用户操作
            const operation = {
                type: 'correction',
                field: 'customerName',
                originalValue: 'john smith',
                correctedValue: 'John Smith',
                context: { orderType: 'pickup' }
            };

            const recorded = operationLearner.recordOperation(operation);
            expect(recorded).toBeTruthy();

            // 2. 生成学习规则
            const rule = ruleEngine.generateRule(operation);
            expect(rule).toBeTruthy();

            // 3. 预测校正
            const prediction = predictor.predictCorrection('customerName', 'jane doe', {});
            expect(prediction).toBeTruthy();
            expect(typeof prediction.hasPrediction).toBe('boolean');
        });

        it('应该能够处理缓存和性能监控的集成', () => {
            const cacheManager = window.OTA?.intelligentCacheManager || window.intelligentCacheManager;
            const performanceMonitor = window.OTA?.performanceMonitor || window.performanceMonitor;

            if (!cacheManager || !performanceMonitor) {
                throw new Error('缺少必要的模块');
            }

            // 测试缓存操作的性能监控
            const startTime = performance.now();
            cacheManager.set('integration-test', { data: 'test' });
            performanceMonitor.recordOperation('cache-set', startTime);

            const retrieved = cacheManager.get('integration-test');
            expect(retrieved).toBeTruthy();
        });

        it('应该能够处理错误分类和规则生成的集成', () => {
            const errorClassifier = window.OTA?.errorClassificationSystem || window.errorClassificationSystem;
            const ruleEngine = window.OTA?.ruleGenerationEngine || window.ruleGenerationEngine;

            if (!errorClassifier || !ruleEngine) {
                throw new Error('缺少必要的模块');
            }

            // 分类错误并生成规则
            const errorData = {
                field: 'phoneNumber',
                originalValue: '1234567890',
                correctedValue: '+60-12-345-6789',
                context: { country: 'Malaysia' }
            };

            const classification = errorClassifier.classifyError(errorData);
            expect(classification).toBeTruthy();

            const rule = ruleEngine.generateRule(errorData);
            expect(rule).toBeTruthy();
        });

        it('应该能够处理存储和配置的集成', () => {
            const config = window.OTA?.learningConfig || window.learningConfig;
            const storageManager = window.OTA?.learningStorageManager || window.learningStorageManager;

            if (!config || !storageManager) {
                throw new Error('缺少必要的模块');
            }

            // 测试配置和存储的协同工作
            config.set('integration.test', true);
            const configValue = config.get('integration.test');
            expect(configValue).toBe(true);

            storageManager.setData('integration-config', { test: true });
            const storedData = storageManager.getData('integration-config');
            expect(storedData).toEqual({ test: true });
        });
    });
}

// 导出函数供HTML页面调用
window.loadTestSuites = loadTestSuites;
