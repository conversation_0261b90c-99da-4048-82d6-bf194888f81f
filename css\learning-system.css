/**
 * 智能学习型格式预处理引擎 - 样式文件
 * 为学习系统UI组件提供样式支持
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

/* 学习系统按钮样式 */
.learning-correction-btn {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border: none;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
}

.learning-correction-btn:hover {
    background: linear-gradient(135deg, #e67e22, #d35400);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
}

.learning-history-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.learning-history-btn:hover {
    background: linear-gradient(135deg, #2980b9, #1f4e79);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.4);
}

/* 字段更正图标 */
.field-correction-icon {
    display: inline-block;
    margin-left: 5px;
    cursor: pointer;
    opacity: 0.6;
    font-size: 12px;
    transition: all 0.2s ease;
    padding: 2px;
    border-radius: 3px;
}

.field-correction-icon:hover {
    opacity: 1;
    background-color: rgba(243, 156, 18, 0.1);
    transform: scale(1.1);
}

/* 学习系统模态框 */
.learning-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.learning-modal .modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.learning-modal .modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.learning-modal .modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.learning-modal .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.learning-modal .close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.learning-modal .modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.learning-modal .modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* 更正表单样式 */
.learning-correction-form .form-header {
    margin-bottom: 24px;
    text-align: center;
}

.learning-correction-form .form-header h4 {
    color: #495057;
    margin-bottom: 8px;
}

.field-edit-group {
    margin-bottom: 20px;
    padding: 16px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.field-edit-group:hover {
    border-color: #667eea;
    background-color: #ffffff;
}

.field-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.field-status .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

.field-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.field-correction-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.field-correction-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.reset-field-btn {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    background-color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reset-field-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.field-suggestions {
    margin-top: 8px;
}

.suggestions-list {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px;
}

.suggestion-item {
    font-size: 0.875rem;
    color: #856404;
}

/* 更正摘要样式 */
.correction-summary {
    margin-top: 24px;
    padding: 16px;
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
}

.correction-summary h5 {
    color: #0056b3;
    margin-bottom: 12px;
}

.correction-item {
    margin-bottom: 8px;
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    font-size: 0.875rem;
}

.original-value {
    color: #dc3545;
    text-decoration: line-through;
}

.corrected-value {
    color: #28a745;
    font-weight: 600;
}

/* 历史记录样式 */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: 16px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 12px;
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.history-item:hover {
    background-color: #e9ecef;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.field-name {
    font-weight: 600;
    color: #495057;
    background-color: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
}

.timestamp {
    font-size: 0.75rem;
    color: #6c757d;
}

.history-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.value-change {
    flex: 1;
}

.value-change .original {
    color: #dc3545;
    text-decoration: line-through;
}

.value-change .corrected {
    color: #28a745;
    font-weight: 600;
}

.confidence {
    font-size: 0.75rem;
    color: #6c757d;
    background-color: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .learning-modal .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .learning-modal .modal-body {
        padding: 16px;
        max-height: 70vh;
    }
    
    .field-edit-group {
        padding: 12px;
    }
    
    .field-input-group {
        flex-direction: column;
        gap: 8px;
    }
    
    .field-correction-input {
        width: 100%;
    }
    
    .reset-field-btn {
        align-self: flex-end;
        width: auto;
    }
    
    .history-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .history-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* 学习系统状态指示器 */
.learning-status-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    z-index: 1000;
    transition: all 0.3s ease;
}

.learning-status-indicator:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.learning-status-indicator.disabled {
    background: #6c757d;
    opacity: 0.7;
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.learning-correction-btn.active {
    animation: pulse 2s infinite;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .learning-modal .modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .learning-modal .modal-footer {
        background-color: #1a202c;
        border-top-color: #4a5568;
    }
    
    .field-edit-group {
        background-color: #1a202c;
        border-color: #4a5568;
    }
    
    .field-edit-group:hover {
        background-color: #2d3748;
    }
    
    .field-correction-input {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .history-item {
        background-color: #1a202c;
        border-color: #4a5568;
    }
    
    .history-item:hover {
        background-color: #2d3748;
    }
}
