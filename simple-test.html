<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统依赖注入测试</title>
</head>
<body>
    <h1>OTA系统依赖注入测试</h1>
    <p>请打开浏览器控制台查看测试结果</p>
    
    <!-- 加载核心架构模块 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    
    <!-- 加载基础服务 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    
    <!-- 加载测试脚本 -->
    <script src="test-dependency-injection.js"></script>
    
    <script>
        // 等待所有脚本加载完成后运行测试
        window.addEventListener('load', function() {
            setTimeout(function() {
                console.log('开始运行依赖注入测试...');
                
                // 运行测试
                if (window.testDependencyInjection) {
                    const results = window.testDependencyInjection.runAllTests();
                    
                    // 在页面上显示结果
                    const body = document.body;
                    const resultDiv = document.createElement('div');
                    resultDiv.innerHTML = `
                        <h2>测试结果</h2>
                        <p><strong>总测试数:</strong> ${results.stats.total}</p>
                        <p><strong>通过测试:</strong> ${results.stats.passed}</p>
                        <p><strong>失败测试:</strong> ${results.stats.failed}</p>
                        <p><strong>成功率:</strong> ${results.stats.successRate}%</p>
                        <p><strong>系统健康度:</strong> ${results.stats.healthScore}%</p>
                        <p><strong>测试耗时:</strong> ${results.stats.duration}ms</p>
                        <p><strong>整体状态:</strong> ${results.success ? '✅ 通过' : '❌ 失败'}</p>
                    `;
                    body.appendChild(resultDiv);
                } else {
                    console.error('测试脚本未正确加载');
                }
            }, 1000);
        });
    </script>
</body>
</html>
