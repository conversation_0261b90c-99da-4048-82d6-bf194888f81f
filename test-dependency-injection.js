/**
 * OTA系统依赖注入功能测试脚本
 * 用于验证新的服务定位器和依赖容器功能
 */

(function() {
    'use strict';

    // 测试结果收集器
    const testResults = {
        total: 0,
        passed: 0,
        failed: 0,
        results: []
    };

    // 添加测试结果
    function addTestResult(testName, success, message, details = null) {
        testResults.total++;
        if (success) {
            testResults.passed++;
        } else {
            testResults.failed++;
        }

        const result = {
            name: testName,
            success,
            message,
            details,
            timestamp: new Date().toISOString()
        };

        testResults.results.push(result);
        
        // 输出到控制台
        const icon = success ? '✅' : '❌';
        console.log(`${icon} ${testName}: ${message}`);
        if (details) {
            console.log('   详细信息:', details);
        }
    }

    // 测试服务定位器
    function testServiceLocator() {
        console.log('\n🔍 开始测试服务定位器...');

        try {
            // 测试1: 检查服务定位器是否存在
            const hasServiceLocator = window.OTA && window.OTA.serviceLocator;
            addTestResult('服务定位器存在性检查', hasServiceLocator, 
                hasServiceLocator ? '服务定位器已正确加载' : '服务定位器未找到');

            if (!hasServiceLocator) return false;

            // 测试2: 检查getService函数
            const hasGetService = typeof window.getService === 'function';
            addTestResult('getService函数检查', hasGetService,
                hasGetService ? 'getService函数可用' : 'getService函数未找到');

            // 测试3: 测试服务获取
            if (hasGetService) {
                try {
                    const logger = window.getService('logger');
                    addTestResult('Logger服务获取', !!logger,
                        logger ? 'Logger服务获取成功' : 'Logger服务获取失败');

                    const appState = window.getService('appState');
                    addTestResult('AppState服务获取', !!appState,
                        appState ? 'AppState服务获取成功' : 'AppState服务获取失败');

                    // 测试4: 测试服务方法调用
                    if (logger && typeof logger.log === 'function') {
                        logger.log('依赖注入测试', 'info');
                        addTestResult('Logger方法调用', true, 'Logger.log方法调用成功');
                    }

                } catch (error) {
                    addTestResult('服务获取测试', false, `服务获取过程中出错: ${error.message}`);
                }
            }

            return hasServiceLocator && hasGetService;

        } catch (error) {
            addTestResult('服务定位器测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 测试依赖容器
    function testDependencyContainer() {
        console.log('\n📦 开始测试依赖容器...');

        try {
            // 测试1: 检查依赖容器是否存在
            const hasContainer = window.OTA && window.OTA.container;
            addTestResult('依赖容器存在性检查', hasContainer,
                hasContainer ? '依赖容器已正确加载' : '依赖容器未找到');

            if (!hasContainer) return false;

            const container = window.OTA.container;

            // 测试2: 检查容器方法
            const hasMethods = typeof container.register === 'function' && 
                             typeof container.get === 'function' &&
                             typeof container.has === 'function';
            addTestResult('容器方法检查', hasMethods,
                hasMethods ? '容器方法完整' : '容器方法缺失');

            // 测试3: 测试服务注册和获取
            if (hasMethods) {
                try {
                    // 注册测试服务
                    container.register('testService', () => ({ 
                        test: true, 
                        timestamp: Date.now(),
                        version: '1.0.0'
                    }));
                    
                    const hasTestService = container.has('testService');
                    addTestResult('服务注册测试', hasTestService,
                        hasTestService ? '测试服务注册成功' : '测试服务注册失败');

                    if (hasTestService) {
                        const testService = container.get('testService');
                        const isValid = testService && testService.test === true;
                        addTestResult('服务获取测试', isValid,
                            isValid ? '测试服务获取成功' : '测试服务获取失败', testService);
                    }

                    // 测试4: 获取已注册服务列表
                    if (typeof container.getRegisteredServices === 'function') {
                        const services = container.getRegisteredServices();
                        addTestResult('服务列表获取', Array.isArray(services),
                            `已注册服务数量: ${services ? services.length : 0}`, services);
                    }

                } catch (error) {
                    addTestResult('容器操作测试', false, `容器操作过程中出错: ${error.message}`);
                }
            }

            return hasContainer && hasMethods;

        } catch (error) {
            addTestResult('依赖容器测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 测试服务迁移状态
    function testServiceMigration() {
        console.log('\n🔄 开始测试服务迁移状态...');

        try {
            const serviceLocator = window.OTA && window.OTA.serviceLocator;
            if (!serviceLocator) {
                addTestResult('迁移状态测试', false, '服务定位器不可用');
                return false;
            }

            // 获取迁移报告
            if (typeof serviceLocator.getMigrationReport === 'function') {
                const report = serviceLocator.getMigrationReport();
                addTestResult('迁移报告生成', true, '迁移报告生成成功', report);
                
                // 检查迁移进度
                const migrationProgress = report.totalServices > 0 ? 
                    report.containerServices / report.totalServices : 0;
                const progressPercent = Math.round(migrationProgress * 100);
                
                addTestResult('迁移进度检查', migrationProgress > 0,
                    `迁移进度: ${progressPercent}% (${report.containerServices}/${report.totalServices})`);

                // 检查是否有降级使用的服务
                if (report.fallbackUsed > 0) {
                    addTestResult('降级服务检查', true,
                        `发现 ${report.fallbackUsed} 个服务使用了降级方式`, report.warnings);
                }

                return true;
            } else {
                addTestResult('迁移报告功能', false, '迁移报告功能不可用');
                return false;
            }

        } catch (error) {
            addTestResult('服务迁移测试', false, `测试过程中出错: ${error.message}`);
            return false;
        }
    }

    // 系统健康检查
    function performHealthCheck() {
        console.log('\n🏥 开始系统健康检查...');

        const checks = [
            { name: 'OTA命名空间', check: () => !!window.OTA },
            { name: '依赖容器', check: () => !!(window.OTA && window.OTA.container) },
            { name: '服务定位器', check: () => !!(window.OTA && window.OTA.serviceLocator) },
            { name: 'getService函数', check: () => typeof window.getService === 'function' },
            { name: 'Logger服务', check: () => {
                try { return !!window.getService('logger'); } catch { return false; }
            }},
            { name: 'AppState服务', check: () => {
                try { return !!window.getService('appState'); } catch { return false; }
            }}
        ];

        let healthyCount = 0;
        checks.forEach(({ name, check }) => {
            try {
                const result = check();
                if (result) healthyCount++;
                addTestResult(`健康检查: ${name}`, result, result ? '正常' : '异常');
            } catch (error) {
                addTestResult(`健康检查: ${name}`, false, `检查失败: ${error.message}`);
            }
        });

        const healthScore = Math.round((healthyCount / checks.length) * 100);
        addTestResult('系统健康评分', healthScore >= 80, `系统健康度: ${healthScore}%`);

        return healthScore;
    }

    // 运行所有测试
    function runAllTests() {
        console.log('🚀 开始执行OTA系统依赖注入功能测试...');
        console.log('测试时间:', new Date().toLocaleString());

        const startTime = performance.now();

        // 执行测试
        const serviceLocatorOk = testServiceLocator();
        const containerOk = testDependencyContainer();
        const migrationOk = testServiceMigration();
        const healthScore = performHealthCheck();

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // 输出测试总结
        console.log('\n📊 测试总结:');
        console.log(`总测试数: ${testResults.total}`);
        console.log(`通过测试: ${testResults.passed}`);
        console.log(`失败测试: ${testResults.failed}`);
        console.log(`成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
        console.log(`测试耗时: ${duration}ms`);
        console.log(`系统健康度: ${healthScore}%`);

        // 返回测试结果
        return {
            success: testResults.failed === 0,
            stats: {
                total: testResults.total,
                passed: testResults.passed,
                failed: testResults.failed,
                successRate: Math.round((testResults.passed / testResults.total) * 100),
                duration,
                healthScore
            },
            results: testResults.results
        };
    }

    // 导出测试函数到全局
    window.testDependencyInjection = {
        runAllTests,
        testServiceLocator,
        testDependencyContainer,
        testServiceMigration,
        performHealthCheck,
        getResults: () => testResults
    };

    console.log('✅ 依赖注入测试脚本已加载');

})();
