@echo off
REM 智能学习型格式预处理引擎 - Windows部署脚本
REM 用于自动化部署到生产环境
REM 
REM 使用方法: deploy.bat [环境] [版本]
REM 示例: deploy.bat production 1.0.0

setlocal enabledelayedexpansion

REM 配置变量
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set ENVIRONMENT=%1
set VERSION=%2
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set BACKUP_DIR=%PROJECT_ROOT%\backups
set DEPLOY_DIR=%PROJECT_ROOT%\dist
set LOG_FILE=%PROJECT_ROOT%\deployment_%TIMESTAMP%.log

REM 设置默认值
if "%ENVIRONMENT%"=="" set ENVIRONMENT=production
if "%VERSION%"=="" set VERSION=1.0.0

REM 日志函数
echo [%date% %time%] 开始部署智能学习型格式预处理引擎 >> "%LOG_FILE%"
echo [%date% %time%] 环境: %ENVIRONMENT%, 版本: %VERSION% >> "%LOG_FILE%"

echo.
echo ================================
echo 智能学习型格式预处理引擎部署脚本
echo ================================
echo 环境: %ENVIRONMENT%
echo 版本: %VERSION%
echo 时间戳: %TIMESTAMP%
echo.

REM 检查依赖
echo [1/8] 检查部署依赖...
echo [%date% %time%] 检查部署依赖... >> "%LOG_FILE%"

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Node.js 已安装
    echo [%date% %time%] Node.js 已安装 >> "%LOG_FILE%"
) else (
    echo ⚠ Node.js 未安装，某些功能可能不可用
    echo [%date% %time%] Node.js 未安装 >> "%LOG_FILE%"
)

REM 检查必要文件
set "files_missing=0"
if not exist "%PROJECT_ROOT%\index.html" (
    echo ✗ 缺少文件: index.html
    set "files_missing=1"
)
if not exist "%PROJECT_ROOT%\js\logger.js" (
    echo ✗ 缺少文件: js\logger.js
    set "files_missing=1"
)
if not exist "%PROJECT_ROOT%\js\learning-engine" (
    echo ✗ 缺少目录: js\learning-engine
    set "files_missing=1"
)

if "%files_missing%"=="1" (
    echo 部署失败：缺少必要文件
    echo [%date% %time%] 部署失败：缺少必要文件 >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo ✓ 文件检查通过

REM 创建备份
echo [2/8] 创建部署前备份...
echo [%date% %time%] 创建部署前备份... >> "%LOG_FILE%"

if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

if exist "%DEPLOY_DIR%" (
    set "backup_name=backup_%ENVIRONMENT%_%TIMESTAMP%"
    powershell -command "Compress-Archive -Path '%DEPLOY_DIR%\*' -DestinationPath '%BACKUP_DIR%\!backup_name!.zip' -Force" >nul 2>&1
    if %errorlevel%==0 (
        echo ✓ 备份已创建: !backup_name!.zip
        echo [%date% %time%] 备份已创建: !backup_name!.zip >> "%LOG_FILE%"
    ) else (
        echo ⚠ 备份创建失败，继续部署
        echo [%date% %time%] 备份创建失败 >> "%LOG_FILE%"
    )
) else (
    echo ⚠ 没有找到现有部署，跳过备份
    echo [%date% %time%] 没有找到现有部署，跳过备份 >> "%LOG_FILE%"
)

REM 构建项目
echo [3/8] 构建项目...
echo [%date% %time%] 构建项目... >> "%LOG_FILE%"

if exist "%DEPLOY_DIR%" rmdir /s /q "%DEPLOY_DIR%"
mkdir "%DEPLOY_DIR%"

REM 复制核心文件
copy "%PROJECT_ROOT%\index.html" "%DEPLOY_DIR%\" >nul
xcopy "%PROJECT_ROOT%\js" "%DEPLOY_DIR%\js\" /e /i /q >nul

REM 复制其他文件
if exist "%PROJECT_ROOT%\css" xcopy "%PROJECT_ROOT%\css" "%DEPLOY_DIR%\css\" /e /i /q >nul
if exist "%PROJECT_ROOT%\assets" xcopy "%PROJECT_ROOT%\assets" "%DEPLOY_DIR%\assets\" /e /i /q >nul
if exist "%PROJECT_ROOT%\learning-system-dashboard.html" copy "%PROJECT_ROOT%\learning-system-dashboard.html" "%DEPLOY_DIR%\" >nul

REM 复制部署配置
mkdir "%DEPLOY_DIR%\deployment"
copy "%PROJECT_ROOT%\deployment\production-config.js" "%DEPLOY_DIR%\deployment\" >nul

REM 复制文档
if exist "%PROJECT_ROOT%\docs" xcopy "%PROJECT_ROOT%\docs" "%DEPLOY_DIR%\docs\" /e /i /q >nul

echo ✓ 项目构建完成

REM 优化文件
echo [4/8] 优化部署文件...
echo [%date% %time%] 优化部署文件... >> "%LOG_FILE%"

REM 删除开发相关文件
for /r "%DEPLOY_DIR%" %%f in (*.test.js *.spec.js .DS_Store) do (
    if exist "%%f" del "%%f" >nul 2>&1
)

REM 删除测试目录
if exist "%DEPLOY_DIR%\tests" rmdir /s /q "%DEPLOY_DIR%\tests" >nul 2>&1

echo ✓ 文件优化完成

REM 配置生产环境
echo [5/8] 配置生产环境...
echo [%date% %time%] 配置生产环境... >> "%LOG_FILE%"

REM 更新index.html以加载生产配置
if exist "%DEPLOY_DIR%\index.html" (
    powershell -command "(Get-Content '%DEPLOY_DIR%\index.html') -replace '</body>', '    <script src=\"deployment/production-config.js\"></script>^</body>' | Set-Content '%DEPLOY_DIR%\index.html'"
    echo ✓ 生产配置已添加到index.html
)

REM 更新管理面板
if exist "%DEPLOY_DIR%\learning-system-dashboard.html" (
    powershell -command "(Get-Content '%DEPLOY_DIR%\learning-system-dashboard.html') -replace '</body>', '    <script src=\"deployment/production-config.js\"></script>^</body>' | Set-Content '%DEPLOY_DIR%\learning-system-dashboard.html'"
    echo ✓ 生产配置已添加到管理面板
)

REM 创建版本信息文件
(
echo {
echo     "version": "%VERSION%",
echo     "environment": "%ENVIRONMENT%",
echo     "buildDate": "%date%T%time%Z",
echo     "buildNumber": "%TIMESTAMP%",
echo     "gitCommit": "unknown"
echo }
) > "%DEPLOY_DIR%\version.json"

echo ✓ 版本信息文件已创建

REM 验证部署
echo [6/8] 验证部署...
echo [%date% %time%] 验证部署... >> "%LOG_FILE%"

set "validation_failed=0"
if not exist "%DEPLOY_DIR%\index.html" (
    echo ✗ 关键文件缺失: index.html
    set "validation_failed=1"
)
if not exist "%DEPLOY_DIR%\js\logger.js" (
    echo ✗ 关键文件缺失: js\logger.js
    set "validation_failed=1"
)
if not exist "%DEPLOY_DIR%\js\learning-engine\learning-config.js" (
    echo ✗ 关键文件缺失: js\learning-engine\learning-config.js
    set "validation_failed=1"
)
if not exist "%DEPLOY_DIR%\deployment\production-config.js" (
    echo ✗ 关键文件缺失: deployment\production-config.js
    set "validation_failed=1"
)
if not exist "%DEPLOY_DIR%\version.json" (
    echo ✗ 关键文件缺失: version.json
    set "validation_failed=1"
)

if "%validation_failed%"=="1" (
    echo 验证失败：关键文件缺失
    echo [%date% %time%] 验证失败：关键文件缺失 >> "%LOG_FILE%"
    pause
    exit /b 1
)

REM 计算部署包大小
for /f "tokens=3" %%a in ('dir "%DEPLOY_DIR%" /s /-c ^| find "个文件"') do set "total_size=%%a"
echo ✓ 部署包验证通过，大小: %total_size% 字节

REM 生成部署报告
echo [7/8] 生成部署报告...
echo [%date% %time%] 生成部署报告... >> "%LOG_FILE%"

set "report_file=%PROJECT_ROOT%\deployment_report_%TIMESTAMP%.md"

(
echo # 部署报告
echo.
echo ## 基本信息
echo - **环境**: %ENVIRONMENT%
echo - **版本**: %VERSION%
echo - **部署时间**: %date% %time%
echo - **部署目录**: %DEPLOY_DIR%
echo.
echo ## 文件统计
echo - **总大小**: %total_size% 字节
echo.
echo ## 核心模块
for /f %%f in ('dir "%DEPLOY_DIR%\js\learning-engine\*.js" /b') do echo - %%f
echo.
echo ## 配置文件
echo - production-config.js
echo - version.json
echo.
echo ## 验证结果
echo ✓ 所有关键文件存在
echo ✓ 部署包完整性验证通过
echo.
echo ## 下一步
echo 1. 将 %DEPLOY_DIR% 目录部署到Web服务器
echo 2. 配置Web服务器（如IIS、Nginx、Apache）
echo 3. 设置SSL证书（推荐）
echo 4. 配置监控和日志收集
echo 5. 进行功能测试
echo.
echo ## 回滚方案
echo 如需回滚，可使用备份文件：
echo ```
echo cd %BACKUP_DIR%
echo # 解压备份文件
echo ```
echo.
) > "%report_file%"

echo ✓ 部署报告已生成: deployment_report_%TIMESTAMP%.md

REM 清理临时文件
echo [8/8] 清理临时文件...
echo [%date% %time%] 清理临时文件... >> "%LOG_FILE%"

for /r "%PROJECT_ROOT%" %%f in (*.bak .DS_Store) do (
    if exist "%%f" del "%%f" >nul 2>&1
)

echo ✓ 清理完成

REM 完成部署
echo.
echo ================================
echo 🎉 部署成功完成！
echo ================================
echo 📁 部署目录: %DEPLOY_DIR%
echo 📋 部署报告: deployment_report_%TIMESTAMP%.md
echo 📄 日志文件: deployment_%TIMESTAMP%.log
echo.
echo 下一步操作:
echo 1. 将部署目录上传到Web服务器
echo 2. 配置Web服务器
echo 3. 设置SSL证书
echo 4. 进行功能测试
echo.

echo [%date% %time%] 部署完成 >> "%LOG_FILE%"

pause
