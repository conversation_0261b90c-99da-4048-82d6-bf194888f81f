/**
 * 实时分析管理器模块
 * 负责实时订单分析、进度显示和结果处理
 * 支持防抖处理和智能分析触发
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器
    function getAppState() {
        return getService('appState');
    }

    function getLogger() {
        return getService('logger');
    }

    function getGeminiService() {
        return getService('geminiService');
    }

    /**
     * 实时分析管理器类
     * 负责实时分析相关的所有操作
     */
    class RealtimeAnalysisManager {
        constructor(elements, uiManager) {
            this.elements = elements;
            this.uiManager = uiManager;
            
            // 实时分析相关状态（🔧 修复：移除独立的isAnalyzing状态，使用Gemini服务的统一状态）
            this.realtimeAnalysis = {
                enabled: true,
                debounceTimer: null,
                lastAnalysisTime: 0,
                progressIndicator: null
            };

            // 绑定方法上下文
            this.handleRealtimeInput = this.handleRealtimeInput.bind(this);
        }

        /**
         * 初始化实时分析管理器
         */
        init() {
            this.setupRealtimeAnalysis();
            this.createProgressIndicator();
            this.bindInputEvents();
            getLogger().log('实时分析管理器初始化完成', 'success');
        }

        /**
         * 设置实时分析功能
         */
        setupRealtimeAnalysis() {
            // 配置实时分析参数
            getGeminiService().configureRealtimeAnalysis({
                enabled: true,
                debounceDelay: 1500,
                minInputLength: 15,
                onProgress: (message, progress) => this.handleAnalysisProgress(message, progress),
                onResult: (result) => this.handleAnalysisResult(result),
                onError: (error) => this.handleAnalysisError(error)
            });

            getLogger().log('实时分析功能已配置', 'info');
        }

        /**
         * 创建进度指示器
         */
        createProgressIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'realtime-progress';
            indicator.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="progress-text">分析中...</div>
            `;
            indicator.style.display = 'none';

            // 添加样式
            if (!document.getElementById('realtime-progress-styles')) {
                const style = document.createElement('style');
                style.id = 'realtime-progress-styles';
                style.textContent = `
                    .realtime-progress {
                        position: absolute;
                        top: 100%;
                        left: 0;
                        right: 0;
                        background: var(--color-surface);
                        border: 1px solid var(--color-border);
                        border-top: none;
                        border-radius: 0 0 8px 8px;
                        padding: 12px;
                        z-index: 1000;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    }
                    .progress-bar {
                        width: 100%;
                        height: 4px;
                        background: var(--color-border);
                        border-radius: 2px;
                        overflow: hidden;
                        margin-bottom: 8px;
                    }
                    .progress-fill {
                        height: 100%;
                        background: var(--color-primary);
                        border-radius: 2px;
                        transition: width 0.3s ease;
                        width: 0%;
                    }
                    .progress-text {
                        font-size: 12px;
                        color: var(--color-text-secondary);
                        text-align: center;
                    }
                `;
                document.head.appendChild(style);
            }

            // 添加到输入容器
            const inputContainer = this.elements.orderInput?.parentElement;
            if (inputContainer) {
                inputContainer.style.position = 'relative';
                inputContainer.appendChild(indicator);
            }

            this.realtimeAnalysis.progressIndicator = indicator;
        }

        /**
         * 绑定输入事件
         */
        bindInputEvents() {
            if (this.elements.orderInput) {
                this.elements.orderInput.addEventListener('input', this.handleRealtimeInput);
                this.elements.orderInput.addEventListener('paste', (e) => {
                    // 粘贴后稍微延迟处理，确保内容已更新
                    setTimeout(() => this.handleRealtimeInput(e), 100);
                });
            }
        }

        /**
         * 处理实时输入
         * @param {Event} event - 输入事件
         */
        handleRealtimeInput(event) {
            // 🔧 修复：防止事件递归 - 忽略程序触发的事件
            if (event.isTrusted === false && event._programmaticTrigger) {
                getLogger().log('⚠️ 忽略程序触发的input事件，防止递归', 'info');
                return;
            }
            
            const inputText = event.target.value;
            
            // 清除之前的防抖定时器
            if (this.realtimeAnalysis.debounceTimer) {
                clearTimeout(this.realtimeAnalysis.debounceTimer);
            }

            // 如果输入为空，清除分析状态
            if (!inputText.trim()) {
                this.clearRealtimeAnalysis();
                this.updateGeminiStatus('请输入订单描述');
                return;
            }

            // 检查最小输入长度
            if (inputText.trim().length < 15) {
                this.updateGeminiStatus(`请继续输入... (${inputText.trim().length}/15)`);
                return;
            }

            // 设置防抖定时器
            this.realtimeAnalysis.debounceTimer = setTimeout(() => {
                this.triggerRealtimeAnalysis(inputText);
            }, 1500);

            this.updateGeminiStatus('准备分析...');
        }

        /**
         * 触发实时分析 - 统一入口处理（重构）
         * 所有订单输入都先通过多订单检测，根据结果智能选择处理模式
         * @param {string} orderText - 订单文本
         */
        async triggerRealtimeAnalysis(orderText) {
            if (!orderText || !getGeminiService().isAvailable()) {
                return;
            }

            // 防止重复分析（🔧 修复：使用Gemini服务的统一状态）
            if (getGeminiService().getStatus().isAnalyzing) {
                return;
            }
            this.realtimeAnalysis.lastAnalysisTime = Date.now();
            this.showProgressIndicator();

            try {
                // 🔥 核心改进：统一入口处理 - 所有输入都先进行多订单检测
                getLogger().log('🔄 开始统一订单分析处理...', 'info');
                this.updateGeminiStatus('🤖 智能分析订单类型...');
                
                // 步骤1：调用detectAndSplitMultiOrdersWithVerification进行统一检测
                const multiOrderResult = await getGeminiService().detectAndSplitMultiOrdersWithVerification(orderText, {
                    enabled: true,
                    attempts: 3,
                    consistencyThreshold: 0.8
                });
                
                // 🔧 故障保险：额外验证多订单检测结果的准确性
                const validatedResult = this.validateMultiOrderResult(multiOrderResult, orderText);
                
                getLogger().log('📊 多订单检测结果:', 'info', {
                    original: {
                        isMultiOrder: multiOrderResult.isMultiOrder,
                        orderCount: multiOrderResult.orderCount,
                        confidence: multiOrderResult.confidence
                    },
                    validated: {
                        isMultiOrder: validatedResult.isMultiOrder,
                        orderCount: validatedResult.orderCount,
                        confidence: validatedResult.confidence
                    }
                });

                // 步骤2：根据orderCount智能选择处理模式（使用验证后的结果）
                if (validatedResult.orderCount > 1) {
                    // 🎯 多订单模式：直接触发多订单面板
                    getLogger().log(`✅ 检测到多订单(${validatedResult.orderCount}个)，触发多订单处理模式`, 'success');
                    this.updateGeminiStatus(`✅ 检测到 ${validatedResult.orderCount} 个订单`);
                    
                    // 触发多订单检测事件，传递验证后的结果
                    const event = new CustomEvent('multiOrderDetected', {
                        detail: {
                            multiOrderResult: validatedResult,  // 使用验证后的结果
                            orderText: orderText
                        }
                    });
                    document.dispatchEvent(event);
                    
                    // 隐藏进度指示器，因为多订单面板将接管显示
                    this.hideProgressIndicator();
                    
                } else if (validatedResult.orderCount === 1) {
                    // 🎯 单订单模式：提取第一个订单进行标准处理
                    getLogger().log('✅ 检测到单订单，进入标准处理模式', 'success');
                    this.updateGeminiStatus('✅ 分析单个订单...');
                    
                    const singleOrder = validatedResult.orders[0];
                    if (singleOrder) {
                        // 转换为标准的实时分析结果格式
                        this.handleAnalysisResult({
                            success: true,
                            data: singleOrder,
                            confidence: validatedResult.confidence,
                            timestamp: Date.now(),
                            source: 'unified-analysis'
                        });
                    } else {
                        throw new Error('单订单数据缺失');
                    }
                    
                } else {
                    // 🎯 无有效订单：显示错误
                    getLogger().log('⚠️ 未检测到有效订单', 'warning');
                    this.handleAnalysisError(new Error('未检测到有效的订单信息，请检查输入内容'));
                }
                
            } catch (error) {
                getLogger().logError('统一订单分析失败', error);
                this.handleAnalysisError(error);
            } finally {
                // 🔧 修复：确保状态总是被重置，防止死锁
                this.realtimeAnalysis.lastAnalysisTime = Date.now();
                getLogger().log('🔄 实时分析处理完成，状态已重置', 'info');
            }
        }

        /**
         * 验证多订单检测结果的准确性（故障保险机制）
         * @param {object} result - Gemini返回的原始结果
         * @param {string} orderText - 原始订单文本
         * @returns {object} - 验证后的结果
         */
        validateMultiOrderResult(result, orderText) {
            const logger = getLogger();
            logger.log('🔍 开始验证多订单检测结果...', 'info');

            // 创建验证后的结果副本
            const validatedResult = { ...result };

            // 1. 基于文本特征的验证
            const textAnalysis = this.analyzeTextFeatures(orderText);
            
            // 2. 验证orderCount是否与orders数组长度一致
            if (result.orders && Array.isArray(result.orders)) {
                const actualOrderCount = result.orders.length;
                if (result.orderCount !== actualOrderCount) {
                    logger.log(`⚠️ 修正orderCount: ${result.orderCount} → ${actualOrderCount}`, 'warning');
                    validatedResult.orderCount = actualOrderCount;
                }
            }

            // 3. 基于文本特征强制修正多订单判断
            if (textAnalysis.shouldBeMultiOrder && !result.isMultiOrder) {
                logger.log('⚠️ 基于文本分析强制设置为多订单模式', 'warning');
                validatedResult.isMultiOrder = true;
                
                // 如果orderCount小于检测到的特征数量，进行修正
                if (validatedResult.orderCount < textAnalysis.estimatedOrderCount) {
                    validatedResult.orderCount = textAnalysis.estimatedOrderCount;
                }
            }

            // 4. 举牌服务特殊处理
            if (textAnalysis.hasPagingService) {
                logger.log('✅ 检测到举牌服务，确保触发多订单模块', 'info');
                validatedResult.isMultiOrder = true; // 举牌服务必须触发多订单模块
            }

            logger.log('✅ 多订单结果验证完成', 'success', {
                textFeatures: textAnalysis,
                corrected: {
                    orderCount: result.orderCount !== validatedResult.orderCount,
                    isMultiOrder: result.isMultiOrder !== validatedResult.isMultiOrder
                }
            });

            return validatedResult;
        }

        /**
         * 分析文本特征以验证多订单检测
         * @param {string} text - 订单文本
         * @returns {object} - 文本分析结果
         */
        analyzeTextFeatures(text) {
            const features = {
                shouldBeMultiOrder: false,
                estimatedOrderCount: 1,
                hasPagingService: false,
                hasMultipleReferences: false,
                hasMultipleDates: false,
                hasMultipleFlights: false
            };

            // 1. 检测举牌服务关键词
            const pagingKeywords = [
                '举牌', '举牌接机', '举牌服务', 'meet and greet', 'meet & greet', 
                'meet-and-greet', '接机牌', '迎接服务', '接机服务', 'paging', 
                'paging service', '举牌迎接', '机场迎接', '接机员', '迎宾服务'
            ];
            features.hasPagingService = pagingKeywords.some(keyword => 
                text.toLowerCase().includes(keyword.toLowerCase())
            );

            // 2. 检测多个团号/订单号
            const referencePattern = /团号[:：]\s*([A-Z0-9-]+)/gi;
            const references = [...text.matchAll(referencePattern)];
            const uniqueReferences = [...new Set(references.map(match => match[1]))];
            features.hasMultipleReferences = uniqueReferences.length > 1;

            // 3. 检测多个日期
            const datePattern = /\b(\d{1,2}\/\d{1,2})\b/g;
            const dates = [...text.matchAll(datePattern)];
            const uniqueDates = [...new Set(dates.map(match => match[1]))];
            features.hasMultipleDates = uniqueDates.length > 1;

            // 4. 检测多个航班
            const flightPattern = /\b([A-Z]{2}\d{3,4})\b/g;
            const flights = [...text.matchAll(flightPattern)];
            const uniqueFlights = [...new Set(flights.map(match => match[1]))];
            features.hasMultipleFlights = uniqueFlights.length > 1;

            // 5. 检测换行分隔的订单
            const lines = text.split('\n').filter(line => line.trim().length > 0);
            const orderLines = lines.filter(line => 
                /团号|接机|送机|客人/.test(line) && line.length > 20
            );

            // 6. 综合判断
            features.estimatedOrderCount = Math.max(
                uniqueReferences.length,
                uniqueDates.length,
                uniqueFlights.length,
                orderLines.length,
                1
            );

            features.shouldBeMultiOrder = 
                features.hasMultipleReferences ||
                features.hasMultipleDates ||
                features.hasMultipleFlights ||
                features.hasPagingService ||
                orderLines.length > 1;

            return features;
        }

        /**
         * 处理分析进度
         * @param {string} message - 进度消息
         * @param {number} progress - 进度百分比
         */
        handleAnalysisProgress(message, progress) {
            this.updateGeminiStatus(message);
            this.updateProgressIndicator(progress);
        }

        /**
         * 处理分析结果
         * @param {object} result - 分析结果
         */
        handleAnalysisResult(result) {
            // 🔧 修复：Gemini服务已自动管理isAnalyzing状态，此处无需手动设置
            this.hideProgressIndicator();

            if (result.success && result.data) {
                // 更新应用状态
                getAppState().setCurrentOrder({
                    rawText: this.elements.orderInput.value,
                    parsedData: result.data,
                    confidence: result.confidence || this.calculateDataConfidence(result.data),
                    timestamp: result.timestamp || Date.now(),
                    source: 'realtime'
                });

                // 更新状态显示
                const confidence = result.confidence || this.calculateDataConfidence(result.data);
                this.updateGeminiStatus(`✅ 分析完成 (置信度: ${confidence}%)`);

                // 显示预览
                this.showPreviewModal();

                // 处理价格转换
                if (result.data && window.OTA.managers.PriceManager) {
                    const priceManager = new window.OTA.managers.PriceManager(this.elements);
                    priceManager.processPriceConversion(result.data);
                }

                // 🔄 改进说明：多订单检测现在在统一入口处理，此处不再需要触发
                // 单订单模式下的数据已经经过统一入口的多订单检测筛选
                // 只有orderCount=1的情况才会到达这里，无需再次触发多订单检测

                getLogger().log('实时分析完成', 'success', {
                    confidence: confidence,
                    dataKeys: Object.keys(result.data)
                });
            } else {
                this.handleAnalysisError(new Error(result.message || '分析失败'));
            }
        }

        /**
         * 处理分析错误
         * @param {Error} error - 错误对象
         */
        handleAnalysisError(error) {
            // 🔧 修复：Gemini服务已自动管理isAnalyzing状态，此处无需手动设置  
            this.hideProgressIndicator();
            this.updateGeminiStatus('❌ 分析失败，请检查输入');
            getLogger().log('实时分析失败', 'error', { error: error.message });
        }

        /**
         * 显示进度指示器
         */
        showProgressIndicator() {
            if (this.realtimeAnalysis.progressIndicator) {
                this.realtimeAnalysis.progressIndicator.style.display = 'block';
            }
        }

        /**
         * 隐藏进度指示器
         */
        hideProgressIndicator() {
            if (this.realtimeAnalysis.progressIndicator) {
                this.realtimeAnalysis.progressIndicator.style.display = 'none';
            }
        }

        /**
         * 更新进度指示器
         * @param {number} progress - 进度百分比
         */
        updateProgressIndicator(progress) {
            if (this.realtimeAnalysis.progressIndicator) {
                const progressFill = this.realtimeAnalysis.progressIndicator.querySelector('.progress-fill');
                if (progressFill) {
                    progressFill.style.width = `${Math.min(progress, 100)}%`;
                }
            }
        }

        /**
         * 清除实时分析状态
         */
        clearRealtimeAnalysis() {
            // 清除定时器
            if (this.realtimeAnalysis.debounceTimer) {
                clearTimeout(this.realtimeAnalysis.debounceTimer);
                this.realtimeAnalysis.debounceTimer = null;
            }

            // 重置状态（🔧 修复：Gemini服务已自动管理isAnalyzing状态）
            this.hideProgressIndicator();
        }

        /**
         * 更新Gemini状态显示
         * @param {string} status - 状态文本
         */
        updateGeminiStatus(status) {
            if (window.OTA.managers.StateManager) {
                const stateManager = new window.OTA.managers.StateManager(this.elements);
                stateManager.updateGeminiStatus(status);
            }
        }

        /**
         * 显示预览模态框
         */
        showPreviewModal() {
            if (this.uiManager && this.uiManager.showPreviewModal) {
                this.uiManager.showPreviewModal();
            }
        }

        /**
         * 计算数据置信度
         * @param {object} orderData - 订单数据
         * @returns {number} 置信度百分比
         */
        calculateDataConfidence(orderData) {
            if (!orderData || typeof orderData !== 'object') {
                return 0;
            }

            // 重要字段权重
            const importantFields = {
                'customer_name': 15,
                'customer_contact': 10,
                'pickup_location': 20,
                'dropoff_location': 20,
                'pickup_date': 15,
                'pickup_time': 10,
                'ota_price': 5,
                'ota_reference_number': 5
            };

            let filledWeight = 0;
            let totalWeight = 0;

            for (const [field, weight] of Object.entries(importantFields)) {
                totalWeight += weight;
                const value = orderData[field];
                if (value !== null && value !== undefined && value !== '' && value !== 0) {
                    filledWeight += weight;
                }
            }

            return Math.round((filledWeight / totalWeight) * 100);
        }



        /**
         * 启用/禁用实时分析
         * @param {boolean} enabled - 是否启用
         */
        setRealtimeAnalysisEnabled(enabled) {
            this.realtimeAnalysis.enabled = enabled;
            
            if (!enabled) {
                this.clearRealtimeAnalysis();
            }

            getLogger().log(`实时分析${enabled ? '已启用' : '已禁用'}`, 'info');
        }

        /**
         * 获取实时分析状态
         * @returns {object} 实时分析状态
         */
        getRealtimeAnalysisStatus() {
            return {
                enabled: this.realtimeAnalysis.enabled,
                isAnalyzing: getGeminiService().getStatus().isAnalyzing, // 🔧 修复：使用Gemini服务的统一状态
                lastAnalysisTime: this.realtimeAnalysis.lastAnalysisTime
            };
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.RealtimeAnalysisManager = RealtimeAnalysisManager;

})();
