/**
 * 日志管理器模块
 * 负责记录系统操作、API调用、错误信息等
 * 重构为传统script标签加载方式
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

class Logger {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000; // 最大日志条数
        this.debugMode = false;
        this.listeners = [];
        this.originalConsole = {}; // 存储原始console方法
        
        // 日志级别
        this.levels = {
            debug: 0,
            info: 1,
            success: 2,
            warning: 3,
            error: 4
        };
        
        // 全局监控配置
        this.monitoring = {
            enabled: true,
            factoryFunctions: new Map(), // 工厂函数调用统计
            performanceMetrics: new Map(), // 性能指标
            userInteractions: [], // 用户交互记录
            systemStates: new Map(), // 系统状态变化
            apiCalls: new Map(), // API调用统计
            errorTracking: new Map(), // 错误跟踪
            realTimeConsole: true // 实时控制台输出
        };
        
        // 性能监控器
        this.perfObserver = null;
        
        this.loadFromStorage();
        this.interceptConsole();
        this.initializeGlobalMonitoring();
    }
    
    /**
     * 从本地存储加载日志
     */
    loadFromStorage() {
        try {
            const saved = localStorage.getItem('ota-system-logs');
            if (saved) {
                this.logs = JSON.parse(saved);
                // 限制日志数量
                if (this.logs.length > this.maxLogs) {
                    this.logs = this.logs.slice(-this.maxLogs);
                }
            }
        } catch (error) {
            console.warn('加载日志失败:', error);
            this.logs = [];
        }
    }
    
    /**
     * 保存日志到本地存储
     */
    saveToStorage() {
        try {
            // 只保存最近的日志
            const logsToSave = this.logs.slice(-this.maxLogs);
            localStorage.setItem('ota-system-logs', JSON.stringify(logsToSave));
        } catch (error) {
            console.warn('保存日志失败:', error);
        }
    }
    
    /**
     * 拦截控制台输出
     */
    interceptConsole() {
        // 保存原始console方法
        this.originalConsole = {
            log: console.log.bind(console),
            info: console.info.bind(console),
            warn: console.warn.bind(console),
            error: console.error.bind(console),
            group: console.group.bind(console),
            groupCollapsed: console.groupCollapsed.bind(console),
            groupEnd: console.groupEnd.bind(console),
            time: console.time.bind(console),
            timeEnd: console.timeEnd.bind(console),
            // 添加table方法，确保兼容性和安全性
            table: this.createSafeTableMethod()
        };
        
        console.log = (...args) => {
            this.log(args.join(' '), 'info', null, false);
            this.originalConsole.log.apply(console, args);
        };
        
        console.info = (...args) => {
            this.log(args.join(' '), 'info', null, false);
            this.originalConsole.info.apply(console, args);
        };
        
        console.warn = (...args) => {
            this.log(args.join(' '), 'warning', null, false);
            this.originalConsole.warn.apply(console, args);
        };
        
        console.error = (...args) => {
            this.log(args.join(' '), 'error', null, false);
            this.originalConsole.error.apply(console, args);
        };
    }
    
    /**
     * 兼容性table方法实现
     * 在不支持console.table的浏览器中提供替代方案
     * @param {Object|Array} data - 要显示的数据
     * @param {Array} columns - 要显示的列（可选）
     */
    fallbackTable(data, columns = null) {
        try {
            if (!data) {
                this.originalConsole.log('(没有数据)');
                return;
            }
            
            // 防止循环引用和其他问题
            let safeData;
            try {
                safeData = JSON.parse(JSON.stringify(data));
            } catch (e) {
                safeData = String(data);
            }
            
            // 如果是对象，转换为数组格式
            let tableData = Array.isArray(safeData) ? safeData : Object.entries(safeData);
            
            // 如果是简单的键值对对象
            if (!Array.isArray(safeData) && typeof safeData === 'object') {
                this.originalConsole.log('📋 数据表格:');
                Object.entries(safeData).forEach(([key, value]) => {
                    this.originalConsole.log(`  ${key}: ${value}`);
                });
                return;
            }
            
            // 如果是数组，显示为列表
            if (Array.isArray(tableData) && tableData.length > 0) {
                this.originalConsole.log('📋 数据表格:');
                tableData.forEach((item, index) => {
                    if (typeof item === 'object' && item !== null) {
                        this.originalConsole.log(`  [${index}]:`);
                        Object.entries(item).forEach(([key, value]) => {
                            this.originalConsole.log(`    ${key}: ${value}`);
                        });
                    } else {
                        this.originalConsole.log(`  [${index}]: ${item}`);
                    }
                });
            } else {
                this.originalConsole.log('(空数据)');
            }
        } catch (error) {
            this.originalConsole.log('⚠️ 表格数据显示失败:', error.message);
            this.originalConsole.log('原始数据:', data);
        }
    }

    /**
     * 创建安全的table方法
     * 提供更强的兼容性和错误处理
     * @returns {function} 安全的table方法
     */
    createSafeTableMethod() {
        // 首先尝试使用原生console.table
        if (typeof console.table === 'function') {
            return (...args) => {
                try {
                    console.table.apply(console, args);
                } catch (error) {
                    // 如果原生方法失败，使用fallback
                    this.fallbackTable.apply(this, args);
                }
            };
        }
        
        // 如果原生方法不存在，直接使用fallback
        return this.fallbackTable.bind(this);
    }

    /**
     * 初始化全局监控系统
     */
    initializeGlobalMonitoring() {
        if (!this.monitoring.enabled) return;
        
        this.log('🔍 初始化全局监控系统...', 'info', {
            type: 'monitoring_init',
            features: ['工厂函数监控', '性能监控', '用户交互监控', '系统状态监控', 'API调用监控', '错误跟踪']
        });
        
        // 初始化性能观察器
        this.initializePerformanceObserver();
        
        // 监控未捕获的错误
        this.setupGlobalErrorHandling();
        
        // 监控DOM事件
        this.setupDOMEventMonitoring();
        
        // 监控网络状态
        this.setupNetworkStatusMonitoring();
        
        // 定期清理监控数据
        this.setupMonitoringCleanup();
        
        this.log('✅ 全局监控系统初始化完成', 'success', {
            type: 'monitoring_ready'
        });
    }
    
    /**
     * 初始化性能观察器
     */
    initializePerformanceObserver() {
        try {
            if ('PerformanceObserver' in window) {
                this.perfObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        this.logPerformanceMetric(entry);
                    });
                });
                
                this.perfObserver.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
                this.log('性能观察器已启动', 'info', { type: 'performance_observer_init' });
            }
        } catch (error) {
            this.log('性能观察器初始化失败', 'warning', { error: error.message });
        }
    }
    
    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 捕获JavaScript错误
        window.addEventListener('error', (event) => {
            this.logGlobalError('JavaScript错误', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        // 捕获Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.logGlobalError('未处理的Promise拒绝', {
                reason: event.reason,
                promise: event.promise
            });
        });
        
        this.log('全局错误处理已设置', 'info', { type: 'global_error_handler_init' });
    }
    
    /**
     * 设置DOM事件监控
     */
    setupDOMEventMonitoring() {
        const interactionEvents = ['click', 'submit', 'change', 'input', 'focus', 'blur'];
        
        interactionEvents.forEach(eventType => {
            document.addEventListener(eventType, (event) => {
                this.logUserInteraction(eventType, {
                    target: this.getElementInfo(event.target),
                    timestamp: Date.now()
                });
            }, true);
        });
        
        this.log('DOM事件监控已设置', 'info', { 
            type: 'dom_event_monitoring_init',
            events: interactionEvents 
        });
    }
    
    /**
     * 设置网络状态监控
     */
    setupNetworkStatusMonitoring() {
        window.addEventListener('online', () => {
            this.logSystemStateChange('network', 'online', '网络已连接');
        });
        
        window.addEventListener('offline', () => {
            this.logSystemStateChange('network', 'offline', '网络已断开');
        });
        
        this.log('网络状态监控已设置', 'info', { type: 'network_monitoring_init' });
    }
    
    /**
     * 设置监控数据清理
     */
    setupMonitoringCleanup() {
        // 每10分钟清理一次旧的监控数据
        setInterval(() => {
            this.cleanupMonitoringData();
        }, 10 * 60 * 1000);
        
        this.log('监控数据清理定时器已设置', 'info', { type: 'monitoring_cleanup_init' });
    }
    
    /**
     * 记录日志
     * @param {string} message - 日志消息
     * @param {string} level - 日志级别
     * @param {any} data - 附加数据
     * @param {boolean} save - 是否保存到存储
     */
    log(message, level = 'info', data = null, save = true) {
        const logEntry = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            level: level.toLowerCase(),
            message: String(message),
            data: data ? this.sanitizeData(data) : null,
            source: this.getCallerInfo()
        };
        
        this.logs.push(logEntry);
        
        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }
        
        if (save) {
            this.saveToStorage();
        }
        
        // 通知监听器
        this.notifyListeners(logEntry);
        
        // 调试模式下输出到控制台
        if (this.debugMode) {
            this.outputToConsole(logEntry);
        }
    }
    
    /**
     * 获取调用者信息
     * @returns {string} 调用者信息
     */
    getCallerInfo() {
        try {
            const stack = new Error().stack;
            const lines = stack.split('\n');
            
            // 找到第一个不是logger的调用
            for (let i = 3; i < lines.length; i++) {
                const line = lines[i];
                if (line && !line.includes('logger.js') && !line.includes('console.')) {
                    const match = line.match(/at\s+(.+?)\s+\(/);
                    if (match) {
                        return match[1];
                    }
                }
            }
        } catch (error) {
            // 忽略错误
        }
        return 'unknown';
    }
    
    /**
     * 清理数据，避免循环引用
     * @param {any} data - 原始数据
     * @returns {any} 清理后的数据
     */
    sanitizeData(data) {
        try {
            // 使用JSON序列化来避免循环引用
            return JSON.parse(JSON.stringify(data, (key, value) => {
                // 过滤敏感信息
                if (typeof key === 'string' && 
                    (key.toLowerCase().includes('password') || 
                     key.toLowerCase().includes('token') ||
                     key.toLowerCase().includes('key'))) {
                    return '[FILTERED]';
                }
                return value;
            }));
        } catch (error) {
            return { error: '无法序列化数据', type: typeof data };
        }
    }
    
    /**
     * 输出到控制台
     * @param {object} logEntry - 日志条目
     */
    outputToConsole(logEntry) {
        if (!this.monitoring.realTimeConsole) return;
        
        const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
        const prefix = `[${timestamp}] [${logEntry.level.toUpperCase()}]`;
        
        // 根据日志类型使用不同的控制台样式
        const styles = this.getConsoleStyles(logEntry.level, logEntry.data?.type);
        
        // 使用原始console方法避免递归
        switch (logEntry.level) {
            case 'error':
                this.originalConsole.error(
                    `%c${prefix}%c ${logEntry.message}`,
                    styles.prefix,
                    styles.message,
                    logEntry.data || ''
                );
                break;
            case 'warning':
                this.originalConsole.warn(
                    `%c${prefix}%c ${logEntry.message}`,
                    styles.prefix,
                    styles.message,
                    logEntry.data || ''
                );
                break;
            case 'success':
                this.originalConsole.info(
                    `%c${prefix}%c ${logEntry.message}`,
                    styles.prefix,
                    styles.message,
                    logEntry.data || ''
                );
                break;
            case 'debug':
                this.originalConsole.log(
                    `%c${prefix}%c ${logEntry.message}`,
                    styles.prefix,
                    styles.message,
                    logEntry.data || ''
                );
                break;
            default:
                this.originalConsole.log(
                    `%c${prefix}%c ${logEntry.message}`,
                    styles.prefix,
                    styles.message,
                    logEntry.data || ''
                );
        }
        
        // 如果有性能数据，输出性能信息
        if (logEntry.data && logEntry.data.performance) {
            this.outputPerformanceToConsole(logEntry.data.performance);
        }
    }
    
    /**
     * 获取控制台样式
     * @param {string} level - 日志级别
     * @param {string} type - 日志类型
     * @returns {object} 样式对象
     */
    getConsoleStyles(level, type) {
        const baseStyles = {
            prefix: 'font-weight: bold; padding: 2px 4px; border-radius: 3px;',
            message: 'font-weight: normal;'
        };
        
        // 根据级别设置颜色
        const levelColors = {
            error: 'background: #ff4757; color: white;',
            warning: 'background: #ffa502; color: white;',
            success: 'background: #2ed573; color: white;',
            info: 'background: #3742fa; color: white;',
            debug: 'background: #747d8c; color: white;'
        };
        
        // 根据类型添加特殊样式
        const typeColors = {
            factory_function: 'background: #8e44ad; color: white;',
            performance: 'background: #e67e22; color: white;',
            user_interaction: 'background: #27ae60; color: white;',
            system_state: 'background: #34495e; color: white;',
            api_call: 'background: #2980b9; color: white;',
            monitoring: 'background: #95a5a6; color: white;'
        };
        
        return {
            prefix: baseStyles.prefix + (typeColors[type] || levelColors[level] || levelColors.info),
            message: baseStyles.message
        };
    }
    
    /**
     * 输出性能信息到控制台
     * @param {object} performance - 性能数据
     */
    outputPerformanceToConsole(performance) {
        if (performance.duration && performance.duration > 100) {
            this.originalConsole.group(
                `%c⚡ 性能警告`,
                'color: #e67e22; font-weight: bold;'
            );
            this.originalConsole.log(`执行时间: ${performance.duration}ms`);
            this.originalConsole.log(`函数: ${performance.functionName}`);
            if (performance.memory) {
                this.originalConsole.log(`内存使用: ${(performance.memory / 1024 / 1024).toFixed(2)}MB`);
            }
            this.originalConsole.groupEnd();
        }
    }
    
    /**
     * 记录工厂函数调用
     * @param {string} functionName - 函数名称
     * @param {number} duration - 执行时间
     * @param {any} result - 返回结果
     * @param {object} context - 上下文信息
     */
    logFactoryFunctionCall(functionName, duration, result, context = {}) {
        const key = functionName;
        const existing = this.monitoring.factoryFunctions.get(key) || {
            count: 0,
            totalDuration: 0,
            lastCalled: null,
            errors: 0
        };
        
        existing.count++;
        existing.totalDuration += duration;
        existing.lastCalled = Date.now();
        existing.averageDuration = existing.totalDuration / existing.count;
        
        if (context.error) {
            existing.errors++;
        }
        
        this.monitoring.factoryFunctions.set(key, existing);
        
        const message = `🏭 工厂函数调用: ${functionName}`;
        const data = {
            type: 'factory_function',
            functionName,
            duration: `${duration}ms`,
            callCount: existing.count,
            averageDuration: `${existing.averageDuration.toFixed(2)}ms`,
            hasResult: Boolean(result),
            context,
            performance: {
                functionName,
                duration,
                memory: this.getMemoryUsage()
            }
        };
        
        const level = duration > 100 ? 'warning' : 'info';
        this.log(message, level, data);
    }
    
    /**
     * 记录性能指标
     * @param {PerformanceEntry} entry - 性能条目
     */
    logPerformanceMetric(entry) {
        const key = `${entry.entryType}_${entry.name}`;
        const existing = this.monitoring.performanceMetrics.get(key) || {
            count: 0,
            totalDuration: 0,
            maxDuration: 0,
            minDuration: Infinity
        };
        
        existing.count++;
        existing.totalDuration += entry.duration;
        existing.maxDuration = Math.max(existing.maxDuration, entry.duration);
        existing.minDuration = Math.min(existing.minDuration, entry.duration);
        existing.averageDuration = existing.totalDuration / existing.count;
        
        this.monitoring.performanceMetrics.set(key, existing);
        
        // 只记录较慢的操作
        if (entry.duration > 50) {
            const message = `⚡ 性能指标: ${entry.name}`;
            const data = {
                type: 'performance',
                entryType: entry.entryType,
                name: entry.name,
                duration: `${entry.duration.toFixed(2)}ms`,
                averageDuration: `${existing.averageDuration.toFixed(2)}ms`,
                count: existing.count,
                performance: {
                    functionName: entry.name,
                    duration: entry.duration,
                    memory: this.getMemoryUsage()
                }
            };
            
            const level = entry.duration > 200 ? 'warning' : 'info';
            this.log(message, level, data);
        }
    }
    
    /**
     * 记录用户交互
     * @param {string} eventType - 事件类型
     * @param {object} details - 详细信息
     */
    logUserInteraction(eventType, details) {
        // 限制用户交互日志的频率
        const now = Date.now();
        const lastInteraction = this.monitoring.userInteractions[this.monitoring.userInteractions.length - 1];
        
        if (lastInteraction && (now - lastInteraction.timestamp) < 100) {
            return; // 防止过于频繁的记录
        }
        
        const interaction = {
            eventType,
            timestamp: now,
            details
        };
        
        this.monitoring.userInteractions.push(interaction);
        
        // 保持最近的100个交互记录
        if (this.monitoring.userInteractions.length > 100) {
            this.monitoring.userInteractions = this.monitoring.userInteractions.slice(-100);
        }
        
        // 只记录重要的用户交互
        const importantEvents = ['click', 'submit', 'change'];
        if (importantEvents.includes(eventType)) {
            const message = `👆 用户交互: ${eventType}`;
            const data = {
                type: 'user_interaction',
                eventType,
                element: details.target,
                timestamp: now
            };
            
            this.log(message, 'debug', data);
        }
    }
    
    /**
     * 记录系统状态变化
     * @param {string} stateType - 状态类型
     * @param {any} newValue - 新值
     * @param {string} description - 描述
     */
    logSystemStateChange(stateType, newValue, description) {
        const existing = this.monitoring.systemStates.get(stateType);
        
        // 如果状态没有变化，不记录
        if (existing && existing.value === newValue) {
            return;
        }
        
        this.monitoring.systemStates.set(stateType, {
            value: newValue,
            timestamp: Date.now(),
            previousValue: existing?.value
        });
        
        const message = `🔄 系统状态变化: ${description}`;
        const data = {
            type: 'system_state',
            stateType,
            newValue,
            previousValue: existing?.value,
            description
        };
        
        this.log(message, 'info', data);
    }
    
    /**
     * 记录全局错误
     * @param {string} errorType - 错误类型
     * @param {object} errorDetails - 错误详情
     */
    logGlobalError(errorType, errorDetails) {
        const key = `${errorType}_${errorDetails.message || 'unknown'}`;
        const existing = this.monitoring.errorTracking.get(key) || {
            count: 0,
            firstOccurrence: Date.now(),
            lastOccurrence: null
        };
        
        existing.count++;
        existing.lastOccurrence = Date.now();
        
        this.monitoring.errorTracking.set(key, existing);
        
        const message = `❌ 全局错误: ${errorType}`;
        const data = {
            type: 'global_error',
            errorType,
            details: errorDetails,
            count: existing.count,
            firstOccurrence: existing.firstOccurrence
        };
        
        this.log(message, 'error', data);
    }
    
    /**
     * 获取元素信息
     * @param {Element} element - DOM元素
     * @returns {object} 元素信息
     */
    getElementInfo(element) {
        if (!element) return null;
        
        return {
            tagName: element.tagName?.toLowerCase(),
            id: element.id,
            className: element.className,
            name: element.name,
            type: element.type,
            value: element.type === 'password' ? '[HIDDEN]' : element.value?.substring(0, 50)
        };
    }
    
    /**
     * 获取内存使用情况
     * @returns {number} 内存使用量（字节）
     */
    getMemoryUsage() {
        try {
            if (performance.memory) {
                return performance.memory.usedJSHeapSize;
            }
        } catch (error) {
            // 忽略错误
        }
        return null;
    }
    
    /**
     * 清理监控数据
     */
    cleanupMonitoringData() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30分钟
        
        // 清理旧的用户交互记录
        this.monitoring.userInteractions = this.monitoring.userInteractions.filter(
            interaction => (now - interaction.timestamp) < maxAge
        );
        
        this.log('监控数据已清理', 'debug', {
            type: 'monitoring_cleanup',
            userInteractionsCount: this.monitoring.userInteractions.length
        });
    }
    
    /**
     * 记录API调用
     * @param {string} url - API URL
     * @param {string} method - HTTP方法
     * @param {object} requestData - 请求数据
     * @param {object} responseData - 响应数据
     * @param {number} duration - 请求耗时
     */
    logApiCall(url, method, requestData = null, responseData = null, duration = null) {
        const message = `API调用: ${method.toUpperCase()} ${url}`;
        const data = {
            type: 'api_call',
            url,
            method: method.toUpperCase(),
            request: requestData,
            response: responseData,
            duration: duration ? `${duration}ms` : null
        };
        
        this.log(message, responseData?.status === false ? 'error' : 'info', data);
    }
    
    /**
     * 记录用户操作
     * @param {string} action - 操作类型
     * @param {object} details - 操作详情
     */
    logUserAction(action, details = null) {
        const message = `用户操作: ${action}`;
        const data = {
            type: 'user_action',
            action,
            details,
            user: this.getCurrentUser()
        };
        
        this.log(message, 'info', data);
    }
    
    /**
     * 记录Gemini AI交互
     * @param {string} input - 输入内容
     * @param {object} output - 输出结果
     * @param {number} confidence - 置信度
     */
    logGeminiInteraction(input, output, confidence = null) {
        const message = `Gemini AI解析: ${input.substring(0, 50)}...`;
        const data = {
            type: 'gemini_interaction',
            input: input.substring(0, 200), // 限制长度
            output,
            confidence,
            inputLength: input.length
        };
        
        this.log(message, output.success ? 'success' : 'warning', data);
    }
    
    /**
     * 记录数据变更
     * @param {string} field - 字段名
     * @param {any} oldValue - 旧值
     * @param {any} newValue - 新值
     * @param {string} source - 变更来源
     */
    logDataChange(field, oldValue, newValue, source = 'manual') {
        const message = `数据变更: ${field}`;
        const data = {
            type: 'data_change',
            field,
            oldValue,
            newValue,
            source
        };
        
        this.log(message, 'info', data);
    }
    
    /**
     * 记录错误
     * @param {Error|string} error - 错误对象或消息
     * @param {object} context - 错误上下文
     */
    logError(error, context = null) {
        const message = error instanceof Error ? error.message : String(error);
        const data = {
            type: 'error',
            error: error instanceof Error ? {
                message: error.message,
                stack: error.stack,
                name: error.name
            } : error,
            context
        };
        
        this.log(message, 'error', data);
    }
    
    /**
     * 获取当前用户信息
     * @returns {object|null} 用户信息
     */
    getCurrentUser() {
        try {
            return window.appState?.get('auth.user') || null;
        } catch (error) {
            return null;
        }
    }
    
    /**
     * 设置调试模式
     * @param {boolean} enabled - 是否启用
     */
    setDebugMode(enabled) {
        this.debugMode = Boolean(enabled);
        this.monitoring.realTimeConsole = enabled;
        this.log(`调试模式${enabled ? '启用' : '禁用'}`, 'info');
    }
    
    /**
     * 启用/禁用全局监控
     * @param {boolean} enabled - 是否启用
     */
    setMonitoringEnabled(enabled) {
        this.monitoring.enabled = Boolean(enabled);
        this.log(`全局监控${enabled ? '启用' : '禁用'}`, 'info', {
            type: 'monitoring_control'
        });
    }
    
    /**
     * 获取监控报告
     * @returns {object} 监控报告
     */
    getMonitoringReport() {
        const report = {
            timestamp: new Date().toISOString(),
            factoryFunctions: this.getFactoryFunctionStats(),
            performance: this.getPerformanceStats(),
            userInteractions: this.getUserInteractionStats(),
            systemStates: this.getSystemStateStats(),
            errors: this.getErrorStats(),
            summary: {}
        };
        
        // 生成摘要
        report.summary = {
            totalFactoryFunctionCalls: report.factoryFunctions.totalCalls,
            averagePerformance: report.performance.averageDuration,
            userInteractionCount: report.userInteractions.totalInteractions,
            errorCount: report.errors.totalErrors,
            systemHealth: this.calculateSystemHealth(report)
        };
        
        return report;
    }
    
    /**
     * 获取工厂函数统计
     * @returns {object} 工厂函数统计
     */
    getFactoryFunctionStats() {
        const stats = {
            totalCalls: 0,
            functions: {},
            slowestFunction: null,
            mostCalledFunction: null
        };
        
        let slowestDuration = 0;
        let mostCalls = 0;
        
        this.monitoring.factoryFunctions.forEach((data, functionName) => {
            stats.totalCalls += data.count;
            stats.functions[functionName] = {
                callCount: data.count,
                averageDuration: data.averageDuration.toFixed(2) + 'ms',
                totalDuration: data.totalDuration.toFixed(2) + 'ms',
                errorRate: data.errors > 0 ? ((data.errors / data.count) * 100).toFixed(1) + '%' : '0%',
                lastCalled: new Date(data.lastCalled).toLocaleTimeString()
            };
            
            if (data.averageDuration > slowestDuration) {
                slowestDuration = data.averageDuration;
                stats.slowestFunction = functionName;
            }
            
            if (data.count > mostCalls) {
                mostCalls = data.count;
                stats.mostCalledFunction = functionName;
            }
        });
        
        return stats;
    }
    
    /**
     * 获取性能统计
     * @returns {object} 性能统计
     */
    getPerformanceStats() {
        const stats = {
            totalMeasures: 0,
            averageDuration: 0,
            slowestOperations: [],
            memoryUsage: this.getMemoryUsage()
        };
        
        let totalDuration = 0;
        const operations = [];
        
        this.monitoring.performanceMetrics.forEach((data, operation) => {
            stats.totalMeasures += data.count;
            totalDuration += data.totalDuration;
            
            operations.push({
                name: operation,
                averageDuration: data.averageDuration.toFixed(2) + 'ms',
                maxDuration: data.maxDuration.toFixed(2) + 'ms',
                count: data.count
            });
        });
        
        if (stats.totalMeasures > 0) {
            stats.averageDuration = (totalDuration / stats.totalMeasures).toFixed(2) + 'ms';
        }
        
        // 获取最慢的5个操作
        stats.slowestOperations = operations
            .sort((a, b) => parseFloat(b.maxDuration) - parseFloat(a.maxDuration))
            .slice(0, 5);
        
        return stats;
    }
    
    /**
     * 获取用户交互统计
     * @returns {object} 用户交互统计
     */
    getUserInteractionStats() {
        const stats = {
            totalInteractions: this.monitoring.userInteractions.length,
            byEventType: {},
            recentInteractions: []
        };
        
        // 按事件类型统计
        this.monitoring.userInteractions.forEach(interaction => {
            stats.byEventType[interaction.eventType] = 
                (stats.byEventType[interaction.eventType] || 0) + 1;
        });
        
        // 获取最近10个交互
        stats.recentInteractions = this.monitoring.userInteractions
            .slice(-10)
            .map(interaction => ({
                eventType: interaction.eventType,
                timestamp: new Date(interaction.timestamp).toLocaleTimeString(),
                element: interaction.details.target?.tagName?.toLowerCase()
            }));
        
        return stats;
    }
    
    /**
     * 获取系统状态统计
     * @returns {object} 系统状态统计
     */
    getSystemStateStats() {
        const stats = {
            currentStates: {},
            stateChanges: 0
        };
        
        this.monitoring.systemStates.forEach((data, stateType) => {
            stats.currentStates[stateType] = {
                value: data.value,
                lastChanged: new Date(data.timestamp).toLocaleTimeString()
            };
            stats.stateChanges++;
        });
        
        return stats;
    }
    
    /**
     * 获取错误统计
     * @returns {object} 错误统计
     */
    getErrorStats() {
        const stats = {
            totalErrors: 0,
            uniqueErrors: 0,
            errorTypes: {},
            recentErrors: []
        };
        
        this.monitoring.errorTracking.forEach((data, errorKey) => {
            stats.totalErrors += data.count;
            stats.uniqueErrors++;
            
            const [errorType] = errorKey.split('_');
            stats.errorTypes[errorType] = (stats.errorTypes[errorType] || 0) + data.count;
            
            if (data.lastOccurrence > Date.now() - 5 * 60 * 1000) { // 最近5分钟
                stats.recentErrors.push({
                    type: errorType,
                    count: data.count,
                    lastOccurrence: new Date(data.lastOccurrence).toLocaleTimeString()
                });
            }
        });
        
        return stats;
    }
    
    /**
     * 计算系统健康度
     * @param {object} report - 监控报告
     * @returns {string} 健康度评级
     */
    calculateSystemHealth(report) {
        let score = 100;
        
        // 错误率影响
        if (report.errors.totalErrors > 0) {
            score -= Math.min(report.errors.totalErrors * 5, 30);
        }
        
        // 性能影响
        const avgPerf = parseFloat(report.performance.averageDuration) || 0;
        if (avgPerf > 100) {
            score -= Math.min((avgPerf - 100) / 10, 20);
        }
        
        // 工厂函数错误率影响
        Object.values(report.factoryFunctions.functions).forEach(func => {
            const errorRate = parseFloat(func.errorRate) || 0;
            score -= errorRate;
        });
        
        if (score >= 90) return '优秀';
        if (score >= 80) return '良好';
        if (score >= 70) return '一般';
        if (score >= 60) return '较差';
        return '糟糕';
    }
    
    /**
     * 输出监控报告到控制台
     */
    printMonitoringReport() {
        const report = this.getMonitoringReport();
        
        this.originalConsole.group(
            `%c📊 系统监控报告 - ${new Date().toLocaleTimeString()}`,
            'color: #3498db; font-size: 16px; font-weight: bold;'
        );
        
        // 系统健康度
        this.originalConsole.log(
            `%c🏥 系统健康度: ${report.summary.systemHealth}`,
            'color: #2ecc71; font-weight: bold;'
        );
        
        // 工厂函数统计
        this.originalConsole.group('%c🏭 工厂函数调用统计', 'color: #9b59b6; font-weight: bold;');
        this.originalConsole.log(`总调用次数: ${report.summary.totalFactoryFunctionCalls}`);
        this.originalConsole.log(`最慢函数: ${report.factoryFunctions.slowestFunction || '无'}`);
        this.originalConsole.log(`最常调用: ${report.factoryFunctions.mostCalledFunction || '无'}`);
        // 安全地调用table方法
        this.safeTableCall('工厂函数详情', report.factoryFunctions.functions);
        this.originalConsole.groupEnd();
        
        // 性能统计
        this.originalConsole.group('%c⚡ 性能统计', 'color: #e67e22; font-weight: bold;');
        this.originalConsole.log(`平均响应时间: ${report.performance.averageDuration}`);
        this.originalConsole.log(`内存使用: ${report.performance.memoryUsage ? (report.performance.memoryUsage / 1024 / 1024).toFixed(2) + 'MB' : '未知'}`);
        if (report.performance.slowestOperations.length > 0) {
            this.originalConsole.log('最慢操作:');
            this.safeTableCall('最慢操作详情', report.performance.slowestOperations);
        }
        this.originalConsole.groupEnd();
        
        // 用户交互统计
        this.originalConsole.group('%c👆 用户交互统计', 'color: #27ae60; font-weight: bold;');
        this.originalConsole.log(`总交互次数: ${report.userInteractions.totalInteractions}`);
        this.originalConsole.log('按事件类型分布:');
        this.safeTableCall('交互类型详情', report.userInteractions.byEventType);
        this.originalConsole.groupEnd();
        
        // 错误统计
        if (report.errors.totalErrors > 0) {
            this.originalConsole.group('%c❌ 错误统计', 'color: #e74c3c; font-weight: bold;');
            this.originalConsole.log(`总错误数: ${report.errors.totalErrors}`);
            this.originalConsole.log(`唯一错误数: ${report.errors.uniqueErrors}`);
            this.safeTableCall('错误类型详情', report.errors.errorTypes);
            this.originalConsole.groupEnd();
        }
        
        this.originalConsole.groupEnd();
        
        return report;
    }
    
    /**
     * 安全调用table方法的包装器
     * @param {string} label - 数据标签  
     * @param {*} data - 要显示的数据
     */
    safeTableCall(label, data) {
        try {
            if (this.originalConsole && typeof this.originalConsole.table === 'function') {
                this.originalConsole.table(data);
            } else {
                this.fallbackTable(data);
            }
        } catch (error) {
            // 最后的备用方案 - 使用JSON格式显示
            this.originalConsole.log(`${label}:`, JSON.stringify(data, null, 2));
        }
    }

    /**
     * 获取日志列表
     * @param {object} filters - 过滤条件
     * @returns {Array} 日志列表
     */
    getLogs(filters = {}) {
        let filteredLogs = [...this.logs];
        
        // 按级别过滤
        if (filters.level) {
            filteredLogs = filteredLogs.filter(log => log.level === filters.level);
        }
        
        // 按时间范围过滤
        if (filters.startTime) {
            const startTime = new Date(filters.startTime);
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startTime);
        }
        
        if (filters.endTime) {
            const endTime = new Date(filters.endTime);
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endTime);
        }
        
        // 按消息内容过滤
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredLogs = filteredLogs.filter(log => 
                log.message.toLowerCase().includes(searchTerm)
            );
        }
        
        // 按数量限制
        if (filters.limit) {
            filteredLogs = filteredLogs.slice(-filters.limit);
        }
        
        return filteredLogs.reverse(); // 最新的在前面
    }
    
    /**
     * 获取统计信息
     * @returns {object} 统计信息
     */
    getStats() {
        const stats = {
            total: this.logs.length,
            byLevel: {},
            byType: {},
            today: 0,
            lastHour: 0
        };
        
        const now = new Date();
        const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        
        this.logs.forEach(log => {
            // 按级别统计
            stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;
            
            // 按类型统计
            if (log.data?.type) {
                stats.byType[log.data.type] = (stats.byType[log.data.type] || 0) + 1;
            }
            
            // 时间统计
            const logTime = new Date(log.timestamp);
            if (logTime >= todayStart) {
                stats.today++;
            }
            if (logTime >= hourAgo) {
                stats.lastHour++;
            }
        });
        
        return stats;
    }
    
    /**
     * 清空日志
     */
    clear() {
        this.logs = [];
        this.saveToStorage();
        this.log('日志已清空', 'info');
        this.notifyListeners({ type: 'clear' });
    }
    
    /**
     * 导出日志
     * @param {object} options - 导出选项
     * @returns {object} 导出结果
     */
    export(options = {}) {
        const filters = options.filters || {};
        const format = options.format || 'json';
        const logs = this.getLogs(filters);
        
        const exportData = {
            metadata: {
                exportTime: new Date().toISOString(),
                totalLogs: logs.length,
                filters,
                stats: this.getStats()
            },
            logs
        };
        
        switch (format) {
            case 'csv':
                return this.exportToCSV(logs);
            case 'txt':
                return this.exportToText(logs);
            default:
                return JSON.stringify(exportData, null, 2);
        }
    }
    
    /**
     * 导出为CSV格式
     * @param {Array} logs - 日志列表
     * @returns {string} CSV字符串
     */
    exportToCSV(logs) {
        const headers = ['时间', '级别', '消息', '来源', '数据'];
        const rows = [headers.join(',')];
        
        logs.forEach(log => {
            const row = [
                `"${log.timestamp}"`,
                `"${log.level}"`,
                `"${log.message.replace(/"/g, '""')}"`,
                `"${log.source}"`,
                `"${log.data ? JSON.stringify(log.data).replace(/"/g, '""') : ''}"`
            ];
            rows.push(row.join(','));
        });
        
        return rows.join('\n');
    }
    
    /**
     * 导出为文本格式
     * @param {Array} logs - 日志列表
     * @returns {string} 文本字符串
     */
    exportToText(logs) {
        return logs.map(log => {
            const timestamp = new Date(log.timestamp).toLocaleString();
            let text = `[${timestamp}] [${log.level.toUpperCase()}] ${log.message}`;
            
            if (log.data) {
                text += `\n  数据: ${JSON.stringify(log.data, null, 2)}`;
            }
            
            return text;
        }).join('\n\n');
    }
    
    /**
     * 添加监听器
     * @param {function} callback - 回调函数
     */
    on(callback) {
        this.listeners.push(callback);
    }
    
    /**
     * 移除监听器
     * @param {function} callback - 回调函数
     */
    off(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }
    
    /**
     * 通知监听器
     * @param {object} logEntry - 日志条目
     */
    notifyListeners(logEntry) {
        this.listeners.forEach(callback => {
            try {
                callback(logEntry);
            } catch (error) {
                // 使用原始console方法避免递归
                this.originalConsole.error('日志监听器错误:', error);
            }
        });
    }
    
    /**
     * 搜索日志
     * @param {string} query - 搜索查询
     * @param {object} options - 搜索选项
     * @returns {Array} 搜索结果
     */
    search(query, options = {}) {
        const searchTerm = query.toLowerCase();
        const caseSensitive = options.caseSensitive || false;
        const searchFields = options.fields || ['message'];
        
        return this.logs.filter(log => {
            return searchFields.some(field => {
                let value = log[field];
                if (field === 'data' && log.data) {
                    value = JSON.stringify(log.data);
                }
                
                if (typeof value === 'string') {
                    const searchValue = caseSensitive ? value : value.toLowerCase();
                    const searchQuery = caseSensitive ? query : searchTerm;
                    return searchValue.includes(searchQuery);
                }
                
                return false;
            });
        });
    }
}

    // 创建全局日志实例
    const logger = new Logger();

    // 记录系统启动
    logger.log('OTA订单处理系统启动', 'info', {
        type: 'system_start',
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
    });

    // 暴露到OTA命名空间
    window.OTA.logger = logger;

    // 向后兼容：暴露到全局window对象
    window.logger = logger;

})();