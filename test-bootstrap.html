<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统启动流程测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            color: #764ba2;
            text-align: center;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        #console {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 OTA系统启动流程测试</h1>
        
        <div>
            <button onclick="testBootstrap()">测试启动流程</button>
            <button onclick="testHealthCheck()">健康检查</button>
            <button onclick="clearConsole()">清空控制台</button>
        </div>
        
        <h2>测试控制台</h2>
        <div id="console"></div>
        
        <div id="results"></div>
    </div>

    <!-- 加载核心架构模块 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    
    <!-- 加载基础服务 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    
    <script>
        // 自定义控制台输出
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '✅';
            const line = `[${timestamp}] ${prefix} ${message}\n`;
            consoleDiv.textContent += line;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            originalLog(message);
        }
        
        console.log = logToConsole;
        console.error = (msg) => logToConsole(msg, 'error');
        console.warn = (msg) => logToConsole(msg, 'warn');
        
        function clearConsole() {
            consoleDiv.textContent = '';
        }
        
        async function testBootstrap() {
            logToConsole('🚀 开始测试启动协调器...');
            
            try {
                // 检查核心模块是否加载
                if (!window.OTA || !window.OTA.ApplicationBootstrap) {
                    logToConsole('❌ 核心模块未正确加载', 'error');
                    return;
                }
                
                // 创建启动协调器
                const bootstrap = new window.OTA.ApplicationBootstrap();
                logToConsole('✅ 启动协调器创建成功');
                
                // 执行启动流程
                const result = await bootstrap.start();
                
                if (result.success) {
                    logToConsole(`✅ 系统启动成功，耗时: ${result.duration.toFixed(2)}ms`);
                    logToConsole(`📊 启动阶段数: ${result.phases ? result.phases.length : '未知'}`);
                    
                    // 显示启动报告
                    if (result.report) {
                        logToConsole('📋 启动报告:');
                        Object.entries(result.report).forEach(([phase, info]) => {
                            logToConsole(`  ${phase}: ${info.duration}ms - ${info.status}`);
                        });
                    }
                    
                    // 测试服务可用性
                    testServiceAvailability();
                    
                } else {
                    logToConsole(`❌ 系统启动失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                logToConsole(`❌ 启动测试失败: ${error.message}`, 'error');
            }
        }
        
        function testServiceAvailability() {
            logToConsole('🔍 测试服务可用性...');
            
            const services = ['logger', 'appState', 'apiService', 'geminiService'];
            let availableCount = 0;
            
            services.forEach(serviceName => {
                try {
                    const service = window.getService(serviceName);
                    if (service) {
                        logToConsole(`✅ ${serviceName} 服务可用`);
                        availableCount++;
                    } else {
                        logToConsole(`❌ ${serviceName} 服务不可用`, 'error');
                    }
                } catch (error) {
                    logToConsole(`❌ ${serviceName} 服务获取失败: ${error.message}`, 'error');
                }
            });
            
            const availability = Math.round((availableCount / services.length) * 100);
            logToConsole(`📊 服务可用性: ${availability}% (${availableCount}/${services.length})`);
        }
        
        async function testHealthCheck() {
            logToConsole('🏥 开始健康检查...');
            
            try {
                // 检查系统组件
                const checks = [
                    { name: 'OTA命名空间', check: () => !!window.OTA },
                    { name: '依赖容器', check: () => !!(window.OTA && window.OTA.container) },
                    { name: '服务定位器', check: () => !!(window.OTA && window.OTA.serviceLocator) },
                    { name: 'getService函数', check: () => typeof window.getService === 'function' },
                    { name: '启动协调器', check: () => !!(window.OTA && window.OTA.ApplicationBootstrap) }
                ];
                
                let healthyCount = 0;
                checks.forEach(({ name, check }) => {
                    try {
                        const result = check();
                        if (result) {
                            logToConsole(`✅ ${name}: 正常`);
                            healthyCount++;
                        } else {
                            logToConsole(`❌ ${name}: 异常`, 'error');
                        }
                    } catch (error) {
                        logToConsole(`❌ ${name}: 检查失败 - ${error.message}`, 'error');
                    }
                });
                
                const healthScore = Math.round((healthyCount / checks.length) * 100);
                logToConsole(`🏥 系统健康度: ${healthScore}%`);
                
                if (healthScore >= 80) {
                    logToConsole('✅ 系统状态良好');
                } else if (healthScore >= 60) {
                    logToConsole('⚠️ 系统状态一般', 'warn');
                } else {
                    logToConsole('❌ 系统状态异常', 'error');
                }
                
            } catch (error) {
                logToConsole(`❌ 健康检查失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后自动运行基本检查
        window.addEventListener('load', function() {
            setTimeout(function() {
                logToConsole('🔧 OTA系统启动流程测试页面已加载');
                logToConsole('点击按钮开始测试...');
            }, 500);
        });
    </script>
</body>
</html>
