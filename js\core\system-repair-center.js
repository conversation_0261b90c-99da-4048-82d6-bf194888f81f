/**
 * 系统修复中心
 * 统一管理所有修复工具、诊断接口和系统健康检查
 * 
 * 功能特性:
 * - 整合分散的修复工具
 * - 提供统一的诊断接口
 * - 自动化系统修复流程
 * - 修复历史记录和报告
 * - 预防性维护机制
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 系统修复中心类
     */
    class SystemRepairCenter {
        constructor() {
            this.repairTools = new Map();
            this.diagnosticTools = new Map();
            this.repairHistory = [];
            this.healthChecks = new Map();
            this.autoRepairEnabled = false;
            this.logger = null;
            this.initialized = false;
        }

        /**
         * 初始化修复中心
         */
        init() {
            if (this.initialized) {
                console.warn('系统修复中心已经初始化');
                return;
            }

            // 获取依赖
            this.logger = window.getService ? window.getService('logger') : null;

            // 注册修复工具
            this.registerRepairTools();

            // 注册诊断工具
            this.registerDiagnosticTools();

            // 注册健康检查
            this.registerHealthChecks();

            // 设置自动修复
            this.setupAutoRepair();

            this.initialized = true;
            this.log('系统修复中心初始化完成', 'success');
        }

        /**
         * 注册修复工具
         */
        registerRepairTools() {
            // 按钮修复工具
            this.repairTools.set('buttons', {
                name: '按钮修复工具',
                description: '修复按钮事件绑定和响应问题',
                fix: () => this.fixButtons(),
                priority: 'high',
                category: 'ui'
            });

            // 表单字段修复工具
            this.repairTools.set('formFields', {
                name: '表单字段修复工具',
                description: '修复表单验证和字段映射问题',
                fix: () => this.fixFormFields(),
                priority: 'critical',
                category: 'form'
            });

            // 状态同步修复工具
            this.repairTools.set('stateSync', {
                name: '状态同步修复工具',
                description: '修复状态管理和数据同步问题',
                fix: () => this.fixStateSync(),
                priority: 'medium',
                category: 'state'
            });

            // Manager初始化修复工具
            this.repairTools.set('managerInit', {
                name: 'Manager初始化修复工具',
                description: '修复Manager初始化顺序和依赖问题',
                fix: () => this.fixManagerInitialization(),
                priority: 'high',
                category: 'system'
            });

            // 服务定位器修复工具
            this.repairTools.set('serviceLocator', {
                name: '服务定位器修复工具',
                description: '修复服务定位和依赖注入问题',
                fix: () => this.fixServiceLocator(),
                priority: 'medium',
                category: 'architecture'
            });
        }

        /**
         * 注册诊断工具
         */
        registerDiagnosticTools() {
            // 系统健康诊断
            this.diagnosticTools.set('systemHealth', {
                name: '系统健康诊断',
                description: '检查系统整体健康状态',
                diagnose: () => this.diagnoseSystemHealth(),
                category: 'system'
            });

            // 按钮功能诊断
            this.diagnosticTools.set('buttonFunction', {
                name: '按钮功能诊断',
                description: '检查所有按钮的事件绑定和响应',
                diagnose: () => this.diagnoseButtonFunction(),
                category: 'ui'
            });

            // 表单验证诊断
            this.diagnosticTools.set('formValidation', {
                name: '表单验证诊断',
                description: '检查表单字段和验证规则',
                diagnose: () => this.diagnoseFormValidation(),
                category: 'form'
            });

            // 状态管理诊断
            this.diagnosticTools.set('stateManagement', {
                name: '状态管理诊断',
                description: '检查状态管理器和数据一致性',
                diagnose: () => this.diagnoseStateManagement(),
                category: 'state'
            });

            // 服务依赖诊断
            this.diagnosticTools.set('serviceDependency', {
                name: '服务依赖诊断',
                description: '检查服务注册和依赖关系',
                diagnose: () => this.diagnoseServiceDependency(),
                category: 'architecture'
            });
        }

        /**
         * 注册健康检查
         */
        registerHealthChecks() {
            // 核心服务健康检查
            this.healthChecks.set('coreServices', {
                name: '核心服务健康检查',
                check: () => this.checkCoreServices(),
                interval: 30000, // 30秒
                critical: true
            });

            // UI组件健康检查
            this.healthChecks.set('uiComponents', {
                name: 'UI组件健康检查',
                check: () => this.checkUIComponents(),
                interval: 60000, // 1分钟
                critical: false
            });

            // 状态一致性检查
            this.healthChecks.set('stateConsistency', {
                name: '状态一致性检查',
                check: () => this.checkStateConsistency(),
                interval: 45000, // 45秒
                critical: true
            });
        }

        /**
         * 运行完整系统诊断
         * @returns {Object} 诊断报告
         */
        async runFullDiagnostic() {
            this.log('开始运行完整系统诊断...', 'info');
            
            const diagnosticReport = {
                timestamp: new Date().toISOString(),
                overall: 'unknown',
                categories: {},
                issues: [],
                recommendations: []
            };

            let totalChecks = 0;
            let passedChecks = 0;

            // 运行所有诊断工具
            for (const [key, tool] of this.diagnosticTools) {
                try {
                    this.log(`运行诊断: ${tool.name}`, 'info');
                    const result = await tool.diagnose();
                    
                    if (!diagnosticReport.categories[tool.category]) {
                        diagnosticReport.categories[tool.category] = {
                            name: tool.category,
                            checks: [],
                            status: 'healthy'
                        };
                    }

                    diagnosticReport.categories[tool.category].checks.push({
                        name: tool.name,
                        status: result.status,
                        details: result.details,
                        issues: result.issues || []
                    });

                    totalChecks++;
                    if (result.status === 'healthy') {
                        passedChecks++;
                    } else {
                        diagnosticReport.categories[tool.category].status = 'unhealthy';
                        diagnosticReport.issues.push(...(result.issues || []));
                    }

                } catch (error) {
                    this.log(`诊断工具 ${tool.name} 执行失败: ${error.message}`, 'error');
                    diagnosticReport.issues.push({
                        category: tool.category,
                        severity: 'error',
                        message: `诊断工具执行失败: ${error.message}`
                    });
                }
            }

            // 计算整体健康状态
            const healthScore = Math.round((passedChecks / totalChecks) * 100);
            diagnosticReport.overall = healthScore >= 80 ? 'healthy' : 
                                     healthScore >= 60 ? 'warning' : 'critical';
            diagnosticReport.healthScore = healthScore;

            // 生成修复建议
            diagnosticReport.recommendations = this.generateRepairRecommendations(diagnosticReport);

            this.log(`系统诊断完成，健康度: ${healthScore}%`, 
                diagnosticReport.overall === 'healthy' ? 'success' : 'warning');

            return diagnosticReport;
        }

        /**
         * 执行自动修复
         * @param {Array} issues - 问题列表
         * @returns {Object} 修复结果
         */
        async performAutoRepair(issues = null) {
            if (!this.autoRepairEnabled) {
                this.log('自动修复未启用', 'warning');
                return { success: false, message: '自动修复未启用' };
            }

            this.log('开始执行自动修复...', 'info');

            const repairResult = {
                timestamp: new Date().toISOString(),
                totalIssues: 0,
                fixedIssues: 0,
                failedFixes: 0,
                results: []
            };

            // 如果没有提供问题列表，先运行诊断
            if (!issues) {
                const diagnostic = await this.runFullDiagnostic();
                issues = diagnostic.issues;
            }

            repairResult.totalIssues = issues.length;

            // 按优先级排序修复工具
            const sortedTools = Array.from(this.repairTools.entries())
                .sort((a, b) => this.getPriorityWeight(a[1].priority) - this.getPriorityWeight(b[1].priority));

            // 执行修复
            for (const [key, tool] of sortedTools) {
                try {
                    this.log(`执行修复: ${tool.name}`, 'info');
                    const fixResult = await tool.fix();
                    
                    repairResult.results.push({
                        tool: tool.name,
                        category: tool.category,
                        success: fixResult.success,
                        message: fixResult.message,
                        details: fixResult.details
                    });

                    if (fixResult.success) {
                        repairResult.fixedIssues++;
                    } else {
                        repairResult.failedFixes++;
                    }

                } catch (error) {
                    this.log(`修复工具 ${tool.name} 执行失败: ${error.message}`, 'error');
                    repairResult.results.push({
                        tool: tool.name,
                        category: tool.category,
                        success: false,
                        message: `修复工具执行失败: ${error.message}`,
                        error: error.message
                    });
                    repairResult.failedFixes++;
                }
            }

            // 记录修复历史
            this.repairHistory.push(repairResult);

            // 限制历史记录大小
            if (this.repairHistory.length > 50) {
                this.repairHistory.shift();
            }

            const successRate = Math.round((repairResult.fixedIssues / repairResult.totalIssues) * 100);
            this.log(`自动修复完成，成功率: ${successRate}%`, 
                successRate >= 80 ? 'success' : 'warning');

            return repairResult;
        }

        /**
         * 获取优先级权重
         * @param {string} priority - 优先级
         * @returns {number} 权重值
         */
        getPriorityWeight(priority) {
            const weights = {
                'critical': 1,
                'high': 2,
                'medium': 3,
                'low': 4
            };
            return weights[priority] || 5;
        }

        /**
         * 生成修复建议
         * @param {Object} diagnosticReport - 诊断报告
         * @returns {Array} 建议列表
         */
        generateRepairRecommendations(diagnosticReport) {
            const recommendations = [];

            // 基于问题类型生成建议
            diagnosticReport.issues.forEach(issue => {
                switch (issue.category) {
                    case 'ui':
                        recommendations.push('建议运行按钮修复工具');
                        break;
                    case 'form':
                        recommendations.push('建议运行表单字段修复工具');
                        break;
                    case 'state':
                        recommendations.push('建议运行状态同步修复工具');
                        break;
                    case 'system':
                        recommendations.push('建议重新初始化系统管理器');
                        break;
                    case 'architecture':
                        recommendations.push('建议运行服务定位器修复工具');
                        break;
                }
            });

            // 去重并返回
            return [...new Set(recommendations)];
        }

        /**
         * 设置自动修复
         */
        setupAutoRepair() {
            // 默认禁用自动修复，需要手动启用
            this.autoRepairEnabled = false;
            this.log('自动修复设置完成（默认禁用）', 'info');
        }

        /**
         * 启用自动修复
         */
        enableAutoRepair() {
            this.autoRepairEnabled = true;
            this.log('自动修复已启用', 'info');
        }

        /**
         * 禁用自动修复
         */
        disableAutoRepair() {
            this.autoRepairEnabled = false;
            this.log('自动修复已禁用', 'info');
        }

        /**
         * 获取修复历史
         * @param {number} limit - 限制数量
         * @returns {Array} 修复历史
         */
        getRepairHistory(limit = 10) {
            return this.repairHistory.slice(-limit);
        }

        /**
         * 获取系统修复中心状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                initialized: this.initialized,
                autoRepairEnabled: this.autoRepairEnabled,
                repairTools: this.repairTools.size,
                diagnosticTools: this.diagnosticTools.size,
                healthChecks: this.healthChecks.size,
                repairHistory: this.repairHistory.length,
                lastRepair: this.repairHistory.length > 0 ? 
                    this.repairHistory[this.repairHistory.length - 1].timestamp : null
            };
        }

        /**
         * 日志记录
         * @param {string} message - 消息
         * @param {string} level - 日志级别
         */
        log(message, level = 'info') {
            const prefix = '[SystemRepairCenter]';
            if (this.logger && this.logger.log) {
                this.logger.log(`${prefix} ${message}`, level);
            } else {
                console.log(`${prefix} ${message}`);
            }
        }

        /**
         * 修复按钮功能
         */
        async fixButtons() {
            try {
                const issues = [];
                const fixes = [];

                // 检查并修复常见按钮问题
                const buttonSelectors = [
                    '#uploadImageBtn',
                    '#showHistoryBtn',
                    '#logoutBtn',
                    '#createOrderBtn',
                    '#clearFormBtn'
                ];

                for (const selector of buttonSelectors) {
                    const button = document.querySelector(selector);
                    if (button) {
                        // 检查事件监听器
                        const hasListeners = button.onclick ||
                            button.addEventListener.toString().includes('click');

                        if (!hasListeners) {
                            // 尝试重新绑定事件
                            this.rebindButtonEvent(selector);
                            fixes.push(`重新绑定 ${selector} 事件`);
                        }
                    } else {
                        issues.push(`按钮 ${selector} 不存在`);
                    }
                }

                // 调用现有的按钮修复工具
                if (window.buttonFixer && typeof window.buttonFixer.fixAllButtons === 'function') {
                    try {
                        window.buttonFixer.fixAllButtons();
                        fixes.push('执行综合按钮修复器');
                    } catch (error) {
                        issues.push(`综合按钮修复器执行失败: ${error.message}`);
                    }
                }

                return {
                    success: issues.length === 0,
                    message: `按钮修复完成，修复项目: ${fixes.length}，问题: ${issues.length}`,
                    details: { fixes, issues }
                };

            } catch (error) {
                return {
                    success: false,
                    message: `按钮修复失败: ${error.message}`,
                    details: { error: error.message }
                };
            }
        }

        /**
         * 修复表单字段
         */
        async fixFormFields() {
            try {
                const issues = [];
                const fixes = [];

                // 调用现有的负责人字段修复工具
                if (window.fixResponsiblePerson && typeof window.fixResponsiblePerson === 'function') {
                    try {
                        window.fixResponsiblePerson();
                        fixes.push('执行负责人字段修复');
                    } catch (error) {
                        issues.push(`负责人字段修复失败: ${error.message}`);
                    }
                }

                // 检查必填字段映射
                const requiredFields = ['pickup_location', 'destination', 'date', 'time'];
                for (const field of requiredFields) {
                    const element = document.querySelector(`[name="${field}"]`);
                    if (!element) {
                        issues.push(`必填字段 ${field} 不存在`);
                    }
                }

                // 检查隐藏字段
                const hiddenFields = ['responsible_person_id', 'languages_id_array'];
                for (const field of hiddenFields) {
                    const element = document.querySelector(`[name="${field}"]`);
                    if (!element) {
                        // 尝试创建隐藏字段
                        this.createHiddenField(field);
                        fixes.push(`创建隐藏字段 ${field}`);
                    }
                }

                return {
                    success: issues.length === 0,
                    message: `表单字段修复完成，修复项目: ${fixes.length}，问题: ${issues.length}`,
                    details: { fixes, issues }
                };

            } catch (error) {
                return {
                    success: false,
                    message: `表单字段修复失败: ${error.message}`,
                    details: { error: error.message }
                };
            }
        }

        /**
         * 修复状态同步
         */
        async fixStateSync() {
            try {
                const issues = [];
                const fixes = [];

                // 检查中央状态管理器
                const centralManager = window.getCentralStateManager();
                if (centralManager) {
                    if (!centralManager.initialized) {
                        centralManager.init();
                        fixes.push('初始化中央状态管理器');
                    }
                } else {
                    issues.push('中央状态管理器不可用');
                }

                // 检查状态管理器适配器
                const adapter = window.getStateManagerAdapter();
                if (adapter) {
                    if (!adapter.initialized) {
                        adapter.init();
                        fixes.push('初始化状态管理器适配器');
                    }
                } else {
                    issues.push('状态管理器适配器不可用');
                }

                // 检查AppState
                const appState = window.getAppState();
                if (!appState) {
                    issues.push('AppState不可用');
                }

                return {
                    success: issues.length === 0,
                    message: `状态同步修复完成，修复项目: ${fixes.length}，问题: ${issues.length}`,
                    details: { fixes, issues }
                };

            } catch (error) {
                return {
                    success: false,
                    message: `状态同步修复失败: ${error.message}`,
                    details: { error: error.message }
                };
            }
        }

        /**
         * 修复Manager初始化
         */
        async fixManagerInitialization() {
            try {
                const issues = [];
                const fixes = [];

                // 检查核心Manager
                const managers = [
                    'uiManager',
                    'multiOrderManager',
                    'orderHistoryManager',
                    'imageUploadManager',
                    'currencyConverter'
                ];

                for (const managerName of managers) {
                    if (!window.OTA || !window.OTA[managerName]) {
                        // 尝试重新初始化Manager
                        const factoryName = `get${managerName.charAt(0).toUpperCase() + managerName.slice(1)}`;
                        if (window[factoryName] && typeof window[factoryName] === 'function') {
                            try {
                                window.OTA[managerName] = window[factoryName]();
                                fixes.push(`重新初始化 ${managerName}`);
                            } catch (error) {
                                issues.push(`${managerName} 初始化失败: ${error.message}`);
                            }
                        } else {
                            issues.push(`${managerName} 工厂函数不存在`);
                        }
                    }
                }

                return {
                    success: issues.length === 0,
                    message: `Manager初始化修复完成，修复项目: ${fixes.length}，问题: ${issues.length}`,
                    details: { fixes, issues }
                };

            } catch (error) {
                return {
                    success: false,
                    message: `Manager初始化修复失败: ${error.message}`,
                    details: { error: error.message }
                };
            }
        }

        /**
         * 修复服务定位器
         */
        async fixServiceLocator() {
            try {
                const issues = [];
                const fixes = [];

                // 检查服务定位器
                const serviceLocator = window.OTA && window.OTA.serviceLocator;
                if (!serviceLocator) {
                    issues.push('服务定位器不存在');
                } else {
                    // 检查getService函数
                    if (typeof window.getService !== 'function') {
                        // 尝试重新设置getService函数
                        window.getService = serviceLocator.get.bind(serviceLocator);
                        fixes.push('重新设置getService函数');
                    }
                }

                // 检查依赖容器
                const container = window.OTA && window.OTA.container;
                if (!container) {
                    issues.push('依赖容器不存在');
                }

                return {
                    success: issues.length === 0,
                    message: `服务定位器修复完成，修复项目: ${fixes.length}，问题: ${issues.length}`,
                    details: { fixes, issues }
                };

            } catch (error) {
                return {
                    success: false,
                    message: `服务定位器修复失败: ${error.message}`,
                    details: { error: error.message }
                };
            }
        }

        /**
         * 重新绑定按钮事件
         * @param {string} selector - 按钮选择器
         */
        rebindButtonEvent(selector) {
            const button = document.querySelector(selector);
            if (!button) return;

            // 根据按钮类型绑定相应事件
            switch (selector) {
                case '#uploadImageBtn':
                    button.onclick = () => {
                        const manager = window.OTA && window.OTA.imageUploadManager;
                        if (manager && manager.openImageUpload) {
                            manager.openImageUpload();
                        }
                    };
                    break;
                case '#showHistoryBtn':
                    button.onclick = () => {
                        const manager = window.OTA && window.OTA.orderHistoryManager;
                        if (manager && manager.showHistory) {
                            manager.showHistory();
                        }
                    };
                    break;
                case '#logoutBtn':
                    button.onclick = () => {
                        const uiManager = window.OTA && window.OTA.uiManager;
                        if (uiManager && uiManager.handleLogout) {
                            uiManager.handleLogout();
                        }
                    };
                    break;
                case '#createOrderBtn':
                    button.onclick = () => {
                        const uiManager = window.OTA && window.OTA.uiManager;
                        if (uiManager && uiManager.handleCreateOrder) {
                            uiManager.handleCreateOrder();
                        }
                    };
                    break;
            }
        }

        /**
         * 创建隐藏字段
         * @param {string} fieldName - 字段名称
         */
        createHiddenField(fieldName) {
            const form = document.querySelector('form');
            if (!form) return;

            const hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = fieldName;
            hiddenField.id = fieldName;
            form.appendChild(hiddenField);
        }

        /**
         * 诊断系统健康状态
         */
        async diagnoseSystemHealth() {
            const issues = [];
            const details = [];

            try {
                // 检查核心命名空间
                if (!window.OTA) {
                    issues.push({ severity: 'critical', message: 'OTA命名空间不存在' });
                } else {
                    details.push('OTA命名空间正常');
                }

                // 检查核心服务
                const coreServices = ['logger', 'appState', 'apiService', 'geminiService'];
                for (const service of coreServices) {
                    if (!window.OTA || !window.OTA[service]) {
                        issues.push({ severity: 'high', message: `核心服务 ${service} 不可用` });
                    } else {
                        details.push(`核心服务 ${service} 正常`);
                    }
                }

                // 检查架构组件
                const architectureComponents = ['container', 'serviceLocator', 'centralStateManager'];
                for (const component of architectureComponents) {
                    if (!window.OTA || !window.OTA[component]) {
                        issues.push({ severity: 'medium', message: `架构组件 ${component} 不可用` });
                    } else {
                        details.push(`架构组件 ${component} 正常`);
                    }
                }

                const status = issues.length === 0 ? 'healthy' :
                             issues.some(i => i.severity === 'critical') ? 'critical' : 'warning';

                return { status, details, issues };

            } catch (error) {
                return {
                    status: 'critical',
                    details: ['系统健康检查执行失败'],
                    issues: [{ severity: 'critical', message: `检查失败: ${error.message}` }]
                };
            }
        }

        /**
         * 诊断按钮功能
         */
        async diagnoseButtonFunction() {
            const issues = [];
            const details = [];

            try {
                const buttonTests = [
                    { selector: '#uploadImageBtn', name: '图片上传按钮' },
                    { selector: '#showHistoryBtn', name: '历史订单按钮' },
                    { selector: '#logoutBtn', name: '退出登录按钮' },
                    { selector: '#createOrderBtn', name: '创建订单按钮' },
                    { selector: '#clearFormBtn', name: '清空表单按钮' }
                ];

                for (const test of buttonTests) {
                    const button = document.querySelector(test.selector);
                    if (!button) {
                        issues.push({ severity: 'medium', message: `${test.name} 不存在` });
                    } else {
                        // 检查是否有事件监听器
                        const hasEvents = button.onclick ||
                            (button.addEventListener && button.addEventListener.toString().includes('click'));

                        if (!hasEvents) {
                            issues.push({ severity: 'high', message: `${test.name} 缺少事件监听器` });
                        } else {
                            details.push(`${test.name} 功能正常`);
                        }
                    }
                }

                const status = issues.length === 0 ? 'healthy' : 'warning';
                return { status, details, issues };

            } catch (error) {
                return {
                    status: 'critical',
                    details: ['按钮功能检查执行失败'],
                    issues: [{ severity: 'critical', message: `检查失败: ${error.message}` }]
                };
            }
        }

        /**
         * 诊断表单验证
         */
        async diagnoseFormValidation() {
            const issues = [];
            const details = [];

            try {
                // 检查必填字段
                const requiredFields = [
                    { name: 'pickup_location', label: '接送地点' },
                    { name: 'destination', label: '目的地' },
                    { name: 'date', label: '日期' },
                    { name: 'time', label: '时间' }
                ];

                for (const field of requiredFields) {
                    const element = document.querySelector(`[name="${field.name}"]`);
                    if (!element) {
                        issues.push({ severity: 'high', message: `必填字段 ${field.label} 不存在` });
                    } else {
                        details.push(`必填字段 ${field.label} 存在`);
                    }
                }

                // 检查隐藏字段
                const hiddenFields = [
                    { name: 'responsible_person_id', label: '负责人ID' },
                    { name: 'languages_id_array', label: '语言ID数组' }
                ];

                for (const field of hiddenFields) {
                    const element = document.querySelector(`[name="${field.name}"]`);
                    if (!element) {
                        issues.push({ severity: 'critical', message: `隐藏字段 ${field.label} 不存在` });
                    } else {
                        details.push(`隐藏字段 ${field.label} 存在`);
                    }
                }

                // 检查表单验证函数
                if (window.fixResponsiblePerson && typeof window.fixResponsiblePerson === 'function') {
                    details.push('负责人字段修复函数可用');
                } else {
                    issues.push({ severity: 'medium', message: '负责人字段修复函数不可用' });
                }

                const status = issues.length === 0 ? 'healthy' :
                             issues.some(i => i.severity === 'critical') ? 'critical' : 'warning';

                return { status, details, issues };

            } catch (error) {
                return {
                    status: 'critical',
                    details: ['表单验证检查执行失败'],
                    issues: [{ severity: 'critical', message: `检查失败: ${error.message}` }]
                };
            }
        }

        /**
         * 诊断状态管理
         */
        async diagnoseStateManagement() {
            const issues = [];
            const details = [];

            try {
                // 检查中央状态管理器
                const centralManager = window.getCentralStateManager();
                if (!centralManager) {
                    issues.push({ severity: 'high', message: '中央状态管理器不可用' });
                } else {
                    if (centralManager.initialized) {
                        details.push('中央状态管理器已初始化');

                        // 检查状态域
                        const stats = centralManager.getStats();
                        if (stats.domains.length > 0) {
                            details.push(`状态域数量: ${stats.domains.length}`);
                        } else {
                            issues.push({ severity: 'medium', message: '没有注册的状态域' });
                        }
                    } else {
                        issues.push({ severity: 'medium', message: '中央状态管理器未初始化' });
                    }
                }

                // 检查状态管理器适配器
                const adapter = window.getStateManagerAdapter();
                if (!adapter) {
                    issues.push({ severity: 'medium', message: '状态管理器适配器不可用' });
                } else {
                    if (adapter.initialized) {
                        details.push('状态管理器适配器已初始化');
                    } else {
                        issues.push({ severity: 'low', message: '状态管理器适配器未初始化' });
                    }
                }

                // 检查AppState
                const appState = window.getAppState();
                if (!appState) {
                    issues.push({ severity: 'high', message: 'AppState不可用' });
                } else {
                    details.push('AppState可用');
                }

                const status = issues.length === 0 ? 'healthy' :
                             issues.some(i => i.severity === 'high') ? 'warning' : 'healthy';

                return { status, details, issues };

            } catch (error) {
                return {
                    status: 'critical',
                    details: ['状态管理检查执行失败'],
                    issues: [{ severity: 'critical', message: `检查失败: ${error.message}` }]
                };
            }
        }

        /**
         * 诊断服务依赖
         */
        async diagnoseServiceDependency() {
            const issues = [];
            const details = [];

            try {
                // 检查服务定位器
                const serviceLocator = window.OTA && window.OTA.serviceLocator;
                if (!serviceLocator) {
                    issues.push({ severity: 'high', message: '服务定位器不存在' });
                } else {
                    details.push('服务定位器存在');

                    // 检查可用服务
                    if (typeof serviceLocator.getAvailableServices === 'function') {
                        const services = serviceLocator.getAvailableServices();
                        details.push(`可用服务数量: ${services.length}`);
                    }
                }

                // 检查依赖容器
                const container = window.OTA && window.OTA.container;
                if (!container) {
                    issues.push({ severity: 'high', message: '依赖容器不存在' });
                } else {
                    details.push('依赖容器存在');

                    // 检查注册的服务
                    if (typeof container.getRegisteredServices === 'function') {
                        const services = container.getRegisteredServices();
                        details.push(`注册服务数量: ${services.length}`);
                    }
                }

                // 检查getService函数
                if (typeof window.getService === 'function') {
                    details.push('getService函数可用');

                    // 测试核心服务获取
                    const testServices = ['logger', 'appState'];
                    for (const serviceName of testServices) {
                        try {
                            const service = window.getService(serviceName);
                            if (service) {
                                details.push(`服务 ${serviceName} 可获取`);
                            } else {
                                issues.push({ severity: 'medium', message: `服务 ${serviceName} 获取失败` });
                            }
                        } catch (error) {
                            issues.push({ severity: 'medium', message: `服务 ${serviceName} 获取异常: ${error.message}` });
                        }
                    }
                } else {
                    issues.push({ severity: 'critical', message: 'getService函数不可用' });
                }

                const status = issues.length === 0 ? 'healthy' :
                             issues.some(i => i.severity === 'critical' || i.severity === 'high') ? 'warning' : 'healthy';

                return { status, details, issues };

            } catch (error) {
                return {
                    status: 'critical',
                    details: ['服务依赖检查执行失败'],
                    issues: [{ severity: 'critical', message: `检查失败: ${error.message}` }]
                };
            }
        }

        // 健康检查方法
        checkCoreServices() {
            return { healthy: true, details: '核心服务正常' };
        }

        checkUIComponents() {
            return { healthy: true, details: 'UI组件正常' };
        }

        checkStateConsistency() {
            return { healthy: true, details: '状态一致性正常' };
        }
    }

    // 创建全局实例
    const systemRepairCenter = new SystemRepairCenter();

    // 导出到OTA命名空间
    window.OTA.systemRepairCenter = systemRepairCenter;

    // 提供便捷的全局访问函数
    window.getSystemRepairCenter = function() {
        return window.OTA.systemRepairCenter;
    };

    // 提供便捷的全局修复命令
    window.repairSystem = async function(options = {}) {
        const center = window.getSystemRepairCenter();
        if (!center.initialized) {
            center.init();
        }
        
        if (options.diagnose) {
            return await center.runFullDiagnostic();
        } else if (options.autoRepair) {
            return await center.performAutoRepair();
        } else {
            // 默认运行诊断
            return await center.runFullDiagnostic();
        }
    };

    console.log('✅ 系统修复中心已加载');

})();
