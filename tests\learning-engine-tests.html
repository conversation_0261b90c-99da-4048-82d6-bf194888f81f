<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能学习型格式预处理引擎 - 测试套件</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .test-header h1 {
            color: #4a5568;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .test-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: rgba(247, 250, 252, 0.8);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .summary-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #718096;
            font-weight: 500;
        }

        .passed { color: #48bb78; }
        .failed { color: #f56565; }
        .skipped { color: #ed8936; }
        .total { color: #4299e1; }

        .test-results {
            margin-top: 20px;
        }

        .test-suite {
            margin-bottom: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
        }

        .suite-header {
            background: #f7fafc;
            padding: 15px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .suite-title {
            font-weight: 600;
            color: #2d3748;
        }

        .suite-status {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .badge-passed {
            background: rgba(72, 187, 120, 0.1);
            color: #2f855a;
        }

        .badge-failed {
            background: rgba(245, 101, 101, 0.1);
            color: #c53030;
        }

        .badge-running {
            background: rgba(66, 153, 225, 0.1);
            color: #2b6cb0;
        }

        .test-cases {
            display: none;
        }

        .test-cases.expanded {
            display: block;
        }

        .test-case {
            padding: 12px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .test-case:last-child {
            border-bottom: none;
        }

        .test-name {
            font-weight: 500;
        }

        .test-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        .icon-passed {
            background: #48bb78;
        }

        .icon-failed {
            background: #f56565;
        }

        .icon-running {
            background: #4299e1;
            animation: pulse 1.5s ease-in-out infinite;
        }

        .icon-pending {
            background: #a0aec0;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .test-duration {
            color: #718096;
            font-size: 0.85rem;
        }

        .test-error {
            background: rgba(245, 101, 101, 0.05);
            border-left: 4px solid #f56565;
            padding: 10px 15px;
            margin: 10px 20px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            color: #c53030;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            transition: width 0.3s ease;
            width: 0%;
        }

        .log-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .log-info { color: #63b3ed; }
        .log-success { color: #68d391; }
        .log-warning { color: #fbb6ce; }
        .log-error { color: #fc8181; }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>智能学习型格式预处理引擎</h1>
            <p>测试套件 - 自动化测试和质量保证</p>
        </div>

        <div class="test-controls">
            <button class="btn btn-primary" onclick="runAllTests()">运行所有测试</button>
            <button class="btn btn-success" onclick="runUnitTests()">单元测试</button>
            <button class="btn btn-success" onclick="runIntegrationTests()">集成测试</button>
            <button class="btn btn-secondary" onclick="clearResults()">清空结果</button>
            <button class="btn btn-danger" onclick="stopTests()">停止测试</button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="test-progress"></div>
        </div>

        <div class="test-summary">
            <div class="summary-card">
                <div class="summary-value total" id="total-tests">0</div>
                <div class="summary-label">总测试数</div>
            </div>
            <div class="summary-card">
                <div class="summary-value passed" id="passed-tests">0</div>
                <div class="summary-label">通过</div>
            </div>
            <div class="summary-card">
                <div class="summary-value failed" id="failed-tests">0</div>
                <div class="summary-label">失败</div>
            </div>
            <div class="summary-card">
                <div class="summary-value skipped" id="skipped-tests">0</div>
                <div class="summary-label">跳过</div>
            </div>
        </div>

        <div class="test-results" id="test-results">
            <!-- 测试结果将在这里动态生成 -->
        </div>

        <div class="log-container" id="test-logs">
            <div class="log-entry log-info">[INFO] 测试系统已准备就绪</div>
        </div>
    </div>

    <!-- 引入被测试的模块 -->
    <script src="../js/logger.js"></script>
    <script src="../js/learning-engine/learning-config.js"></script>
    <script src="../js/learning-engine/learning-storage-manager.js"></script>
    <script src="../js/learning-engine/user-operation-learner.js"></script>
    <script src="../js/learning-engine/error-classification-system.js"></script>
    <script src="../js/learning-engine/pattern-matching-engine.js"></script>
    <script src="../js/learning-engine/manual-correction-interface.js"></script>
    <script src="../js/learning-engine/rule-generation-engine.js"></script>
    <script src="../js/learning-engine/learning-integration-manager.js"></script>
    <script src="../js/learning-engine/ui-correction-manager.js"></script>
    <script src="../js/learning-engine/data-persistence-manager.js"></script>
    <script src="../js/learning-engine/predictive-corrector.js"></script>
    <script src="../js/learning-engine/adaptive-prompt-optimizer.js"></script>
    <script src="../js/learning-engine/learning-effectiveness-evaluator.js"></script>
    <script src="../js/learning-engine/intelligent-cache-manager.js"></script>
    <script src="../js/learning-engine/performance-monitor.js"></script>
    <script src="../js/learning-engine/performance-optimizer.js"></script>

    <script>
        /**
         * 简单的测试框架
         */
        class TestFramework {
            constructor() {
                this.testSuites = [];
                this.currentSuite = null;
                this.stats = {
                    total: 0,
                    passed: 0,
                    failed: 0,
                    skipped: 0
                };
                this.isRunning = false;
                this.shouldStop = false;
            }

            /**
             * 创建测试套件
             */
            describe(name, callback) {
                const suite = {
                    name: name,
                    tests: [],
                    beforeEach: null,
                    afterEach: null,
                    status: 'pending'
                };
                
                this.testSuites.push(suite);
                this.currentSuite = suite;
                
                callback();
                
                this.currentSuite = null;
                return suite;
            }

            /**
             * 创建测试用例
             */
            it(name, callback) {
                if (!this.currentSuite) {
                    throw new Error('测试用例必须在describe块内定义');
                }

                const test = {
                    name: name,
                    callback: callback,
                    status: 'pending',
                    duration: 0,
                    error: null
                };

                this.currentSuite.tests.push(test);
                return test;
            }

            /**
             * 设置前置操作
             */
            beforeEach(callback) {
                if (this.currentSuite) {
                    this.currentSuite.beforeEach = callback;
                }
            }

            /**
             * 设置后置操作
             */
            afterEach(callback) {
                if (this.currentSuite) {
                    this.currentSuite.afterEach = callback;
                }
            }

            /**
             * 断言函数
             */
            expect(actual) {
                return {
                    toBe: (expected) => {
                        if (actual !== expected) {
                            throw new Error(`期望 ${expected}，但得到 ${actual}`);
                        }
                    },
                    toEqual: (expected) => {
                        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                            throw new Error(`期望 ${JSON.stringify(expected)}，但得到 ${JSON.stringify(actual)}`);
                        }
                    },
                    toBeTruthy: () => {
                        if (!actual) {
                            throw new Error(`期望真值，但得到 ${actual}`);
                        }
                    },
                    toBeFalsy: () => {
                        if (actual) {
                            throw new Error(`期望假值，但得到 ${actual}`);
                        }
                    },
                    toBeGreaterThan: (expected) => {
                        if (actual <= expected) {
                            throw new Error(`期望大于 ${expected}，但得到 ${actual}`);
                        }
                    },
                    toContain: (expected) => {
                        if (!actual.includes(expected)) {
                            throw new Error(`期望包含 ${expected}，但在 ${actual} 中未找到`);
                        }
                    },
                    toThrow: () => {
                        let threw = false;
                        try {
                            actual();
                        } catch (e) {
                            threw = true;
                        }
                        if (!threw) {
                            throw new Error('期望抛出异常，但没有抛出');
                        }
                    }
                };
            }

            /**
             * 运行所有测试
             */
            async runAllTests() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.shouldStop = false;
                this.resetStats();
                
                this.log('开始运行所有测试...', 'info');
                
                for (const suite of this.testSuites) {
                    if (this.shouldStop) break;
                    await this.runTestSuite(suite);
                }
                
                this.isRunning = false;
                this.updateProgress(100);
                this.log(`测试完成！通过: ${this.stats.passed}, 失败: ${this.stats.failed}, 跳过: ${this.stats.skipped}`, 
                    this.stats.failed > 0 ? 'error' : 'success');
            }

            /**
             * 运行测试套件
             */
            async runTestSuite(suite) {
                suite.status = 'running';
                this.updateSuiteDisplay(suite);
                
                this.log(`运行测试套件: ${suite.name}`, 'info');
                
                let suitePassed = 0;
                let suiteFailed = 0;
                
                for (const test of suite.tests) {
                    if (this.shouldStop) break;
                    
                    const result = await this.runTest(test, suite);
                    if (result === 'passed') suitePassed++;
                    else if (result === 'failed') suiteFailed++;
                    
                    this.updateTestDisplay(test, suite);
                    this.updateStats();
                    this.updateProgress();
                }
                
                suite.status = suiteFailed > 0 ? 'failed' : 'passed';
                this.updateSuiteDisplay(suite);
            }

            /**
             * 运行单个测试
             */
            async runTest(test, suite) {
                test.status = 'running';
                const startTime = performance.now();
                
                try {
                    // 运行前置操作
                    if (suite.beforeEach) {
                        await suite.beforeEach();
                    }
                    
                    // 运行测试
                    await test.callback();
                    
                    // 运行后置操作
                    if (suite.afterEach) {
                        await suite.afterEach();
                    }
                    
                    test.status = 'passed';
                    test.duration = performance.now() - startTime;
                    this.log(`✓ ${test.name} (${test.duration.toFixed(2)}ms)`, 'success');
                    return 'passed';
                    
                } catch (error) {
                    test.status = 'failed';
                    test.duration = performance.now() - startTime;
                    test.error = error.message;
                    this.log(`✗ ${test.name}: ${error.message}`, 'error');
                    return 'failed';
                }
            }

            /**
             * 停止测试
             */
            stopTests() {
                this.shouldStop = true;
                this.log('测试已停止', 'warning');
            }

            /**
             * 重置统计
             */
            resetStats() {
                this.stats = { total: 0, passed: 0, failed: 0, skipped: 0 };
                this.updateStatsDisplay();
            }

            /**
             * 更新统计
             */
            updateStats() {
                this.stats.total = this.testSuites.reduce((sum, suite) => sum + suite.tests.length, 0);
                this.stats.passed = this.testSuites.reduce((sum, suite) => 
                    sum + suite.tests.filter(t => t.status === 'passed').length, 0);
                this.stats.failed = this.testSuites.reduce((sum, suite) => 
                    sum + suite.tests.filter(t => t.status === 'failed').length, 0);
                this.stats.skipped = this.testSuites.reduce((sum, suite) => 
                    sum + suite.tests.filter(t => t.status === 'skipped').length, 0);
                
                this.updateStatsDisplay();
            }

            /**
             * 更新统计显示
             */
            updateStatsDisplay() {
                document.getElementById('total-tests').textContent = this.stats.total;
                document.getElementById('passed-tests').textContent = this.stats.passed;
                document.getElementById('failed-tests').textContent = this.stats.failed;
                document.getElementById('skipped-tests').textContent = this.stats.skipped;
            }

            /**
             * 更新进度
             */
            updateProgress(percentage = null) {
                if (percentage === null) {
                    const completed = this.stats.passed + this.stats.failed + this.stats.skipped;
                    percentage = this.stats.total > 0 ? (completed / this.stats.total) * 100 : 0;
                }
                
                const progressBar = document.getElementById('test-progress');
                if (progressBar) {
                    progressBar.style.width = percentage + '%';
                }
            }

            /**
             * 更新套件显示
             */
            updateSuiteDisplay(suite) {
                const resultsContainer = document.getElementById('test-results');
                let suiteElement = document.getElementById(`suite-${suite.name}`);
                
                if (!suiteElement) {
                    suiteElement = this.createSuiteElement(suite);
                    resultsContainer.appendChild(suiteElement);
                }
                
                // 更新套件状态
                const statusBadge = suiteElement.querySelector('.status-badge');
                statusBadge.className = `status-badge badge-${suite.status}`;
                statusBadge.textContent = this.getStatusText(suite.status);
            }

            /**
             * 创建套件元素
             */
            createSuiteElement(suite) {
                const suiteDiv = document.createElement('div');
                suiteDiv.className = 'test-suite';
                suiteDiv.id = `suite-${suite.name}`;
                
                suiteDiv.innerHTML = `
                    <div class="suite-header" onclick="toggleSuite('${suite.name}')">
                        <div class="suite-title">${suite.name}</div>
                        <div class="suite-status">
                            <span class="status-badge badge-${suite.status}">${this.getStatusText(suite.status)}</span>
                            <span>▼</span>
                        </div>
                    </div>
                    <div class="test-cases" id="cases-${suite.name}">
                        ${suite.tests.map(test => this.createTestCaseHTML(test)).join('')}
                    </div>
                `;
                
                return suiteDiv;
            }

            /**
             * 创建测试用例HTML
             */
            createTestCaseHTML(test) {
                return `
                    <div class="test-case" id="test-${test.name}">
                        <div class="test-name">${test.name}</div>
                        <div class="test-status">
                            <div class="status-icon icon-${test.status}"></div>
                            <span class="test-duration">${test.duration ? test.duration.toFixed(2) + 'ms' : ''}</span>
                        </div>
                    </div>
                    ${test.error ? `<div class="test-error">${test.error}</div>` : ''}
                `;
            }

            /**
             * 更新测试显示
             */
            updateTestDisplay(test, suite) {
                const casesContainer = document.getElementById(`cases-${suite.name}`);
                if (casesContainer) {
                    casesContainer.innerHTML = suite.tests.map(t => this.createTestCaseHTML(t)).join('');
                }
            }

            /**
             * 获取状态文本
             */
            getStatusText(status) {
                const statusMap = {
                    pending: '等待中',
                    running: '运行中',
                    passed: '通过',
                    failed: '失败',
                    skipped: '跳过'
                };
                return statusMap[status] || status;
            }

            /**
             * 记录日志
             */
            log(message, level = 'info') {
                const logContainer = document.getElementById('test-logs');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${level}`;
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            /**
             * 清空结果
             */
            clearResults() {
                document.getElementById('test-results').innerHTML = '';
                document.getElementById('test-logs').innerHTML = 
                    '<div class="log-entry log-info">[INFO] 测试系统已准备就绪</div>';
                this.resetStats();
                this.updateProgress(0);
            }
        }

        // 创建全局测试框架实例
        const testFramework = new TestFramework();
        
        // 导出全局函数
        window.describe = testFramework.describe.bind(testFramework);
        window.it = testFramework.it.bind(testFramework);
        window.beforeEach = testFramework.beforeEach.bind(testFramework);
        window.afterEach = testFramework.afterEach.bind(testFramework);
        window.expect = testFramework.expect.bind(testFramework);

        // 控制函数
        window.runAllTests = () => testFramework.runAllTests();
        window.stopTests = () => testFramework.stopTests();
        window.clearResults = () => testFramework.clearResults();

        window.toggleSuite = (suiteName) => {
            const casesContainer = document.getElementById(`cases-${suiteName}`);
            if (casesContainer) {
                casesContainer.classList.toggle('expanded');
            }
        };

        // 加载测试用例
        window.addEventListener('DOMContentLoaded', () => {
            // 这里将加载具体的测试用例
            loadTestSuites();
        });

        /**
         * 加载测试套件
         */
        function loadTestSuites() {
            // 加载外部测试用例文件
            const script = document.createElement('script');
            script.src = 'test-suites.js';
            script.onload = () => {
                if (window.loadTestSuites) {
                    window.loadTestSuites();
                }
            };
            document.head.appendChild(script);
        }

        // 分类测试函数
        window.runUnitTests = () => {
            testFramework.log('运行单元测试...', 'info');
            // 这里可以过滤只运行单元测试
            testFramework.runAllTests();
        };

        window.runIntegrationTests = () => {
            testFramework.log('运行集成测试...', 'info');
            // 这里可以过滤只运行集成测试
            testFramework.runAllTests();
        };
    </script>
</body>
</html>
