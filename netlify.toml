[build]
  publish = "."
  command = "echo 'Static site deployment - OTA Order Processing System'"

[build.environment]
  NODE_VERSION = "18"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://generativelanguage.googleapis.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' https://gomyhire.com.my https://generativelanguage.googleapis.com;"

# Cache static assets
[[headers]]
  for = "/js/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/style.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Enable gzip compression
[[headers]]
  for = "*.js"
  [headers.values]
    Content-Encoding = "gzip"

[[headers]]
  for = "*.css"
  [headers.values]
    Content-Encoding = "gzip"

# Redirect rules (if needed)
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# SPA fallback for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# Error pages
[[redirects]]
  from = "/404"
  to = "/index.html"
  status = 404
