<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统 - 综合功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            color: #764ba2;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        }
        
        .btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .stats-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #764ba2;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #764ba2;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
            font-weight: 500;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin-top: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #51cf66, #40c057);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .test-console {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 0.9em;
            line-height: 1.4;
            margin-bottom: 20px;
            border: 2px solid #333;
        }
        
        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .suite-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #764ba2;
        }
        
        .suite-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .suite-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #764ba2;
        }
        
        .suite-stats {
            font-size: 0.9em;
            color: #666;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-icon {
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .test-name {
            flex: 1;
            font-weight: 500;
        }
        
        .test-message {
            font-size: 0.9em;
            color: #666;
            margin-left: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background: #ffc107; animation: pulse 1s infinite; }
        .status-passed { background: #28a745; }
        .status-failed { background: #dc3545; }
        .status-pending { background: #6c757d; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #764ba2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .test-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .stats-dashboard {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .test-results {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 OTA系统综合功能测试</h1>
        
        <div class="test-controls">
            <button class="btn success" onclick="runFullTest()">
                🚀 运行完整测试
            </button>
            <button class="btn" onclick="runQuickTest()">
                ⚡ 快速测试
            </button>
            <button class="btn" onclick="runSpecificSuite()">
                🎯 指定套件测试
            </button>
            <button class="btn danger" onclick="clearResults()">
                🧹 清空结果
            </button>
        </div>
        
        <div class="stats-dashboard">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="totalProgress"></div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="passedProgress"></div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="failedProgress" style="background: linear-gradient(90deg, #ff6b6b, #ee5a52);"></div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="successProgress"></div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testDuration">0ms</div>
                <div class="stat-label">测试耗时</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="systemHealth">未知</div>
                <div class="stat-label">系统健康度</div>
            </div>
        </div>
        
        <div class="test-console" id="console"></div>
        
        <div class="test-results" id="testResults"></div>
    </div>

    <!-- 加载核心架构模块 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/central-state-manager.js"></script>
    <script src="js/core/state-manager-adapter.js"></script>
    <script src="js/core/system-repair-center.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    
    <!-- 加载基础服务 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    
    <!-- 加载测试套件 -->
    <script src="comprehensive-system-test.js"></script>
    
    <script>
        // 测试控制器
        class TestController {
            constructor() {
                this.currentTest = null;
                this.testResults = null;
                this.isRunning = false;
            }
            
            logToConsole(message, type = 'info') {
                const console = document.getElementById('console');
                const timestamp = new Date().toLocaleTimeString();
                const icons = {
                    'info': 'ℹ️',
                    'success': '✅',
                    'warning': '⚠️',
                    'error': '❌',
                    'start': '🚀'
                };
                const icon = icons[type] || 'ℹ️';
                const line = `[${timestamp}] ${icon} ${message}\n`;
                console.textContent += line;
                console.scrollTop = console.scrollHeight;
            }
            
            updateStats(results) {
                if (!results) return;
                
                document.getElementById('totalTests').textContent = results.total;
                document.getElementById('passedTests').textContent = results.passed;
                document.getElementById('failedTests').textContent = results.failed;
                
                const successRate = results.total > 0 ? 
                    Math.round((results.passed / results.total) * 100) : 0;
                document.getElementById('successRate').textContent = successRate + '%';
                
                // 更新进度条
                const totalProgress = 100;
                const passedProgress = results.total > 0 ? (results.passed / results.total) * 100 : 0;
                const failedProgress = results.total > 0 ? (results.failed / results.total) * 100 : 0;
                
                document.getElementById('totalProgress').style.width = totalProgress + '%';
                document.getElementById('passedProgress').style.width = passedProgress + '%';
                document.getElementById('failedProgress').style.width = failedProgress + '%';
                document.getElementById('successProgress').style.width = successRate + '%';
                
                // 更新系统健康度
                if (successRate >= 95) {
                    document.getElementById('systemHealth').textContent = '优秀';
                } else if (successRate >= 80) {
                    document.getElementById('systemHealth').textContent = '良好';
                } else if (successRate >= 60) {
                    document.getElementById('systemHealth').textContent = '一般';
                } else {
                    document.getElementById('systemHealth').textContent = '异常';
                }
            }
            
            displayTestResults(results) {
                const resultsDiv = document.getElementById('testResults');
                resultsDiv.innerHTML = '';
                
                if (!results || !results.suites) return;
                
                results.suites.forEach(suite => {
                    const suiteCard = document.createElement('div');
                    suiteCard.className = 'suite-card';
                    
                    const passed = suite.tests.filter(t => t.passed).length;
                    const total = suite.tests.length;
                    const rate = total > 0 ? Math.round((passed / total) * 100) : 0;
                    
                    suiteCard.innerHTML = `
                        <div class="suite-header">
                            <div class="suite-name">${suite.name}</div>
                            <div class="suite-stats">${passed}/${total} (${rate}%) - ${suite.duration}ms</div>
                        </div>
                        <div class="suite-tests">
                            ${suite.tests.map(test => `
                                <div class="test-item">
                                    <span class="test-icon">${test.passed ? '✅' : '❌'}</span>
                                    <span class="test-name">${test.name}</span>
                                    <span class="test-message">${test.message}</span>
                                </div>
                            `).join('')}
                        </div>
                    `;
                    
                    resultsDiv.appendChild(suiteCard);
                });
            }
            
            async runTest(testFunction, testName) {
                if (this.isRunning) {
                    this.logToConsole('测试正在运行中，请等待完成', 'warning');
                    return;
                }
                
                this.isRunning = true;
                this.logToConsole(`开始运行${testName}...`, 'start');
                
                const startTime = performance.now();
                
                try {
                    this.testResults = await testFunction();
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);
                    
                    document.getElementById('testDuration').textContent = duration + 'ms';
                    
                    this.updateStats(this.testResults);
                    this.displayTestResults(this.testResults);
                    
                    const successRate = this.testResults.total > 0 ? 
                        Math.round((this.testResults.passed / this.testResults.total) * 100) : 0;
                    
                    this.logToConsole(`${testName}完成！成功率: ${successRate}%`, 
                        successRate >= 80 ? 'success' : 'warning');
                    
                } catch (error) {
                    this.logToConsole(`${testName}执行失败: ${error.message}`, 'error');
                } finally {
                    this.isRunning = false;
                }
            }
        }
        
        // 创建测试控制器实例
        const testController = new TestController();
        
        // 全局测试函数
        async function runFullTest() {
            await testController.runTest(window.runComprehensiveTest, '综合功能测试');
        }
        
        async function runQuickTest() {
            // 快速测试只运行核心功能
            const quickTest = async () => {
                const test = new window.ComprehensiveSystemTest();
                await test.initializeTestEnvironment();
                await test.runCoreArchitectureTests();
                await test.runDependencyInjectionTests();
                return test.testResults;
            };
            
            await testController.runTest(quickTest, '快速测试');
        }
        
        async function runSpecificSuite() {
            const suites = [
                '核心架构组件测试',
                '依赖注入系统测试', 
                '状态管理系统测试',
                '修复系统测试',
                'Manager集成测试',
                '系统启动流程测试',
                '端到端集成测试'
            ];
            
            const choice = prompt(`请选择要运行的测试套件:\n${suites.map((s, i) => `${i + 1}. ${s}`).join('\n')}`);
            const index = parseInt(choice) - 1;
            
            if (index >= 0 && index < suites.length) {
                const suiteName = suites[index];
                testController.logToConsole(`运行指定测试套件: ${suiteName}`, 'info');
                // 这里可以实现具体的单个套件测试逻辑
            } else {
                testController.logToConsole('无效的选择', 'warning');
            }
        }
        
        function clearResults() {
            document.getElementById('console').textContent = '';
            document.getElementById('testResults').innerHTML = '';
            
            // 重置统计
            ['totalTests', 'passedTests', 'failedTests'].forEach(id => {
                document.getElementById(id).textContent = '0';
            });
            document.getElementById('successRate').textContent = '0%';
            document.getElementById('testDuration').textContent = '0ms';
            document.getElementById('systemHealth').textContent = '未知';
            
            // 重置进度条
            ['totalProgress', 'passedProgress', 'failedProgress', 'successProgress'].forEach(id => {
                document.getElementById(id).style.width = '0%';
            });
            
            testController.logToConsole('测试结果已清空', 'info');
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            setTimeout(() => {
                testController.logToConsole('🧪 OTA系统综合功能测试页面已加载', 'success');
                testController.logToConsole('点击"运行完整测试"开始测试系统功能...', 'info');
                
                // 显示系统基本信息
                testController.logToConsole(`浏览器: ${navigator.userAgent}`, 'info');
                testController.logToConsole(`页面URL: ${location.href}`, 'info');
            }, 500);
        });
    </script>
</body>
</html>
