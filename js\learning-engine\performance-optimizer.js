/**
 * 智能学习型格式预处理引擎 - 性能优化器
 * 负责系统性能优化、算法调优、资源管理
 * 与缓存系统和性能监控器协同工作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return window.OTA.learningConfig || window.learningConfig;
    }

    function getIntelligentCacheManager() {
        return window.OTA.intelligentCacheManager || window.intelligentCacheManager;
    }

    function getPerformanceMonitor() {
        return window.OTA.performanceMonitor || window.performanceMonitor;
    }

    function getUserOperationLearner() {
        return window.OTA.userOperationLearner || window.userOperationLearner;
    }

    function getRuleGenerationEngine() {
        return window.OTA.ruleGenerationEngine || window.ruleGenerationEngine;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    /**
     * 性能优化器类
     * 提供系统性能优化和算法调优功能
     */
    class PerformanceOptimizer {
        constructor() {
            this.config = getLearningConfig();
            this.cacheManager = getIntelligentCacheManager();
            this.performanceMonitor = getPerformanceMonitor();
            this.operationLearner = getUserOperationLearner();
            this.ruleEngine = getRuleGenerationEngine();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            
            // 优化配置
            this.optimizationConfig = {
                enabled: true,
                autoOptimization: true,
                optimizationInterval: 30 * 60 * 1000,    // 30分钟
                performanceThresholds: {
                    responseTime: 2000,                   // 2秒
                    memoryUsage: 80 * 1024 * 1024,       // 80MB
                    cacheHitRate: 0.8,                   // 80%
                    cpuUsage: 0.7                        // 70%
                },
                optimizationStrategies: {
                    caching: true,
                    algorithmTuning: true,
                    memoryManagement: true,
                    ruleOptimization: true
                }
            };
            
            // 优化历史
            this.optimizationHistory = [];
            
            // 当前优化状态
            this.currentOptimizations = new Map();
            
            // 性能基线
            this.performanceBaseline = null;
            
            // 优化策略
            this.strategies = this.initializeOptimizationStrategies();

            this.initialize();
        }

        /**
         * 初始化性能优化器
         */
        initialize() {
            try {
                // 建立性能基线
                this.establishPerformanceBaseline();
                
                // 设置自动优化
                if (this.optimizationConfig.autoOptimization) {
                    this.setupAutoOptimization();
                }
                
                // 注册性能监控回调
                this.registerPerformanceCallbacks();
                
                this.logger?.log('性能优化器初始化完成', 'info', {
                    version: this.version,
                    autoOptimization: this.optimizationConfig.autoOptimization,
                    strategies: Object.keys(this.strategies).length
                });

            } catch (error) {
                this.logger?.logError('性能优化器初始化失败', error);
            }
        }

        /**
         * 初始化优化策略
         */
        initializeOptimizationStrategies() {
            return {
                // 缓存优化策略
                cacheOptimization: {
                    name: '缓存优化',
                    priority: 1,
                    execute: this.optimizeCache.bind(this),
                    conditions: ['low_cache_hit_rate', 'high_response_time']
                },
                
                // 算法调优策略
                algorithmTuning: {
                    name: '算法调优',
                    priority: 2,
                    execute: this.tuneAlgorithms.bind(this),
                    conditions: ['high_cpu_usage', 'slow_processing']
                },
                
                // 内存管理策略
                memoryManagement: {
                    name: '内存管理',
                    priority: 3,
                    execute: this.optimizeMemoryUsage.bind(this),
                    conditions: ['high_memory_usage', 'memory_leak']
                },
                
                // 规则优化策略
                ruleOptimization: {
                    name: '规则优化',
                    priority: 4,
                    execute: this.optimizeRules.bind(this),
                    conditions: ['rule_conflicts', 'low_rule_effectiveness']
                },
                
                // 数据结构优化
                dataStructureOptimization: {
                    name: '数据结构优化',
                    priority: 5,
                    execute: this.optimizeDataStructures.bind(this),
                    conditions: ['large_data_sets', 'slow_queries']
                }
            };
        }

        /**
         * 执行性能优化
         * @param {Object} options - 优化选项
         * @returns {Object} 优化结果
         */
        async performOptimization(options = {}) {
            try {
                const optimizationId = this.generateOptimizationId();
                const startTime = performance.now();
                
                this.logger?.log('开始性能优化', 'info', { optimizationId, options });
                
                // 收集当前性能数据
                const currentPerformance = this.collectPerformanceData();
                
                // 分析性能问题
                const performanceIssues = this.analyzePerformanceIssues(currentPerformance);
                
                // 选择优化策略
                const selectedStrategies = this.selectOptimizationStrategies(performanceIssues, options);
                
                // 执行优化策略
                const optimizationResults = [];
                for (const strategy of selectedStrategies) {
                    try {
                        const result = await strategy.execute(performanceIssues, options);
                        optimizationResults.push({
                            strategy: strategy.name,
                            success: true,
                            result: result
                        });
                    } catch (error) {
                        optimizationResults.push({
                            strategy: strategy.name,
                            success: false,
                            error: error.message
                        });
                        this.logger?.logError(`优化策略执行失败: ${strategy.name}`, error);
                    }
                }
                
                // 验证优化效果
                const postOptimizationPerformance = this.collectPerformanceData();
                const improvement = this.calculateImprovement(currentPerformance, postOptimizationPerformance);
                
                // 创建优化记录
                const optimizationRecord = {
                    id: optimizationId,
                    timestamp: new Date().toISOString(),
                    duration: performance.now() - startTime,
                    performanceIssues: performanceIssues,
                    strategiesExecuted: optimizationResults,
                    improvement: improvement,
                    beforePerformance: currentPerformance,
                    afterPerformance: postOptimizationPerformance,
                    options: options
                };
                
                // 保存优化记录
                this.saveOptimizationRecord(optimizationRecord);
                
                this.logger?.log('性能优化完成', 'success', {
                    optimizationId,
                    duration: optimizationRecord.duration,
                    improvement: improvement
                });
                
                return optimizationRecord;

            } catch (error) {
                this.logger?.logError('性能优化失败', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        /**
         * 缓存优化
         * @param {Array} issues - 性能问题
         * @param {Object} options - 选项
         * @returns {Object} 优化结果
         */
        async optimizeCache(issues, options) {
            const results = {
                actions: [],
                improvements: {}
            };
            
            try {
                // 获取缓存统计
                const cacheStats = this.cacheManager.getStats();
                
                // 优化缓存大小
                if (cacheStats.hitRate < this.optimizationConfig.performanceThresholds.cacheHitRate) {
                    // 增加缓存大小
                    const currentSize = this.cacheManager.cacheConfig.maxSize;
                    const newSize = Math.min(currentSize * 1.5, 2000);
                    this.cacheManager.cacheConfig.maxSize = newSize;
                    
                    results.actions.push({
                        action: 'increase_cache_size',
                        from: currentSize,
                        to: newSize
                    });
                }
                
                // 优化缓存策略
                if (issues.includes('high_response_time')) {
                    // 启用更积极的预加载
                    this.cacheManager.cacheConfig.preloadThreshold = 0.5;
                    
                    results.actions.push({
                        action: 'enable_aggressive_preload',
                        threshold: 0.5
                    });
                }
                
                // 清理无效缓存
                const keysBeforeCleanup = this.cacheManager.getKeys().length;
                this.cacheManager.performCleanup();
                const keysAfterCleanup = this.cacheManager.getKeys().length;
                
                if (keysBeforeCleanup > keysAfterCleanup) {
                    results.actions.push({
                        action: 'cleanup_invalid_cache',
                        removedKeys: keysBeforeCleanup - keysAfterCleanup
                    });
                }
                
                return results;

            } catch (error) {
                this.logger?.logError('缓存优化失败', error);
                throw error;
            }
        }

        /**
         * 算法调优
         * @param {Array} issues - 性能问题
         * @param {Object} options - 选项
         * @returns {Object} 优化结果
         */
        async tuneAlgorithms(issues, options) {
            const results = {
                actions: [],
                improvements: {}
            };
            
            try {
                // 优化模式匹配算法
                const patternMatcher = window.OTA?.patternMatchingEngine;
                if (patternMatcher && issues.includes('slow_processing')) {
                    // 调整相似度阈值
                    const currentThreshold = patternMatcher.config?.similarityThreshold || 0.8;
                    const newThreshold = Math.min(currentThreshold + 0.1, 0.95);
                    
                    if (patternMatcher.config) {
                        patternMatcher.config.similarityThreshold = newThreshold;
                    }
                    
                    results.actions.push({
                        action: 'adjust_similarity_threshold',
                        from: currentThreshold,
                        to: newThreshold
                    });
                }
                
                // 优化规则匹配算法
                if (this.ruleEngine && issues.includes('high_cpu_usage')) {
                    // 启用规则缓存
                    this.ruleEngine.enableRuleCache = true;
                    
                    results.actions.push({
                        action: 'enable_rule_cache'
                    });
                }
                
                return results;

            } catch (error) {
                this.logger?.logError('算法调优失败', error);
                throw error;
            }
        }

        /**
         * 内存管理优化
         * @param {Array} issues - 性能问题
         * @param {Object} options - 选项
         * @returns {Object} 优化结果
         */
        async optimizeMemoryUsage(issues, options) {
            const results = {
                actions: [],
                improvements: {}
            };
            
            try {
                // 清理用户操作历史
                if (this.operationLearner && issues.includes('high_memory_usage')) {
                    const beforeCount = this.operationLearner.getOperationCount();
                    this.operationLearner.cleanupOldOperations();
                    const afterCount = this.operationLearner.getOperationCount();
                    
                    results.actions.push({
                        action: 'cleanup_operation_history',
                        removedOperations: beforeCount - afterCount
                    });
                }
                
                // 优化缓存内存使用
                if (this.cacheManager && issues.includes('memory_leak')) {
                    // 强制内存缓存清理
                    this.cacheManager.evictLeastUsed('memory', 
                        Math.ceil(this.cacheManager.caches.memory.size * 0.3));
                    
                    results.actions.push({
                        action: 'force_memory_cache_cleanup',
                        percentage: 30
                    });
                }
                
                // 垃圾回收建议
                if (window.gc && issues.includes('high_memory_usage')) {
                    window.gc();
                    results.actions.push({
                        action: 'trigger_garbage_collection'
                    });
                }
                
                return results;

            } catch (error) {
                this.logger?.logError('内存管理优化失败', error);
                throw error;
            }
        }

        /**
         * 规则优化
         * @param {Array} issues - 性能问题
         * @param {Object} options - 选项
         * @returns {Object} 优化结果
         */
        async optimizeRules(issues, options) {
            const results = {
                actions: [],
                improvements: {}
            };
            
            try {
                if (!this.ruleEngine) {
                    return results;
                }
                
                // 清理低效规则
                if (issues.includes('low_rule_effectiveness')) {
                    const beforeCount = this.ruleEngine.getAllRules().length;
                    this.ruleEngine.cleanupIneffectiveRules();
                    const afterCount = this.ruleEngine.getAllRules().length;
                    
                    results.actions.push({
                        action: 'cleanup_ineffective_rules',
                        removedRules: beforeCount - afterCount
                    });
                }
                
                // 解决规则冲突
                if (issues.includes('rule_conflicts')) {
                    const conflicts = this.ruleEngine.detectRuleConflicts();
                    const resolvedConflicts = this.ruleEngine.resolveRuleConflicts(conflicts);
                    
                    results.actions.push({
                        action: 'resolve_rule_conflicts',
                        conflictsResolved: resolvedConflicts.length
                    });
                }
                
                // 优化规则优先级
                this.ruleEngine.optimizeRulePriorities();
                results.actions.push({
                    action: 'optimize_rule_priorities'
                });
                
                return results;

            } catch (error) {
                this.logger?.logError('规则优化失败', error);
                throw error;
            }
        }

        /**
         * 数据结构优化
         * @param {Array} issues - 性能问题
         * @param {Object} options - 选项
         * @returns {Object} 优化结果
         */
        async optimizeDataStructures(issues, options) {
            const results = {
                actions: [],
                improvements: {}
            };
            
            try {
                // 优化存储结构
                if (issues.includes('large_data_sets')) {
                    // 压缩存储数据
                    const storageManager = window.OTA?.learningStorageManager;
                    if (storageManager) {
                        const compressionResult = storageManager.compressStoredData();
                        results.actions.push({
                            action: 'compress_stored_data',
                            compressionRatio: compressionResult.ratio
                        });
                    }
                }
                
                // 优化查询索引
                if (issues.includes('slow_queries')) {
                    // 为常用查询创建索引
                    if (this.operationLearner) {
                        this.operationLearner.createQueryIndexes();
                        results.actions.push({
                            action: 'create_query_indexes'
                        });
                    }
                }
                
                return results;

            } catch (error) {
                this.logger?.logError('数据结构优化失败', error);
                throw error;
            }
        }

        /**
         * 收集性能数据
         * @returns {Object} 性能数据
         */
        collectPerformanceData() {
            const data = {
                timestamp: Date.now(),
                memory: performance.memory ? {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                } : null,
                cache: this.cacheManager ? this.cacheManager.getStats() : null,
                monitor: this.performanceMonitor ? this.performanceMonitor.getRealTimeMetrics() : null
            };
            
            return data;
        }

        /**
         * 分析性能问题
         * @param {Object} performanceData - 性能数据
         * @returns {Array} 问题列表
         */
        analyzePerformanceIssues(performanceData) {
            const issues = [];
            
            // 检查响应时间
            if (performanceData.monitor?.averageResponseTime > this.optimizationConfig.performanceThresholds.responseTime) {
                issues.push('high_response_time');
            }
            
            // 检查内存使用
            if (performanceData.memory?.used > this.optimizationConfig.performanceThresholds.memoryUsage) {
                issues.push('high_memory_usage');
            }
            
            // 检查缓存命中率
            if (performanceData.cache?.hitRate < this.optimizationConfig.performanceThresholds.cacheHitRate) {
                issues.push('low_cache_hit_rate');
            }
            
            // 检查错误率
            const errorRate = performanceData.monitor?.totalErrors / Math.max(performanceData.monitor?.totalOperations, 1);
            if (errorRate > 0.1) {
                issues.push('high_error_rate');
            }
            
            // 检查并发操作数
            if (performanceData.monitor?.currentOperations > 50) {
                issues.push('high_concurrent_operations');
            }
            
            return issues;
        }

        /**
         * 选择优化策略
         * @param {Array} issues - 性能问题
         * @param {Object} options - 选项
         * @returns {Array} 选择的策略
         */
        selectOptimizationStrategies(issues, options) {
            const selectedStrategies = [];
            
            // 根据问题和策略条件选择
            Object.values(this.strategies).forEach(strategy => {
                const shouldExecute = strategy.conditions.some(condition => issues.includes(condition));
                
                if (shouldExecute && this.optimizationConfig.optimizationStrategies[strategy.name.toLowerCase().replace(/\s+/g, '')]) {
                    selectedStrategies.push(strategy);
                }
            });
            
            // 按优先级排序
            selectedStrategies.sort((a, b) => a.priority - b.priority);
            
            // 限制同时执行的策略数量
            return selectedStrategies.slice(0, options.maxStrategies || 3);
        }

        /**
         * 计算改进效果
         * @param {Object} before - 优化前性能
         * @param {Object} after - 优化后性能
         * @returns {Object} 改进效果
         */
        calculateImprovement(before, after) {
            const improvement = {};
            
            // 响应时间改进
            if (before.monitor?.averageResponseTime && after.monitor?.averageResponseTime) {
                const responseTimeImprovement = 
                    (before.monitor.averageResponseTime - after.monitor.averageResponseTime) / 
                    before.monitor.averageResponseTime;
                improvement.responseTime = responseTimeImprovement;
            }
            
            // 内存使用改进
            if (before.memory?.used && after.memory?.used) {
                const memoryImprovement = (before.memory.used - after.memory.used) / before.memory.used;
                improvement.memoryUsage = memoryImprovement;
            }
            
            // 缓存命中率改进
            if (before.cache?.hitRate !== undefined && after.cache?.hitRate !== undefined) {
                improvement.cacheHitRate = after.cache.hitRate - before.cache.hitRate;
            }
            
            // 计算总体改进分数
            const improvements = Object.values(improvement).filter(v => !isNaN(v));
            improvement.overall = improvements.length > 0 ? 
                improvements.reduce((sum, v) => sum + v, 0) / improvements.length : 0;
            
            return improvement;
        }

        /**
         * 建立性能基线
         */
        establishPerformanceBaseline() {
            try {
                this.performanceBaseline = this.collectPerformanceData();
                this.logger?.log('性能基线已建立', 'info', this.performanceBaseline);
            } catch (error) {
                this.logger?.logError('建立性能基线失败', error);
            }
        }

        /**
         * 设置自动优化
         */
        setupAutoOptimization() {
            setInterval(() => {
                this.performOptimization({ automatic: true });
            }, this.optimizationConfig.optimizationInterval);
            
            this.logger?.log('自动优化已设置', 'info', {
                interval: this.optimizationConfig.optimizationInterval / (60 * 1000) + '分钟'
            });
        }

        /**
         * 注册性能监控回调
         */
        registerPerformanceCallbacks() {
            if (this.performanceMonitor) {
                // 注册报警处理器
                this.performanceMonitor.registerAlertHandler((alert) => {
                    this.handlePerformanceAlert(alert);
                });
            }
        }

        /**
         * 处理性能报警
         * @param {Object} alert - 报警信息
         */
        handlePerformanceAlert(alert) {
            try {
                // 根据报警类型触发相应的优化
                const urgentOptimization = this.shouldTriggerUrgentOptimization(alert);
                
                if (urgentOptimization) {
                    this.logger?.log('触发紧急优化', 'warn', { alert });
                    this.performOptimization({ 
                        urgent: true, 
                        alertType: alert.type,
                        maxStrategies: 1 
                    });
                }

            } catch (error) {
                this.logger?.logError('处理性能报警失败', error);
            }
        }

        /**
         * 判断是否应该触发紧急优化
         * @param {Object} alert - 报警信息
         * @returns {boolean} 是否触发
         */
        shouldTriggerUrgentOptimization(alert) {
            const urgentAlertTypes = [
                'high_memory_usage',
                'memory_leak_detected',
                'high_error_rate',
                'performance_degradation'
            ];
            
            return urgentAlertTypes.includes(alert.type) && alert.severity === 'critical';
        }

        /**
         * 生成优化ID
         * @returns {string} 优化ID
         */
        generateOptimizationId() {
            return `opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 保存优化记录
         * @param {Object} record - 优化记录
         */
        saveOptimizationRecord(record) {
            try {
                this.optimizationHistory.push(record);
                
                // 限制历史记录数量
                if (this.optimizationHistory.length > 100) {
                    this.optimizationHistory = this.optimizationHistory.slice(-100);
                }
                
                // 保存到存储
                const storageKey = `${this.config.get('storage.keyPrefix')}optimizations`;
                localStorage.setItem(storageKey, JSON.stringify(this.optimizationHistory));
                
            } catch (error) {
                this.logger?.logError('保存优化记录失败', error);
            }
        }

        /**
         * 获取优化历史
         * @param {number} limit - 限制数量
         * @returns {Array} 优化历史
         */
        getOptimizationHistory(limit = 10) {
            return this.optimizationHistory.slice(-limit);
        }

        /**
         * 获取优化统计
         * @returns {Object} 统计信息
         */
        getOptimizationStats() {
            const history = this.optimizationHistory;
            
            if (history.length === 0) {
                return {
                    totalOptimizations: 0,
                    averageImprovement: 0,
                    lastOptimization: null
                };
            }
            
            const totalImprovement = history.reduce((sum, opt) => 
                sum + (opt.improvement?.overall || 0), 0);
            
            return {
                totalOptimizations: history.length,
                averageImprovement: totalImprovement / history.length,
                lastOptimization: history[history.length - 1]?.timestamp,
                successRate: history.filter(opt => opt.improvement?.overall > 0).length / history.length
            };
        }

        /**
         * 启用/禁用自动优化
         * @param {boolean} enabled - 是否启用
         */
        setAutoOptimization(enabled) {
            this.optimizationConfig.autoOptimization = enabled;
            this.logger?.log(`自动优化${enabled ? '已启用' : '已禁用'}`, 'info');
        }

        /**
         * 设置优化阈值
         * @param {Object} thresholds - 阈值配置
         */
        setPerformanceThresholds(thresholds) {
            Object.assign(this.optimizationConfig.performanceThresholds, thresholds);
            this.logger?.log('性能阈值已更新', 'info', thresholds);
        }

        /**
         * 重置优化器
         */
        reset() {
            this.optimizationHistory = [];
            this.currentOptimizations.clear();
            this.performanceBaseline = null;
            
            this.logger?.log('性能优化器已重置', 'info');
        }
    }

    // 创建全局实例
    const performanceOptimizer = new PerformanceOptimizer();

    // 导出到全局命名空间
    window.OTA.performanceOptimizer = performanceOptimizer;
    window.performanceOptimizer = performanceOptimizer; // 向后兼容

    // 工厂函数
    window.getPerformanceOptimizer = function() {
        return window.OTA.performanceOptimizer || window.performanceOptimizer;
    };

    console.log('性能优化器加载完成', {
        version: performanceOptimizer.version,
        autoOptimization: performanceOptimizer.optimizationConfig.autoOptimization,
        strategies: Object.keys(performanceOptimizer.strategies).length
    });

})();
