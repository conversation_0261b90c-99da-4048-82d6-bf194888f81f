/**
 * 智能学习型格式预处理引擎 - 性能测试套件
 * 负责系统性能测试、压力测试、内存使用测试等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

class PerformanceTestSuite {
    constructor() {
        this.version = '1.0.0';
        this.isRunning = false;
        this.shouldStop = false;
        this.testResults = {};
        this.startTime = null;
        
        // 获取系统模块
        this.cacheManager = window.OTA?.intelligentCacheManager || window.intelligentCacheManager;
        this.performanceMonitor = window.OTA?.performanceMonitor || window.performanceMonitor;
        this.operationLearner = window.OTA?.userOperationLearner || window.userOperationLearner;
        this.ruleEngine = window.OTA?.ruleGenerationEngine || window.ruleGenerationEngine;
        this.predictor = window.OTA?.predictiveCorrector || window.predictiveCorrector;
        this.patternMatcher = window.OTA?.patternMatchingEngine || window.patternMatchingEngine;
        
        this.initialize();
    }

    /**
     * 初始化测试套件
     */
    initialize() {
        console.log('性能测试套件初始化完成', { version: this.version });
    }

    /**
     * 运行所有性能测试
     */
    async runAllPerformanceTests() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.shouldStop = false;
        this.startTime = Date.now();
        this.testResults = {};
        
        this.log('开始运行所有性能测试...', 'info');
        this.disableButtons();
        
        try {
            // 运行各项测试
            await this.runMemoryTest();
            if (this.shouldStop) return;
            
            await this.runSpeedTest();
            if (this.shouldStop) return;
            
            await this.runCacheTest();
            if (this.shouldStop) return;
            
            await this.runLearningAlgorithmTest();
            if (this.shouldStop) return;
            
            // 生成总结报告
            this.generateSummaryReport();
            
        } catch (error) {
            this.log(`测试执行失败: ${error.message}`, 'error');
        } finally {
            this.isRunning = false;
            this.enableButtons();
        }
    }

    /**
     * 内存使用测试
     */
    async runMemoryTest() {
        this.log('开始内存使用测试...', 'info', 'memory');
        this.setStatus('memory', 'running');
        
        const initialMemory = this.getMemoryUsage();
        let peakMemory = initialMemory;
        const memoryReadings = [];
        
        try {
            // 创建大量数据进行内存测试
            const testData = [];
            const iterations = 1000;
            
            for (let i = 0; i < iterations; i++) {
                if (this.shouldStop) break;
                
                // 模拟用户操作记录
                const operation = {
                    type: 'correction',
                    field: `testField${i}`,
                    originalValue: `testValue${i}`.repeat(10),
                    correctedValue: `TestValue${i}`.repeat(10),
                    context: { test: true, iteration: i }
                };
                
                testData.push(operation);
                
                // 记录到学习系统
                if (this.operationLearner) {
                    this.operationLearner.recordOperation(operation);
                }
                
                // 测试缓存
                if (this.cacheManager) {
                    this.cacheManager.set(`test-key-${i}`, operation);
                }
                
                // 记录内存使用
                const currentMemory = this.getMemoryUsage();
                memoryReadings.push(currentMemory);
                peakMemory = Math.max(peakMemory, currentMemory);
                
                // 更新进度
                this.updateProgress('memory', (i / iterations) * 100);
                
                // 更新显示
                this.updateElement('memory-current', this.formatMemory(currentMemory));
                this.updateElement('memory-peak', this.formatMemory(peakMemory));
                
                if (i % 100 === 0) {
                    this.log(`内存测试进度: ${i}/${iterations}`, 'info', 'memory');
                    await this.sleep(10); // 短暂暂停
                }
            }
            
            // 计算内存增长率
            const finalMemory = this.getMemoryUsage();
            const growthRate = ((finalMemory - initialMemory) / initialMemory * 100).toFixed(2);
            
            // 计算效率评分
            const efficiency = this.calculateMemoryEfficiency(initialMemory, finalMemory, iterations);
            
            // 更新结果
            this.updateElement('memory-growth', growthRate + '%');
            this.updateElement('memory-efficiency', efficiency + '/100');
            
            // 保存测试结果
            this.testResults.memory = {
                initialMemory,
                finalMemory,
                peakMemory,
                growthRate: parseFloat(growthRate),
                efficiency,
                iterations
            };
            
            this.setStatus('memory', efficiency > 70 ? 'success' : efficiency > 50 ? 'warning' : 'error');
            this.log(`内存测试完成 - 效率评分: ${efficiency}/100`, 'success', 'memory');
            
        } catch (error) {
            this.setStatus('memory', 'error');
            this.log(`内存测试失败: ${error.message}`, 'error', 'memory');
        }
    }

    /**
     * 响应速度测试
     */
    async runSpeedTest() {
        this.log('开始响应速度测试...', 'info', 'speed');
        this.setStatus('speed', 'running');
        
        const responseTimes = [];
        const iterations = 500;
        
        try {
            for (let i = 0; i < iterations; i++) {
                if (this.shouldStop) break;
                
                const startTime = performance.now();
                
                // 测试模式匹配性能
                if (this.patternMatcher) {
                    this.patternMatcher.calculateSimilarity(
                        `test string ${i}`,
                        `test string ${i + 1}`
                    );
                }
                
                // 测试预测性能
                if (this.predictor) {
                    this.predictor.predictCorrection('testField', `testValue${i}`, {});
                }
                
                // 测试缓存性能
                if (this.cacheManager) {
                    this.cacheManager.get(`test-key-${i % 100}`);
                }
                
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                responseTimes.push(responseTime);
                
                // 更新进度
                this.updateProgress('speed', (i / iterations) * 100);
                
                if (i % 50 === 0) {
                    const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
                    this.updateElement('speed-average', avgTime.toFixed(2) + 'ms');
                    this.log(`速度测试进度: ${i}/${iterations}`, 'info', 'speed');
                }
            }
            
            // 计算统计数据
            const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            const minTime = Math.min(...responseTimes);
            const maxTime = Math.max(...responseTimes);
            const throughput = (iterations / (responseTimes.reduce((a, b) => a + b, 0) / 1000)).toFixed(0);
            
            // 更新显示
            this.updateElement('speed-average', avgTime.toFixed(2) + 'ms');
            this.updateElement('speed-min', minTime.toFixed(2) + 'ms');
            this.updateElement('speed-max', maxTime.toFixed(2) + 'ms');
            this.updateElement('speed-throughput', throughput + ' ops/s');
            
            // 保存测试结果
            this.testResults.speed = {
                averageTime: avgTime,
                minTime,
                maxTime,
                throughput: parseFloat(throughput),
                iterations
            };
            
            const status = avgTime < 10 ? 'success' : avgTime < 50 ? 'warning' : 'error';
            this.setStatus('speed', status);
            this.log(`速度测试完成 - 平均响应时间: ${avgTime.toFixed(2)}ms`, 'success', 'speed');
            
        } catch (error) {
            this.setStatus('speed', 'error');
            this.log(`速度测试失败: ${error.message}`, 'error', 'speed');
        }
    }

    /**
     * 缓存性能测试
     */
    async runCacheTest() {
        this.log('开始缓存性能测试...', 'info', 'cache');
        this.setStatus('cache', 'running');
        
        if (!this.cacheManager) {
            this.log('缓存管理器不可用', 'error', 'cache');
            this.setStatus('cache', 'error');
            return;
        }
        
        try {
            // 清空缓存开始测试
            this.cacheManager.clear();
            
            const iterations = 1000;
            let hits = 0;
            let misses = 0;
            const responseTimes = [];
            
            // 预填充一些数据
            for (let i = 0; i < 100; i++) {
                this.cacheManager.set(`cache-test-${i}`, { data: `test data ${i}` });
            }
            
            // 测试缓存性能
            for (let i = 0; i < iterations; i++) {
                if (this.shouldStop) break;
                
                const startTime = performance.now();
                const key = `cache-test-${i % 150}`; // 部分命中，部分未命中
                const result = this.cacheManager.get(key);
                const endTime = performance.now();
                
                responseTimes.push(endTime - startTime);
                
                if (result !== null) {
                    hits++;
                } else {
                    misses++;
                    // 缓存未命中时添加数据
                    this.cacheManager.set(key, { data: `test data ${i}` });
                }
                
                // 更新进度
                this.updateProgress('cache', (i / iterations) * 100);
                
                if (i % 100 === 0) {
                    const hitRate = (hits / (hits + misses) * 100).toFixed(1);
                    this.updateElement('cache-hit-rate', hitRate + '%');
                    this.log(`缓存测试进度: ${i}/${iterations}`, 'info', 'cache');
                }
            }
            
            // 计算结果
            const hitRate = hits / (hits + misses) * 100;
            const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            const cacheStats = this.cacheManager.getStats();
            const efficiency = this.calculateCacheEfficiency(hitRate, avgResponseTime);
            
            // 更新显示
            this.updateElement('cache-hit-rate', hitRate.toFixed(1) + '%');
            this.updateElement('cache-response-time', avgResponseTime.toFixed(2) + 'ms');
            this.updateElement('cache-size', cacheStats.cacheSize?.memory || 0);
            this.updateElement('cache-efficiency', efficiency + '/100');
            
            // 保存测试结果
            this.testResults.cache = {
                hitRate,
                avgResponseTime,
                efficiency,
                hits,
                misses,
                iterations
            };
            
            const status = hitRate > 70 ? 'success' : hitRate > 50 ? 'warning' : 'error';
            this.setStatus('cache', status);
            this.log(`缓存测试完成 - 命中率: ${hitRate.toFixed(1)}%`, 'success', 'cache');
            
        } catch (error) {
            this.setStatus('cache', 'error');
            this.log(`缓存测试失败: ${error.message}`, 'error', 'cache');
        }
    }

    /**
     * 学习算法性能测试
     */
    async runLearningAlgorithmTest() {
        this.log('开始学习算法性能测试...', 'info', 'learning');
        this.setStatus('learning', 'running');
        
        try {
            const iterations = 200;
            let correctPredictions = 0;
            let totalPredictions = 0;
            let rulesGenerated = 0;
            const learningTimes = [];
            
            // 生成测试数据
            const testCases = this.generateLearningTestCases(iterations);
            
            for (let i = 0; i < testCases.length; i++) {
                if (this.shouldStop) break;
                
                const testCase = testCases[i];
                const startTime = performance.now();
                
                // 记录操作
                if (this.operationLearner) {
                    this.operationLearner.recordOperation(testCase.operation);
                }
                
                // 生成规则
                if (this.ruleEngine) {
                    const rule = this.ruleEngine.generateRule(testCase.operation);
                    if (rule) rulesGenerated++;
                }
                
                // 测试预测
                if (this.predictor && testCase.testValue) {
                    const prediction = this.predictor.predictCorrection(
                        testCase.operation.field,
                        testCase.testValue,
                        testCase.operation.context
                    );
                    
                    totalPredictions++;
                    if (prediction.hasPrediction && 
                        prediction.topPrediction?.correctedValue === testCase.expectedValue) {
                        correctPredictions++;
                    }
                }
                
                const endTime = performance.now();
                learningTimes.push(endTime - startTime);
                
                // 更新进度
                this.updateProgress('learning', (i / iterations) * 100);
                
                if (i % 20 === 0) {
                    const accuracy = totalPredictions > 0 ? 
                        (correctPredictions / totalPredictions * 100).toFixed(1) : '0.0';
                    this.updateElement('learning-accuracy', accuracy + '%');
                    this.updateElement('learning-rules', rulesGenerated);
                    this.log(`学习算法测试进度: ${i}/${iterations}`, 'info', 'learning');
                }
            }
            
            // 计算结果
            const accuracy = totalPredictions > 0 ? 
                correctPredictions / totalPredictions * 100 : 0;
            const avgLearningTime = learningTimes.reduce((a, b) => a + b, 0) / learningTimes.length;
            const predictionAccuracy = accuracy;
            
            // 更新显示
            this.updateElement('learning-accuracy', accuracy.toFixed(1) + '%');
            this.updateElement('learning-speed', avgLearningTime.toFixed(2) + 'ms');
            this.updateElement('learning-rules', rulesGenerated);
            this.updateElement('learning-prediction', predictionAccuracy.toFixed(1) + '%');
            
            // 保存测试结果
            this.testResults.learning = {
                accuracy,
                avgLearningTime,
                rulesGenerated,
                predictionAccuracy,
                correctPredictions,
                totalPredictions
            };
            
            const status = accuracy > 70 ? 'success' : accuracy > 50 ? 'warning' : 'error';
            this.setStatus('learning', status);
            this.log(`学习算法测试完成 - 准确率: ${accuracy.toFixed(1)}%`, 'success', 'learning');
            
        } catch (error) {
            this.setStatus('learning', 'error');
            this.log(`学习算法测试失败: ${error.message}`, 'error', 'learning');
        }
    }

    /**
     * 压力测试
     */
    async runStressTest() {
        this.log('开始压力测试...', 'warning');
        
        // 模拟高并发操作
        const promises = [];
        const concurrency = 50;
        const operationsPerWorker = 100;
        
        for (let i = 0; i < concurrency; i++) {
            promises.push(this.stressTestWorker(i, operationsPerWorker));
        }
        
        try {
            await Promise.all(promises);
            this.log('压力测试完成', 'success');
        } catch (error) {
            this.log(`压力测试失败: ${error.message}`, 'error');
        }
    }

    /**
     * 压力测试工作器
     */
    async stressTestWorker(workerId, operations) {
        for (let i = 0; i < operations; i++) {
            if (this.shouldStop) break;
            
            // 模拟各种操作
            if (this.cacheManager) {
                this.cacheManager.set(`stress-${workerId}-${i}`, { data: i });
                this.cacheManager.get(`stress-${workerId}-${i}`);
            }
            
            if (this.patternMatcher) {
                this.patternMatcher.calculateSimilarity(`test${i}`, `test${i+1}`);
            }
            
            // 短暂延迟避免阻塞
            if (i % 10 === 0) {
                await this.sleep(1);
            }
        }
    }

    /**
     * 生成学习测试用例
     */
    generateLearningTestCases(count) {
        const testCases = [];
        const fields = ['customerName', 'phoneNumber', 'address', 'email'];
        const patterns = [
            { original: 'john doe', corrected: 'John Doe', test: 'jane smith', expected: 'Jane Smith' },
            { original: '1234567890', corrected: '+60-12-345-6789', test: '9876543210', expected: '+60-98-765-4321' },
            { original: 'test@email', corrected: '<EMAIL>', test: 'user@domain', expected: '<EMAIL>' }
        ];
        
        for (let i = 0; i < count; i++) {
            const field = fields[i % fields.length];
            const pattern = patterns[i % patterns.length];
            
            testCases.push({
                operation: {
                    type: 'correction',
                    field: field,
                    originalValue: pattern.original + i,
                    correctedValue: pattern.corrected + i,
                    context: { test: true, iteration: i }
                },
                testValue: pattern.test + i,
                expectedValue: pattern.expected + i
            });
        }
        
        return testCases;
    }

    /**
     * 生成总结报告
     */
    generateSummaryReport() {
        const duration = Date.now() - this.startTime;
        
        // 计算综合评分
        const scores = [];
        if (this.testResults.memory) scores.push(this.testResults.memory.efficiency);
        if (this.testResults.speed) scores.push(this.testResults.speed.averageTime < 10 ? 90 : 60);
        if (this.testResults.cache) scores.push(this.testResults.cache.efficiency);
        if (this.testResults.learning) scores.push(this.testResults.learning.accuracy);
        
        const overallScore = scores.length > 0 ? 
            scores.reduce((a, b) => a + b, 0) / scores.length : 0;
        
        // 确定性能等级
        const grade = overallScore >= 80 ? 'A' : 
                     overallScore >= 70 ? 'B' : 
                     overallScore >= 60 ? 'C' : 'D';
        
        // 生成优化建议
        const suggestions = this.generateOptimizationSuggestions();
        
        // 更新显示
        this.updateElement('overall-score', overallScore.toFixed(1));
        this.updateElement('performance-grade', grade);
        this.updateElement('optimization-suggestions', suggestions.length);
        this.updateElement('test-duration', this.formatDuration(duration));
        
        // 显示总结区域
        document.getElementById('test-summary').style.display = 'block';
        
        // 显示详细建议
        const summaryLog = document.getElementById('summary-log');
        summaryLog.innerHTML = '';
        suggestions.forEach(suggestion => {
            this.log(suggestion, 'warning', 'summary');
        });
        
        this.log(`性能测试完成 - 综合评分: ${overallScore.toFixed(1)}/100`, 'success');
    }

    /**
     * 生成优化建议
     */
    generateOptimizationSuggestions() {
        const suggestions = [];
        
        if (this.testResults.memory?.efficiency < 70) {
            suggestions.push('建议优化内存使用：启用数据压缩，清理无用数据');
        }
        
        if (this.testResults.speed?.averageTime > 20) {
            suggestions.push('建议优化响应速度：启用缓存，优化算法');
        }
        
        if (this.testResults.cache?.hitRate < 70) {
            suggestions.push('建议优化缓存策略：增加缓存大小，改进预加载');
        }
        
        if (this.testResults.learning?.accuracy < 70) {
            suggestions.push('建议改进学习算法：增加训练数据，调整参数');
        }
        
        return suggestions;
    }

    /**
     * 辅助方法
     */
    getMemoryUsage() {
        return performance.memory ? performance.memory.usedJSHeapSize : 0;
    }

    formatMemory(bytes) {
        return (bytes / 1024 / 1024).toFixed(1) + 'MB';
    }

    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        return minutes > 0 ? `${minutes}m ${seconds % 60}s` : `${seconds}s`;
    }

    calculateMemoryEfficiency(initial, final, operations) {
        const growth = (final - initial) / operations;
        const efficiency = Math.max(0, 100 - (growth / 1024)); // 每操作增长1KB扣1分
        return Math.min(100, Math.round(efficiency));
    }

    calculateCacheEfficiency(hitRate, responseTime) {
        const hitScore = hitRate;
        const speedScore = Math.max(0, 100 - responseTime * 10);
        return Math.round((hitScore + speedScore) / 2);
    }

    setStatus(testType, status) {
        const indicator = document.getElementById(`${testType}-status`);
        if (indicator) {
            indicator.className = `status-indicator status-${status}`;
        }
    }

    updateProgress(testType, percentage) {
        const progressBar = document.getElementById(`${testType}-progress`);
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
    }

    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }

    log(message, level = 'info', testType = null) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `<div class="log-entry log-${level}">[${timestamp}] ${message}</div>`;
        
        if (testType) {
            const logContainer = document.getElementById(`${testType}-log`);
            if (logContainer) {
                logContainer.innerHTML += logEntry;
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }
        
        console.log(`[${level.toUpperCase()}] ${message}`);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    disableButtons() {
        const buttons = ['run-all-btn', 'memory-test-btn', 'speed-test-btn', 'stress-test-btn'];
        buttons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = true;
        });
    }

    enableButtons() {
        const buttons = ['run-all-btn', 'memory-test-btn', 'speed-test-btn', 'stress-test-btn'];
        buttons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = false;
        });
    }

    stopAllTests() {
        this.shouldStop = true;
        this.log('正在停止所有测试...', 'warning');
    }

    clearResults() {
        this.testResults = {};
        document.getElementById('test-summary').style.display = 'none';
        
        // 清空所有日志
        ['memory', 'speed', 'cache', 'learning'].forEach(testType => {
            const logContainer = document.getElementById(`${testType}-log`);
            if (logContainer) {
                logContainer.innerHTML = `<div class="log-entry log-info">[INFO] ${testType}测试准备就绪</div>`;
            }
            
            // 重置状态
            this.setStatus(testType, '');
            this.updateProgress(testType, 0);
        });
        
        this.log('测试结果已清空', 'info');
    }
}

// 创建全局实例
const performanceTestSuite = new PerformanceTestSuite();

// 导出全局函数
window.runAllPerformanceTests = () => performanceTestSuite.runAllPerformanceTests();
window.runMemoryTest = () => performanceTestSuite.runMemoryTest();
window.runSpeedTest = () => performanceTestSuite.runSpeedTest();
window.runStressTest = () => performanceTestSuite.runStressTest();
window.stopAllTests = () => performanceTestSuite.stopAllTests();
window.clearResults = () => performanceTestSuite.clearResults();

console.log('性能测试套件加载完成', { version: performanceTestSuite.version });
