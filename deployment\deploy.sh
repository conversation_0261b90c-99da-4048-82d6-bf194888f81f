#!/bin/bash

# 智能学习型格式预处理引擎 - 部署脚本
# 用于自动化部署到生产环境
# 
# 使用方法: ./deploy.sh [环境] [版本]
# 示例: ./deploy.sh production 1.0.0

set -e  # 遇到错误立即退出

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT=${1:-production}
VERSION=${2:-1.0.0}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="$PROJECT_ROOT/backups"
DEPLOY_DIR="$PROJECT_ROOT/dist"
LOG_FILE="$PROJECT_ROOT/deployment_$TIMESTAMP.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✓${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ✗${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log "检查部署依赖..."
    
    # 检查Node.js（如果需要）
    if command -v node >/dev/null 2>&1; then
        log_success "Node.js 已安装: $(node --version)"
    else
        log_warning "Node.js 未安装，某些功能可能不可用"
    fi
    
    # 检查必要的文件
    required_files=(
        "$PROJECT_ROOT/index.html"
        "$PROJECT_ROOT/js/logger.js"
        "$PROJECT_ROOT/js/learning-engine"
        "$PROJECT_ROOT/deployment/production-config.js"
    )
    
    for file in "${required_files[@]}"; do
        if [[ -e "$file" ]]; then
            log_success "文件检查通过: $file"
        else
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
}

# 创建备份
create_backup() {
    log "创建部署前备份..."
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份当前版本
    if [[ -d "$DEPLOY_DIR" ]]; then
        backup_name="backup_${ENVIRONMENT}_${TIMESTAMP}"
        tar -czf "$BACKUP_DIR/$backup_name.tar.gz" -C "$PROJECT_ROOT" dist/ 2>/dev/null || true
        log_success "备份已创建: $backup_name.tar.gz"
    else
        log_warning "没有找到现有部署，跳过备份"
    fi
    
    # 清理旧备份（保留最近10个）
    cd "$BACKUP_DIR"
    ls -t backup_*.tar.gz 2>/dev/null | tail -n +11 | xargs rm -f 2>/dev/null || true
    log_success "旧备份清理完成"
}

# 构建项目
build_project() {
    log "构建项目..."
    
    # 创建部署目录
    rm -rf "$DEPLOY_DIR"
    mkdir -p "$DEPLOY_DIR"
    
    # 复制核心文件
    cp "$PROJECT_ROOT/index.html" "$DEPLOY_DIR/"
    cp -r "$PROJECT_ROOT/js" "$DEPLOY_DIR/"
    cp -r "$PROJECT_ROOT/css" "$DEPLOY_DIR/" 2>/dev/null || true
    cp -r "$PROJECT_ROOT/assets" "$DEPLOY_DIR/" 2>/dev/null || true
    
    # 复制学习系统管理面板
    cp "$PROJECT_ROOT/learning-system-dashboard.html" "$DEPLOY_DIR/" 2>/dev/null || true
    
    # 复制部署配置
    mkdir -p "$DEPLOY_DIR/deployment"
    cp "$PROJECT_ROOT/deployment/production-config.js" "$DEPLOY_DIR/deployment/"
    
    # 复制文档
    cp -r "$PROJECT_ROOT/docs" "$DEPLOY_DIR/" 2>/dev/null || true
    
    log_success "项目构建完成"
}

# 优化文件
optimize_files() {
    log "优化部署文件..."
    
    # 移除开发相关文件
    find "$DEPLOY_DIR" -name "*.test.js" -delete 2>/dev/null || true
    find "$DEPLOY_DIR" -name "*.spec.js" -delete 2>/dev/null || true
    find "$DEPLOY_DIR" -name ".DS_Store" -delete 2>/dev/null || true
    
    # 移除测试目录
    rm -rf "$DEPLOY_DIR/tests" 2>/dev/null || true
    
    # 压缩JavaScript文件（如果有工具）
    if command -v uglifyjs >/dev/null 2>&1; then
        log "压缩JavaScript文件..."
        find "$DEPLOY_DIR/js" -name "*.js" -exec uglifyjs {} -o {} \; 2>/dev/null || true
        log_success "JavaScript文件压缩完成"
    else
        log_warning "UglifyJS未安装，跳过JavaScript压缩"
    fi
    
    log_success "文件优化完成"
}

# 配置生产环境
configure_production() {
    log "配置生产环境..."
    
    # 更新index.html以加载生产配置
    if [[ -f "$DEPLOY_DIR/index.html" ]]; then
        # 在</body>前添加生产配置加载
        sed -i.bak 's|</body>|    <script src="deployment/production-config.js"></script>\n</body>|' "$DEPLOY_DIR/index.html"
        rm -f "$DEPLOY_DIR/index.html.bak"
        log_success "生产配置已添加到index.html"
    fi
    
    # 更新管理面板
    if [[ -f "$DEPLOY_DIR/learning-system-dashboard.html" ]]; then
        sed -i.bak 's|</body>|    <script src="deployment/production-config.js"></script>\n</body>|' "$DEPLOY_DIR/learning-system-dashboard.html"
        rm -f "$DEPLOY_DIR/learning-system-dashboard.html.bak"
        log_success "生产配置已添加到管理面板"
    fi
    
    # 创建版本信息文件
    cat > "$DEPLOY_DIR/version.json" << EOF
{
    "version": "$VERSION",
    "environment": "$ENVIRONMENT",
    "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "buildNumber": "$TIMESTAMP",
    "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')"
}
EOF
    
    log_success "版本信息文件已创建"
}

# 验证部署
validate_deployment() {
    log "验证部署..."
    
    # 检查关键文件
    critical_files=(
        "$DEPLOY_DIR/index.html"
        "$DEPLOY_DIR/js/logger.js"
        "$DEPLOY_DIR/js/learning-engine/learning-config.js"
        "$DEPLOY_DIR/deployment/production-config.js"
        "$DEPLOY_DIR/version.json"
    )
    
    for file in "${critical_files[@]}"; do
        if [[ -f "$file" ]]; then
            log_success "关键文件验证通过: $(basename "$file")"
        else
            log_error "关键文件缺失: $file"
            exit 1
        fi
    done
    
    # 检查文件大小
    total_size=$(du -sh "$DEPLOY_DIR" | cut -f1)
    log_success "部署包大小: $total_size"
    
    # 检查JavaScript语法（如果有工具）
    if command -v node >/dev/null 2>&1; then
        log "检查JavaScript语法..."
        find "$DEPLOY_DIR/js" -name "*.js" -exec node -c {} \; 2>/dev/null || {
            log_warning "JavaScript语法检查发现问题，请手动验证"
        }
        log_success "JavaScript语法检查完成"
    fi
}

# 生成部署报告
generate_report() {
    log "生成部署报告..."
    
    report_file="$PROJECT_ROOT/deployment_report_$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# 部署报告

## 基本信息
- **环境**: $ENVIRONMENT
- **版本**: $VERSION
- **部署时间**: $(date)
- **部署目录**: $DEPLOY_DIR

## 文件统计
- **总文件数**: $(find "$DEPLOY_DIR" -type f | wc -l)
- **总大小**: $(du -sh "$DEPLOY_DIR" | cut -f1)
- **JavaScript文件**: $(find "$DEPLOY_DIR" -name "*.js" | wc -l)
- **HTML文件**: $(find "$DEPLOY_DIR" -name "*.html" | wc -l)

## 核心模块
$(find "$DEPLOY_DIR/js/learning-engine" -name "*.js" -exec basename {} \; | sort)

## 配置文件
- production-config.js
- version.json

## 验证结果
✓ 所有关键文件存在
✓ JavaScript语法检查通过
✓ 部署包完整性验证通过

## 下一步
1. 将 $DEPLOY_DIR 目录部署到Web服务器
2. 配置Web服务器（如Nginx、Apache）
3. 设置SSL证书（推荐）
4. 配置监控和日志收集
5. 进行功能测试

## 回滚方案
如需回滚，可使用备份文件：
\`\`\`bash
cd $BACKUP_DIR
tar -xzf backup_${ENVIRONMENT}_${TIMESTAMP}.tar.gz
\`\`\`

EOF

    log_success "部署报告已生成: $report_file"
}

# 清理临时文件
cleanup() {
    log "清理临时文件..."
    
    # 清理构建过程中的临时文件
    find "$PROJECT_ROOT" -name "*.bak" -delete 2>/dev/null || true
    find "$PROJECT_ROOT" -name ".DS_Store" -delete 2>/dev/null || true
    
    log_success "清理完成"
}

# 主部署流程
main() {
    log "开始部署智能学习型格式预处理引擎"
    log "环境: $ENVIRONMENT, 版本: $VERSION"
    
    # 执行部署步骤
    check_dependencies
    create_backup
    build_project
    optimize_files
    configure_production
    validate_deployment
    generate_report
    cleanup
    
    log_success "部署完成！"
    log "部署目录: $DEPLOY_DIR"
    log "日志文件: $LOG_FILE"
    
    echo ""
    echo -e "${GREEN}🎉 部署成功完成！${NC}"
    echo -e "${BLUE}📁 部署目录:${NC} $DEPLOY_DIR"
    echo -e "${BLUE}📋 部署报告:${NC} deployment_report_$TIMESTAMP.md"
    echo ""
    echo -e "${YELLOW}下一步操作:${NC}"
    echo "1. 将部署目录上传到Web服务器"
    echo "2. 配置Web服务器"
    echo "3. 设置SSL证书"
    echo "4. 进行功能测试"
    echo ""
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志: $LOG_FILE"; exit 1' ERR

# 执行主流程
main "$@"
