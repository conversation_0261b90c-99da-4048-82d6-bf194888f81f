/**
 * 中央状态管理器
 * 统一管理系统中所有状态，提供统一的状态更新机制
 * 解决分散状态管理导致的数据不一致问题
 * 
 * 功能特性:
 * - 统一状态存储和访问
 * - 状态变更通知机制
 * - 状态持久化管理
 * - 状态验证和回滚
 * - 调试和监控支持
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 中央状态管理器类
     */
    class CentralStateManager {
        constructor() {
            this.stores = new Map(); // 存储不同域的状态
            this.subscribers = new Map(); // 状态变更订阅者
            this.middleware = []; // 中间件列表
            this.history = []; // 状态变更历史
            this.maxHistorySize = 100; // 最大历史记录数
            this.logger = null; // 延迟获取logger
            this.initialized = false;
        }

        /**
         * 初始化中央状态管理器
         */
        init() {
            if (this.initialized) {
                console.warn('中央状态管理器已经初始化');
                return;
            }

            // 延迟获取logger
            this.logger = window.getService ? window.getService('logger') : null;

            // 初始化状态域
            this.initializeStateDomains();

            // 设置中间件
            this.setupMiddleware();

            // 迁移现有状态
            this.migrateExistingState();

            this.initialized = true;
            this.log('中央状态管理器初始化完成', 'success');
        }

        /**
         * 初始化状态域
         */
        initializeStateDomains() {
            // 认证状态域
            this.registerDomain('auth', {
                isLoggedIn: false,
                token: null,
                user: null,
                tokenExpiry: null,
                rememberMe: false
            });

            // 系统状态域
            this.registerDomain('system', {
                connected: false,
                lastApiCall: null,
                apiCallCount: 0,
                errors: [],
                performance: {
                    startTime: Date.now(),
                    lastUpdateTime: null,
                    memoryUsage: 0
                }
            });

            // 订单状态域
            this.registerDomain('order', {
                current: {
                    rawInput: '',
                    parsedData: {},
                    formData: {},
                    validationErrors: [],
                    status: 'draft'
                },
                history: [],
                multiOrder: {
                    isActive: false,
                    orders: [],
                    currentIndex: 0
                }
            });

            // UI状态域
            this.registerDomain('ui', {
                theme: 'light',
                language: 'en',
                debugMode: false,
                currentModal: null,
                panelStates: {},
                notifications: []
            });

            // 配置状态域
            this.registerDomain('config', {
                autoSave: true,
                defaultBackendUserId: null,
                geminiApiKey: null,
                apiEndpoint: null,
                preferences: {}
            });

            // 数据缓存状态域
            this.registerDomain('cache', {
                backendUsers: [],
                subCategories: [],
                carTypes: [],
                drivingRegions: [],
                languages: [],
                lastUpdated: null
            });
        }

        /**
         * 注册状态域
         * @param {string} domain - 状态域名称
         * @param {Object} initialState - 初始状态
         */
        registerDomain(domain, initialState) {
            if (this.stores.has(domain)) {
                this.log(`警告: 状态域 ${domain} 已存在，将被覆盖`, 'warning');
            }

            this.stores.set(domain, {
                state: { ...initialState },
                initialState: { ...initialState },
                lastModified: Date.now(),
                version: 1
            });

            this.subscribers.set(domain, new Map());
            this.log(`已注册状态域: ${domain}`, 'info');
        }

        /**
         * 获取状态
         * @param {string} domain - 状态域
         * @param {string} path - 状态路径，如 'user.name'
         * @returns {any} 状态值
         */
        getState(domain, path = null) {
            const store = this.stores.get(domain);
            if (!store) {
                this.log(`状态域 ${domain} 不存在`, 'error');
                return null;
            }

            if (!path) {
                return store.state;
            }

            return path.split('.').reduce((obj, key) => obj?.[key], store.state);
        }

        /**
         * 设置状态
         * @param {string} domain - 状态域
         * @param {string} path - 状态路径
         * @param {any} value - 新值
         * @param {Object} options - 选项
         */
        setState(domain, path, value, options = {}) {
            const store = this.stores.get(domain);
            if (!store) {
                this.log(`状态域 ${domain} 不存在`, 'error');
                return false;
            }

            const oldValue = this.getState(domain, path);
            
            // 执行中间件
            const middlewareResult = this.executeMiddleware('beforeUpdate', {
                domain,
                path,
                oldValue,
                newValue: value,
                options
            });

            if (middlewareResult === false) {
                this.log(`状态更新被中间件阻止: ${domain}.${path}`, 'warning');
                return false;
            }

            // 更新状态
            const keys = path.split('.');
            const lastKey = keys.pop();
            const target = keys.reduce((obj, key) => {
                if (!obj[key]) obj[key] = {};
                return obj[key];
            }, store.state);

            target[lastKey] = value;
            store.lastModified = Date.now();
            store.version++;

            // 记录历史
            this.recordHistory(domain, path, oldValue, value);

            // 通知订阅者
            this.notifySubscribers(domain, path, value, oldValue);

            // 执行后置中间件
            this.executeMiddleware('afterUpdate', {
                domain,
                path,
                oldValue,
                newValue: value,
                options
            });

            // 持久化（如果需要）
            if (options.persist !== false) {
                this.persistState(domain);
            }

            this.log(`状态已更新: ${domain}.${path}`, 'info');
            return true;
        }

        /**
         * 订阅状态变更
         * @param {string} domain - 状态域
         * @param {string} path - 状态路径
         * @param {Function} callback - 回调函数
         * @returns {Function} 取消订阅函数
         */
        subscribe(domain, path, callback) {
            const domainSubscribers = this.subscribers.get(domain);
            if (!domainSubscribers) {
                this.log(`状态域 ${domain} 不存在`, 'error');
                return () => {};
            }

            if (!domainSubscribers.has(path)) {
                domainSubscribers.set(path, new Set());
            }

            domainSubscribers.get(path).add(callback);

            // 返回取消订阅函数
            return () => {
                const pathSubscribers = domainSubscribers.get(path);
                if (pathSubscribers) {
                    pathSubscribers.delete(callback);
                }
            };
        }

        /**
         * 通知订阅者
         * @param {string} domain - 状态域
         * @param {string} path - 状态路径
         * @param {any} newValue - 新值
         * @param {any} oldValue - 旧值
         */
        notifySubscribers(domain, path, newValue, oldValue) {
            const domainSubscribers = this.subscribers.get(domain);
            if (!domainSubscribers) return;

            // 通知精确路径的订阅者
            const pathSubscribers = domainSubscribers.get(path);
            if (pathSubscribers) {
                pathSubscribers.forEach(callback => {
                    try {
                        callback(newValue, oldValue, path);
                    } catch (error) {
                        this.log(`订阅者回调执行失败: ${domain}.${path}`, 'error');
                        console.error(error);
                    }
                });
            }

            // 通知通配符订阅者
            const wildcardSubscribers = domainSubscribers.get('*');
            if (wildcardSubscribers) {
                wildcardSubscribers.forEach(callback => {
                    try {
                        callback(newValue, oldValue, path);
                    } catch (error) {
                        this.log(`通配符订阅者回调执行失败: ${domain}.*`, 'error');
                        console.error(error);
                    }
                });
            }
        }

        /**
         * 设置中间件
         */
        setupMiddleware() {
            // 状态验证中间件
            this.addMiddleware('beforeUpdate', (context) => {
                const { domain, path, newValue } = context;
                
                // 基本类型检查
                if (domain === 'auth' && path === 'isLoggedIn' && typeof newValue !== 'boolean') {
                    this.log(`状态验证失败: ${domain}.${path} 必须是布尔值`, 'error');
                    return false;
                }

                return true;
            });

            // 性能监控中间件
            this.addMiddleware('afterUpdate', (context) => {
                const { domain, path } = context;

                // 避免无限递归 - 不在性能更新时再次触发性能更新
                if (domain === 'system' && path.startsWith('performance.')) {
                    return;
                }

                const store = this.stores.get(domain);
                if (store) {
                    // 直接更新性能指标，不触发中间件
                    const systemStore = this.stores.get('system');
                    if (systemStore) {
                        systemStore.state.performance.lastUpdateTime = Date.now();
                        systemStore.lastModified = Date.now();
                        systemStore.version++;
                    }
                }
            });
        }

        /**
         * 添加中间件
         * @param {string} type - 中间件类型 ('beforeUpdate' | 'afterUpdate')
         * @param {Function} middleware - 中间件函数
         */
        addMiddleware(type, middleware) {
            if (!this.middleware[type]) {
                this.middleware[type] = [];
            }
            this.middleware[type].push(middleware);
        }

        /**
         * 执行中间件
         * @param {string} type - 中间件类型
         * @param {Object} context - 上下文
         * @returns {boolean} 是否继续执行
         */
        executeMiddleware(type, context) {
            const middlewares = this.middleware[type] || [];
            
            for (const middleware of middlewares) {
                try {
                    const result = middleware(context);
                    if (result === false) {
                        return false;
                    }
                } catch (error) {
                    this.log(`中间件执行失败: ${type}`, 'error');
                    console.error(error);
                }
            }
            
            return true;
        }

        /**
         * 记录状态变更历史
         * @param {string} domain - 状态域
         * @param {string} path - 状态路径
         * @param {any} oldValue - 旧值
         * @param {any} newValue - 新值
         */
        recordHistory(domain, path, oldValue, newValue) {
            const historyEntry = {
                timestamp: Date.now(),
                domain,
                path,
                oldValue,
                newValue,
                id: `${domain}.${path}.${Date.now()}`
            };

            this.history.push(historyEntry);

            // 限制历史记录大小
            if (this.history.length > this.maxHistorySize) {
                this.history.shift();
            }
        }

        /**
         * 迁移现有状态
         */
        migrateExistingState() {
            // 从现有的AppState迁移数据
            if (window.OTA && window.OTA.appState) {
                const appState = window.OTA.appState;
                
                try {
                    // 迁移认证状态
                    const authState = appState.get('auth');
                    if (authState) {
                        this.setState('auth', '', authState, { persist: false });
                    }

                    // 迁移系统数据
                    const systemData = appState.get('systemData');
                    if (systemData) {
                        this.setState('cache', '', systemData, { persist: false });
                    }

                    // 迁移配置
                    const config = appState.get('config');
                    if (config) {
                        Object.entries(config).forEach(([key, value]) => {
                            if (key === 'theme' || key === 'language' || key === 'debugMode') {
                                this.setState('ui', key, value, { persist: false });
                            } else {
                                this.setState('config', key, value, { persist: false });
                            }
                        });
                    }

                    this.log('现有状态迁移完成', 'success');
                } catch (error) {
                    this.log('状态迁移失败', 'error');
                    console.error(error);
                }
            }
        }

        /**
         * 持久化状态
         * @param {string} domain - 状态域
         */
        persistState(domain) {
            try {
                const store = this.stores.get(domain);
                if (store) {
                    const key = `ota-central-state-${domain}`;
                    localStorage.setItem(key, JSON.stringify({
                        state: store.state,
                        version: store.version,
                        lastModified: store.lastModified
                    }));
                }
            } catch (error) {
                this.log(`状态持久化失败: ${domain}`, 'error');
                console.error(error);
            }
        }

        /**
         * 从存储恢复状态
         * @param {string} domain - 状态域
         */
        restoreState(domain) {
            try {
                const key = `ota-central-state-${domain}`;
                const stored = localStorage.getItem(key);
                
                if (stored) {
                    const data = JSON.parse(stored);
                    const store = this.stores.get(domain);
                    
                    if (store) {
                        store.state = data.state;
                        store.version = data.version;
                        store.lastModified = data.lastModified;
                        this.log(`状态恢复成功: ${domain}`, 'info');
                    }
                }
            } catch (error) {
                this.log(`状态恢复失败: ${domain}`, 'error');
                console.error(error);
            }
        }

        /**
         * 获取状态管理器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                domains: Array.from(this.stores.keys()),
                totalSubscribers: Array.from(this.subscribers.values())
                    .reduce((total, domainSubs) => total + domainSubs.size, 0),
                historySize: this.history.length,
                middleware: Object.keys(this.middleware).reduce((acc, type) => {
                    acc[type] = this.middleware[type].length;
                    return acc;
                }, {}),
                initialized: this.initialized
            };
        }

        /**
         * 日志记录
         * @param {string} message - 消息
         * @param {string} level - 日志级别
         */
        log(message, level = 'info') {
            const prefix = '[CentralStateManager]';
            if (this.logger && this.logger.log) {
                this.logger.log(`${prefix} ${message}`, level);
            } else {
                console.log(`${prefix} ${message}`);
            }
        }

        /**
         * 重置状态域
         * @param {string} domain - 状态域
         */
        resetDomain(domain) {
            const store = this.stores.get(domain);
            if (store) {
                store.state = { ...store.initialState };
                store.version = 1;
                store.lastModified = Date.now();
                this.persistState(domain);
                this.log(`状态域已重置: ${domain}`, 'info');
            }
        }

        /**
         * 销毁状态管理器
         */
        destroy() {
            this.stores.clear();
            this.subscribers.clear();
            this.middleware = [];
            this.history = [];
            this.initialized = false;
            this.log('中央状态管理器已销毁', 'info');
        }
    }

    // 创建全局实例
    const centralStateManager = new CentralStateManager();

    // 导出到OTA命名空间
    window.OTA.centralStateManager = centralStateManager;

    // 提供便捷的全局访问函数
    window.getCentralStateManager = function() {
        return window.OTA.centralStateManager;
    };

    // 提供兼容性API，逐步替换现有的状态访问方式
    window.OTA.state = {
        get: (path) => {
            const [domain, ...pathParts] = path.split('.');
            return centralStateManager.getState(domain, pathParts.join('.'));
        },
        set: (path, value, options) => {
            const [domain, ...pathParts] = path.split('.');
            return centralStateManager.setState(domain, pathParts.join('.'), value, options);
        },
        subscribe: (path, callback) => {
            const [domain, ...pathParts] = path.split('.');
            return centralStateManager.subscribe(domain, pathParts.join('.'), callback);
        }
    };

    console.log('✅ 中央状态管理器已加载');

})();
