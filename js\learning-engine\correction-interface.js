/**
 * 智能学习型格式预处理引擎 - 手动更正记录接口
 * 负责处理用户手动更正操作，验证更正数据，支持批量处理和撤销功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return window.OTA.learningConfig || window.learningConfig;
    }

    function getUserOperationLearner() {
        return window.OTA.userOperationLearner || window.userOperationLearner;
    }

    function getErrorClassificationSystem() {
        return window.OTA.errorClassificationSystem || window.errorClassificationSystem;
    }

    function getPatternMatchingEngine() {
        return window.OTA.patternMatchingEngine || window.patternMatchingEngine;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    /**
     * 手动更正记录接口类
     * 处理用户的手动更正操作
     */
    class CorrectionInterface {
        constructor() {
            this.config = getLearningConfig();
            this.operationLearner = getUserOperationLearner();
            this.errorClassifier = getErrorClassificationSystem();
            this.patternMatcher = getPatternMatchingEngine();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            
            // 更正历史记录
            this.correctionHistory = [];
            this.maxHistorySize = 100;
            
            // 批量操作状态
            this.batchOperations = new Map();
            
            // 撤销栈
            this.undoStack = [];
            this.maxUndoSize = 20;
            
            this.initialize();
        }

        /**
         * 初始化更正接口
         */
        initialize() {
            try {
                this.logger?.log('手动更正记录接口初始化完成', 'info', {
                    version: this.version,
                    maxHistorySize: this.maxHistorySize,
                    maxUndoSize: this.maxUndoSize
                });
            } catch (error) {
                this.logger?.logError('手动更正记录接口初始化失败', error);
            }
        }

        /**
         * 处理用户更正
         * @param {Object} correctionData - 更正数据
         * @returns {Object} 处理结果
         */
        handleUserCorrection(correctionData) {
            try {
                // 验证更正数据
                const validationResult = this.validateCorrectionData(correctionData);
                if (!validationResult.isValid) {
                    return this.createErrorResult('VALIDATION_ERROR', validationResult.errors);
                }

                // 标准化更正数据
                const normalizedData = this.normalizeCorrectionData(correctionData);

                // 分析更正
                const analysisResult = this.analyzeCorrectionData(normalizedData);

                // 记录操作
                const operationId = this.recordCorrectionOperation(normalizedData, analysisResult);

                // 添加到历史记录
                this.addToHistory(normalizedData, analysisResult, operationId);

                // 添加到撤销栈
                this.addToUndoStack(normalizedData, operationId);

                // 触发学习分析
                this.triggerLearningAnalysis(normalizedData, analysisResult);

                return this.createSuccessResult(operationId, analysisResult);

            } catch (error) {
                this.logger?.logError('处理用户更正失败', error);
                return this.createErrorResult('PROCESSING_ERROR', [error.message]);
            }
        }

        /**
         * 验证更正数据
         * @param {Object} data - 更正数据
         * @returns {Object} 验证结果
         */
        validateCorrectionData(data) {
            const errors = [];
            
            // 必需字段检查
            const requiredFields = ['field', 'originalValue', 'correctedValue'];
            requiredFields.forEach(field => {
                if (!data.hasOwnProperty(field) || data[field] === undefined) {
                    errors.push(`缺少必需字段: ${field}`);
                }
            });

            // 字段值检查
            if (data.field && typeof data.field !== 'string') {
                errors.push('字段名必须是字符串');
            }

            if (data.originalValue !== undefined && data.correctedValue !== undefined) {
                if (data.originalValue === data.correctedValue) {
                    errors.push('原始值和更正值不能相同');
                }
            }

            // 置信度检查
            if (data.confidence !== undefined) {
                if (typeof data.confidence !== 'number' || data.confidence < 0 || data.confidence > 1) {
                    errors.push('置信度必须是0-1之间的数字');
                }
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        /**
         * 标准化更正数据
         * @param {Object} data - 原始更正数据
         * @returns {Object} 标准化后的数据
         */
        normalizeCorrectionData(data) {
            return {
                id: this.generateCorrectionId(),
                timestamp: new Date().toISOString(),
                field: data.field.trim(),
                originalValue: this.sanitizeValue(data.originalValue),
                correctedValue: this.sanitizeValue(data.correctedValue),
                confidence: data.confidence || 0.8,
                context: data.context || {},
                metadata: {
                    source: 'manual_correction',
                    userAgent: navigator.userAgent,
                    sessionId: this.operationLearner?.sessionId,
                    ...data.metadata
                }
            };
        }

        /**
         * 清理值
         */
        sanitizeValue(value) {
            if (value === null || value === undefined) {
                return '';
            }
            return String(value).trim();
        }

        /**
         * 生成更正ID
         */
        generateCorrectionId() {
            return `correction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 分析更正数据
         * @param {Object} data - 标准化的更正数据
         * @returns {Object} 分析结果
         */
        analyzeCorrectionData(data) {
            const analysis = {
                errorClassification: null,
                patternAnalysis: null,
                similarityScore: 0,
                suggestions: []
            };

            try {
                // 错误分类
                analysis.errorClassification = this.errorClassifier.classifyError({
                    field: data.field,
                    originalValue: data.originalValue,
                    correctedValue: data.correctedValue,
                    context: data.context
                });

                // 模式分析
                analysis.patternAnalysis = this.analyzePatterns(data);

                // 相似度计算
                analysis.similarityScore = this.patternMatcher.calculateSimilarity(
                    data.originalValue,
                    data.correctedValue
                );

                // 生成建议
                analysis.suggestions = this.generateCorrectionSuggestions(data, analysis);

            } catch (error) {
                this.logger?.logError('更正数据分析失败', error);
            }

            return analysis;
        }

        /**
         * 分析模式
         */
        analyzePatterns(data) {
            try {
                const originalPatterns = this.patternMatcher.contextMatch(
                    data.originalValue,
                    { fieldType: this.getFieldType(data.field) }
                );

                const correctedPatterns = this.patternMatcher.contextMatch(
                    data.correctedValue,
                    { fieldType: this.getFieldType(data.field) }
                );

                return {
                    original: originalPatterns,
                    corrected: correctedPatterns,
                    patternChange: this.analyzePatternChange(originalPatterns, correctedPatterns)
                };

            } catch (error) {
                this.logger?.logError('模式分析失败', error);
                return null;
            }
        }

        /**
         * 获取字段类型
         */
        getFieldType(fieldName) {
            const fieldTypes = window.OTA.FieldTypes || {};
            return fieldTypes[fieldName]?.type || 'unknown';
        }

        /**
         * 分析模式变化
         */
        analyzePatternChange(originalPatterns, correctedPatterns) {
            return {
                hasPatternChange: JSON.stringify(originalPatterns) !== JSON.stringify(correctedPatterns),
                originalScore: originalPatterns.contextScore,
                correctedScore: correctedPatterns.contextScore,
                improvement: correctedPatterns.contextScore - originalPatterns.contextScore
            };
        }

        /**
         * 生成更正建议
         */
        generateCorrectionSuggestions(data, analysis) {
            const suggestions = [];

            // 基于错误分类的建议
            if (analysis.errorClassification?.suggestions) {
                suggestions.push(...analysis.errorClassification.suggestions);
            }

            // 基于相似度的建议
            if (analysis.similarityScore < 0.3) {
                suggestions.push('原始值和更正值差异较大，请确认更正是否正确');
            }

            // 基于模式分析的建议
            if (analysis.patternAnalysis?.patternChange?.improvement < 0) {
                suggestions.push('更正后的模式匹配度降低，建议重新检查');
            }

            return suggestions;
        }

        /**
         * 记录更正操作
         */
        recordCorrectionOperation(data, analysis) {
            try {
                const operationData = {
                    type: 'correction',
                    field: data.field,
                    originalValue: data.originalValue,
                    correctedValue: data.correctedValue,
                    confidence: data.confidence,
                    context: {
                        ...data.context,
                        errorType: analysis.errorClassification?.errorType,
                        similarityScore: analysis.similarityScore
                    },
                    metadata: data.metadata
                };

                return this.operationLearner.recordOperation(operationData);

            } catch (error) {
                this.logger?.logError('记录更正操作失败', error);
                return null;
            }
        }

        /**
         * 添加到历史记录
         */
        addToHistory(data, analysis, operationId) {
            const historyEntry = {
                id: data.id,
                operationId: operationId,
                timestamp: data.timestamp,
                field: data.field,
                originalValue: data.originalValue,
                correctedValue: data.correctedValue,
                confidence: data.confidence,
                analysis: analysis
            };

            this.correctionHistory.unshift(historyEntry);

            // 限制历史记录大小
            if (this.correctionHistory.length > this.maxHistorySize) {
                this.correctionHistory = this.correctionHistory.slice(0, this.maxHistorySize);
            }
        }

        /**
         * 添加到撤销栈
         */
        addToUndoStack(data, operationId) {
            const undoEntry = {
                correctionId: data.id,
                operationId: operationId,
                timestamp: data.timestamp,
                data: data
            };

            this.undoStack.push(undoEntry);

            // 限制撤销栈大小
            if (this.undoStack.length > this.maxUndoSize) {
                this.undoStack.shift();
            }
        }

        /**
         * 触发学习分析
         */
        triggerLearningAnalysis(data, analysis) {
            // 这里将在后续任务中实现学习规则生成
            this.logger?.log('触发学习分析', 'info', {
                field: data.field,
                errorType: analysis.errorClassification?.errorType,
                confidence: analysis.errorClassification?.confidence
            });
        }

        /**
         * 批量处理更正
         * @param {Array} corrections - 更正数组
         * @returns {Object} 批量处理结果
         */
        handleBatchCorrections(corrections) {
            try {
                const batchId = this.generateBatchId();
                const results = {
                    batchId: batchId,
                    total: corrections.length,
                    successful: 0,
                    failed: 0,
                    results: [],
                    errors: []
                };

                this.batchOperations.set(batchId, {
                    startTime: new Date().toISOString(),
                    total: corrections.length,
                    processed: 0
                });

                corrections.forEach((correction, index) => {
                    try {
                        const result = this.handleUserCorrection(correction);
                        results.results.push(result);
                        
                        if (result.success) {
                            results.successful++;
                        } else {
                            results.failed++;
                            results.errors.push(`项目 ${index + 1}: ${result.errors.join(', ')}`);
                        }

                    } catch (error) {
                        results.failed++;
                        results.errors.push(`项目 ${index + 1}: ${error.message}`);
                    }

                    // 更新批量操作状态
                    const batchStatus = this.batchOperations.get(batchId);
                    if (batchStatus) {
                        batchStatus.processed++;
                    }
                });

                // 完成批量操作
                this.batchOperations.delete(batchId);

                return results;

            } catch (error) {
                this.logger?.logError('批量处理更正失败', error);
                return {
                    batchId: null,
                    total: corrections.length,
                    successful: 0,
                    failed: corrections.length,
                    results: [],
                    errors: [error.message]
                };
            }
        }

        /**
         * 生成批量ID
         */
        generateBatchId() {
            return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 撤销更正
         * @param {string} correctionId - 更正ID
         * @returns {Object} 撤销结果
         */
        undoCorrection(correctionId) {
            try {
                const undoEntry = this.undoStack.find(entry => entry.correctionId === correctionId);
                
                if (!undoEntry) {
                    return this.createErrorResult('UNDO_ERROR', ['未找到可撤销的更正记录']);
                }

                // 从历史记录中移除
                this.correctionHistory = this.correctionHistory.filter(
                    entry => entry.id !== correctionId
                );

                // 从撤销栈中移除
                this.undoStack = this.undoStack.filter(
                    entry => entry.correctionId !== correctionId
                );

                this.logger?.log('更正已撤销', 'info', { correctionId: correctionId });

                return {
                    success: true,
                    correctionId: correctionId,
                    message: '更正已成功撤销'
                };

            } catch (error) {
                this.logger?.logError('撤销更正失败', error);
                return this.createErrorResult('UNDO_ERROR', [error.message]);
            }
        }

        /**
         * 获取更正历史
         * @param {Object} filters - 过滤条件
         * @returns {Array} 历史记录
         */
        getCorrectionHistory(filters = {}) {
            let history = [...this.correctionHistory];

            // 应用过滤器
            if (filters.field) {
                history = history.filter(entry => entry.field === filters.field);
            }

            if (filters.dateFrom) {
                const fromDate = new Date(filters.dateFrom);
                history = history.filter(entry => new Date(entry.timestamp) >= fromDate);
            }

            if (filters.dateTo) {
                const toDate = new Date(filters.dateTo);
                history = history.filter(entry => new Date(entry.timestamp) <= toDate);
            }

            if (filters.limit) {
                history = history.slice(0, filters.limit);
            }

            return history;
        }

        /**
         * 获取批量操作状态
         * @param {string} batchId - 批量ID
         * @returns {Object} 批量状态
         */
        getBatchStatus(batchId) {
            return this.batchOperations.get(batchId) || null;
        }

        /**
         * 创建成功结果
         */
        createSuccessResult(operationId, analysis) {
            return {
                success: true,
                operationId: operationId,
                analysis: analysis,
                timestamp: new Date().toISOString()
            };
        }

        /**
         * 创建错误结果
         */
        createErrorResult(errorType, errors) {
            return {
                success: false,
                errorType: errorType,
                errors: errors,
                timestamp: new Date().toISOString()
            };
        }

        /**
         * 获取统计信息
         */
        getStats() {
            return {
                totalCorrections: this.correctionHistory.length,
                undoStackSize: this.undoStack.length,
                activeBatches: this.batchOperations.size,
                lastCorrection: this.correctionHistory[0]?.timestamp || null
            };
        }

        /**
         * 清理历史记录
         */
        clearHistory() {
            this.correctionHistory = [];
            this.undoStack = [];
            this.logger?.log('更正历史记录已清理', 'info');
        }
    }

    // 创建全局实例
    const correctionInterface = new CorrectionInterface();

    // 导出到全局命名空间
    window.OTA.correctionInterface = correctionInterface;
    window.correctionInterface = correctionInterface; // 向后兼容

    // 工厂函数
    window.getCorrectionInterface = function() {
        return window.OTA.correctionInterface || window.correctionInterface;
    };

    console.log('手动更正记录接口加载完成', {
        version: correctionInterface.version,
        maxHistorySize: correctionInterface.maxHistorySize,
        maxUndoSize: correctionInterface.maxUndoSize
    });

})();
